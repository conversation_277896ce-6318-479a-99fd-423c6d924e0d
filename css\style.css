/********** Template CSS **********/
:root {
    --primary-dark: #212529;
    --primary-blue: #34495e;
    --accent-orange: #fd7e14;
    --accent-light: #f8f9fa;
    --accent-red: #C5172E;
    --text-on-dark: #ffffff;
    --text-on-light: #212529;
    --text-secondary-on-light: #6c757d;
    --main-gradient: linear-gradient(135deg, var(--primary-dark), var(--primary-blue));
}

h1,
h2,
.h1,
.h2,
.fw-bold {
    font-weight: 600 !important;
}

h3,
h4,
.h3,
.h4,
.fw-medium {
    font-weight: 500 !important;
}

h5,
h6,
.h5,
.h6,
.fw-normal {
    font-weight: 400 !important;
}

.back-to-top {
    position: fixed;
    display: none;
    right: 30px;
    bottom: 30px;
    z-index: 99;
}


/*** Spinner ***/
#spinner {
    opacity: 0;
    visibility: hidden;
    transition: opacity .5s ease-out, visibility 0s linear .5s;
    z-index: 99999;
}

#spinner.show {
    transition: opacity .5s ease-out, visibility 0s linear 0s;
    visibility: visible;
    opacity: 1;
}


/*** Button ***/
.btn {
    font-weight: 500;
    transition: .5s;
}

.btn.btn-primary,
.btn.btn-secondary {
    color: var(--text-on-dark);
}

.btn-square {
    width: 38px;
    height: 38px;
}

.btn-sm-square {
    width: 32px;
    height: 32px;
}

.btn-lg-square {
    width: 48px;
    height: 48px;
}

.btn-square,
.btn-sm-square,
.btn-lg-square {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
}

.btn-outline-body {
    color: var(--accent-orange);
    border-color: var(--primary-dark);
}

.btn-outline-body:hover {
    color: var(--text-on-dark);
    background: var(--accent-orange);
    border-color: var(--accent-orange);
}


/*** Navbar ***/
.navbar .dropdown-toggle::after {
    border: none;
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    vertical-align: middle;
    margin-left: 8px;
}

.navbar .navbar-nav .nav-link {
    margin-right: 30px;
    padding: 30px 0;
    color: var(--text-on-light);
    font-weight: 500;
    text-transform: uppercase;
    outline: none;
}

.navbar .navbar-nav .nav-link:hover,
.navbar .navbar-nav .nav-link.active {
    color: var(--accent-orange);
}

.navbar.sticky-top {
    top: -100px;
    transition: .5s;
}

@media (max-width: 991.98px) {
    .navbar .navbar-nav .nav-link {
        margin-right: 0;
        padding: 10px 0;
    }

    .navbar .navbar-nav {
        border-top: 1px solid var(--accent-light);
    }
}

@media (min-width: 992px) {
    .navbar .nav-item .dropdown-menu {
        display: block;
        visibility: hidden;
        top: 100%;
        transform: rotateX(-75deg);
        transform-origin: 0% 0%;
        transition: .5s;
        opacity: 0;
    }

    .navbar .nav-item:hover .dropdown-menu {
        transform: rotateX(0deg);
        visibility: visible;
        transition: .5s;
        opacity: 1;
    }
}


/*** Header ***/
.owl-carousel-inner {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    background: rgba(33, 37, 41, .5);
}

@media (max-width: 768px) {
    .header-carousel .owl-carousel-item {
        position: relative;
        min-height: 500px;
    }

    .header-carousel .owl-carousel-item img {
        position: absolute;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .header-carousel .owl-carousel-item p {
        font-size: 16px !important;
    }
}

.header-carousel .owl-dots {
    position: absolute;
    width: 60px;
    height: 100%;
    top: 0;
    right: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.header-carousel .owl-dots .owl-dot {
    position: relative;
    width: 45px;
    height: 45px;
    margin: 5px 0;
    background: var(--primary-dark);
    transition: .5s;
}

.header-carousel .owl-dots .owl-dot.active {
    width: 60px;
    height: 60px;
}

.header-carousel .owl-dots .owl-dot img {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
    padding: 2px;
    transition: .5s;
    opacity: .3;
}

.header-carousel .owl-dots .owl-dot.active img {
    opacity: 1;
}

.page-header {
    background: linear-gradient(rgba(33, 37, 41, .5), rgba(33, 37, 41, .5)), url(../img/carousel-1.jpg) center center no-repeat;
    background-size: cover;
}

.breadcrumb-item+.breadcrumb-item::before {
    color: var(--accent-light);
}


/*** Section Title ***/
.section-title {
    color: var(--accent-orange);
    font-weight: 600;
    letter-spacing: 5px;
    text-transform: uppercase;
}


/*** Facts ***/
.fact-item .fact-icon {
    background: var(--accent-light);
    color: var(--accent-orange);
    font-size: 50px;
    width: 90px;
    height: 90px;
    margin-top: -45px;
    margin-bottom: 1.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 120px;
    transition: .5s;
}

.fact-item:hover .fact-icon {
    background: var(--primary-dark);
}

.fact-item .fact-icon i {
    color: var(--accent-orange);
    transition: .5;
}

.fact-item:hover .fact-icon i {
    color: var(--text-on-dark);
}


/*** About & Feature ***/
.about-img,
.feature-img {
    position: relative;
    height: 100%;
    min-height: 400px;
}

.about-img img,
.feature-img img {
    position: absolute;
    width: 60%;
    height: 80%;
    object-fit: cover;
}

.about-img img:last-child,
.feature-img img:last-child {
    margin: 20% 0 0 40%;
}

.about-img::before,
.feature-img::before {
    position: absolute;
    content: "";
    width: 60%;
    height: 80%;
    top: 10%;
    left: 20%;
    border: 5px solid var(--accent-orange);
    z-index: -1;
}


/*** Service ***/
.service-item .bg-img {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

.service-item .service-text {
    background: var(--accent-light);
    transition: .5s;
}

.service-item:hover .service-text {
    background: rgba(33, 37, 41, .7);
}

.service-item * {
    transition: .5;
}

.service-item:hover * {
    color: var(--text-on-dark);
}

.service-item .btn {
    color: var(--accent-orange);
    background: var(--accent-light);
    border-color: var(--accent-light);
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    transition: .5s;
}

.service-item:hover .btn {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    width: 140px;
}


/*** Project ***/
.project .nav .nav-link {
    background: var(--accent-light);
    transition: .5s;
}

.project .nav .nav-link.active {
    background: var(--accent-orange);
}

.project .nav .nav-link.active h3 {
    color: var(--text-on-dark) !important;
}

.team-items {
    margin: -.75rem;
}

.team-item {
    background: var(--accent-light);
    padding: .75rem;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.team-item::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    background: var(--accent-orange);
    transition: .5s;
    z-index: -1;
}

.team-item:hover::after {
    height: 100%;
    background: var(--accent-orange);
}

.team-item .team-social {
    position: absolute;
    width: 100%;
    bottom: -20px;
    left: 0;
}

.team-item .team-social .btn {
    display: inline-flex;
    margin: 0 2px;
    color: var(--accent-orange);
    background: var(--accent-light);
}

.team-item .team-social .btn:hover {
    color: var(--text-on-dark);
    background: var(--accent-orange);
}


/*** Appointment ***/
.bootstrap-datetimepicker-widget.bottom {
    top: auto !important;
}

.bootstrap-datetimepicker-widget .table * {
    border-bottom-width: 0px;
}

.bootstrap-datetimepicker-widget .table th {
    font-weight: 500;
}

.bootstrap-datetimepicker-widget.dropdown-menu {
    padding: 10px;
    border-radius: 2px;
}

.bootstrap-datetimepicker-widget table td.active,
.bootstrap-datetimepicker-widget table td.active:hover {
    background: var(--accent-orange);
}

.bootstrap-datetimepicker-widget table td.today::before {
    border-bottom-color: var(--accent-orange);
}


/*** Testimonial ***/
.testimonial-carousel {
    display: flex !important;
    flex-direction: column-reverse;
    max-width: 700px;
    margin: 0 auto;
}

.testimonial-carousel .owl-dots {
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
}

.testimonial-carousel .owl-dots .owl-dot {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 5px;
    transition: .5s;
    border-radius: 50%;
    overflow: hidden;
}

.testimonial-carousel .owl-dots .owl-dot.active {
    width: 100px;
    height: 100px;
}

.testimonial-carousel .owl-dots .owl-dot::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: var(--accent-light);
    border-radius: 50%;
    transform: scale(0);
    transition: .5s;
    z-index: 1;
}

.testimonial-carousel .owl-dots .owl-dot.active::after {
    transform: scale(1);
    opacity: 0.3;
    z-index: 1;
}

.testimonial-carousel .owl-dots .owl-dot img {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
    padding: 2px;
    transition: .5s;
    opacity: .7;
    background: var(--accent-light);
    border-radius: 50%;
    z-index: 2;
}

.testimonial-carousel .owl-dots .owl-dot.active img {
    opacity: 1;
    z-index: 3;
}


/*** Footer ***/
.footer .btn.btn-link {
    display: block;
    margin-bottom: 5px;
    padding: 0;
    text-align: left;
    color: var(--text-on-light);
    font-weight: normal;
    text-transform: capitalize;
    transition: .3s;
}

.footer .btn.btn-link::before {
    position: relative;
    content: "\f105";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: var(--accent-orange);
    margin-right: 10px;
}

.footer .btn.btn-link:hover {
    color: var(--accent-orange);
    letter-spacing: 1px;
    box-shadow: none;
}

.footer .form-control {
    height: 50px;
    background: var(--primary-dark);
    border: 1px solid var(--primary-dark);
    border-radius: 2px;
    color: var(--text-on-dark);
}

.footer .form-control::placeholder {
    color: rgba(248, 249, 250, .5);
}

.footer .form-control:focus {
    background: var(--primary-dark);
    border-color: var(--accent-orange);
}

.footer .copyright {
    padding: 30px 0;
    border-top: 1px solid rgba(248, 249, 250, .1);
}

.footer .copyright a {
    color: var(--text-on-dark);
}

.footer .copyright a:hover {
    color: var(--accent-orange);
}

/* Update primary color references */
.text-primary {
    color: var(--accent-orange) !important;
}

.bg-primary {
    background: var(--accent-orange) !important;
}

.btn-primary {
    background: var(--accent-orange);
    border-color: var(--accent-orange);
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    vertical-align: middle !important;
    line-height: 1.5 !important;
    padding: 0.5rem 1rem !important;
}

.btn-primary:hover {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
}

/* Update dark backgrounds */
.bg-dark {
    background: var(--primary-dark) !important;
}

/* Update light backgrounds */
.bg-light {
    background: var(--accent-light) !important;
}

/* Update text colors */
.text-dark {
    color: var(--primary-dark) !important;
}

.text-light {
    color: var(--accent-light) !important;
}

/* Update borders */
.border-primary {
    border-color: var(--accent-orange) !important;
}

/* Update hover states */
.btn-outline-primary {
    color: var(--accent-orange);
    border-color: var(--accent-orange);
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    vertical-align: middle !important;
    line-height: 1.5 !important;
    padding: 0.5rem 1rem !important;
}

.btn-outline-primary:hover {
    background: var(--accent-orange);
    border-color: var(--accent-orange);
    color: white;
}

/* Update gradients */
.bg-gradient {
    background: var(--main-gradient) !important;
}

/* Update shadows */
.shadow-primary {
    box-shadow: 0 0.5rem 1rem rgba(253, 126, 20, 0.15) !important;
}

/* Update focus states */
.btn-primary:focus,
.btn-primary:active {
    background: var(--primary-blue) !important;
    border-color: var(--primary-blue) !important;
}

/* Button alignment fixes */
.btn-sm {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    vertical-align: middle;
    line-height: 1.5;
    padding: 0.375rem 0.75rem;
}

.btn-outline-primary.btn-sm {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    vertical-align: middle;
    line-height: 1.5;
}

/* Hide unwanted pagination numbers in contractor cards */
.contractor-card .pagination,
.contractor-card .page-numbers,
.contractor-card .owl-dots,
.contractor-card .owl-nav,
.contractor-card [class*="page-"],
.contractor-card [class*="pagination-"],
.contractor-card [class*="number-"] {
    display: none !important;
}

/* Hide any numbered elements that might appear in contractor cards */
.contractor-card .numbered-list,
.contractor-card .counter,
.contractor-card .count,
.contractor-card .step-number {
    display: none !important;
}

/* Update links */
a {
    color: var(--accent-orange);
}

a:hover {
    color: var(--primary-blue);
}

/* Update form elements */
.form-control:focus {
    border-color: var(--accent-orange);
    box-shadow: 0 0 0 0.25rem rgba(253, 126, 20, 0.25);
}

/* Update navigation */
.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link.active {
    color: var(--accent-orange);
}

/* Update footer */
.footer {
    background: var(--primary-dark) !important;
}

.footer .btn-link {
    color: var(--accent-light);
}

.footer .btn-link:hover {
    color: var(--accent-orange);
}

/* Update testimonials */
.testimonial-item .testimonial-text {
    background: var(--accent-light);
}

.testimonial-item .testimonial-text::after {
    border-top-color: var(--accent-light);
}

/* Update service items */
.service-item:hover {
    background: var(--accent-light);
}

.service-item .service-text {
    background: var(--accent-light);
}

/* Update team items */
.team-item .team-social {
    background: var(--primary-dark);
}

.team-item .team-social .btn {
    color: var(--accent-light);
}

.team-item .team-social .btn:hover {
    color: var(--accent-orange);
}

/* Update appointment form */
.appointment-form .form-control:focus {
    border-color: var(--accent-orange);
}

/* Update carousel */
.carousel-caption {
    background: rgba(33, 37, 41, 0.7);
}

/* Update progress bars */
.progress-bar {
    background: var(--accent-orange);
}

/* Update badges */
.badge-primary {
    background: var(--accent-orange);
}

/* Update alerts */
.alert-primary {
    background: var(--accent-orange);
    border-color: var(--accent-orange);
    color: var(--text-on-dark);
}

/* Update cards */
.card-primary {
    background: var(--accent-orange);
    border-color: var(--accent-orange);
}

/* Update list groups */
.list-group-item-primary {
    background: var(--accent-orange);
    border-color: var(--accent-orange);
    color: var(--text-on-dark);
}

/* Update pagination */
.page-item.active .page-link {
    background: var(--accent-orange);
    border-color: var(--accent-orange);
}

.page-link {
    color: var(--accent-orange);
}

.page-link:hover {
    color: var(--primary-blue);
}

/* Update tooltips */
.tooltip .tooltip-inner {
    background: var(--primary-dark);
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--primary-dark);
}

/* Update popovers */
.popover {
    border-color: var(--accent-orange);
}

.popover .popover-header {
    background: var(--accent-orange);
    color: var(--text-on-dark);
}

/* Update modals */
.modal-header {
    background: var(--primary-dark);
    color: var(--text-on-dark);
}

.modal-footer {
    background: var(--accent-light);
}

/* Update tables */
.table-primary {
    background: var(--accent-orange);
    color: var(--text-on-dark);
}

/* Update dropdowns */
.dropdown-item:active {
    background: var(--accent-orange);
}

/* Update custom scrollbar */
::-webkit-scrollbar-thumb {
    background: var(--accent-orange);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-blue);
}

/* Update selection */
::selection {
    background: var(--accent-orange);
    color: var(--text-on-dark);
}

/* Update focus ring */
:focus {
    outline-color: var(--accent-orange);
}

/* Update transitions */
.transition-primary {
    transition: all 0.3s ease;
}

/* Update animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        background: var(--primary-dark);
    }
    to {
        opacity: 1;
        background: var(--accent-light);
    }
}

/* Update responsive styles */
@media (max-width: 768px) {
    .navbar-light .navbar-nav .nav-link:hover,
    .navbar-light .navbar-nav .nav-link.active {
        color: var(--accent-orange);
    }
}

/* Update print styles */
@media print {
    .text-primary {
        color: var(--primary-dark) !important;
    }
    
    .bg-primary {
        background: var(--primary-dark) !important;
    }
}

/* Make all icons use the primary blue color */
i, .fa, .bi {
  color: var(--primary-blue) !important;
}