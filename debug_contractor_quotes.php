<?php
require_once 'config/database.php';

echo "<h2>🔍 Debug Contractor Quote Display Issue</h2>";

try {
    // Step 1: Check if we have any contractors
    echo "<h3>Step 1: Check Contractors</h3>";
    
    $stmt = $pdo->query("
        SELECT u.id, u.email, u.status, cp.business_name, cp.service_areas, cp.service_types
        FROM users u 
        LEFT JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor'
        ORDER BY u.id
    ");
    $contractors = $stmt->fetchAll();
    
    echo "<p>Found " . count($contractors) . " contractors:</p>";
    
    if (empty($contractors)) {
        echo "<p style='color: red;'>❌ No contractors found! This is the main issue.</p>";
        echo "<p>Creating a test contractor...</p>";
        
        // Create test contractor
        $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, 'contractor', 'approved')");
        $stmt->execute(['<EMAIL>', password_hash('password', PASSWORD_DEFAULT)]);
        $contractor_id = $pdo->lastInsertId();
        
        // Create contractor profile
        $stmt = $pdo->prepare("
            INSERT INTO contractor_profiles (
                user_id, business_name, contact_person, phone, business_address,
                service_areas, service_types, cida_registration, cida_grade,
                business_description, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        $stmt->execute([
            $contractor_id,
            'Test Construction Company',
            'Test Contractor',
            '+94 77 123 4567',
            'Test Address, Colombo',
            json_encode(['Colombo', 'Gampaha', 'Kalutara']),
            json_encode([1, 2, 3]), // House Construction, Renovation, Commercial
            'CIDA/TEST/2024/001',
            'C5',
            'Test contractor for debugging purposes'
        ]);
        
        echo "<p style='color: green;'>✅ Created test contractor (ID: $contractor_id)</p>";
        echo "<p><strong>Login credentials:</strong> <EMAIL> / password</p>";
        
        // Refresh contractors list
        $stmt = $pdo->query("
            SELECT u.id, u.email, u.status, cp.business_name, cp.service_areas, cp.service_types
            FROM users u 
            LEFT JOIN contractor_profiles cp ON u.id = cp.user_id 
            WHERE u.user_type = 'contractor'
            ORDER BY u.id
        ");
        $contractors = $stmt->fetchAll();
    }
    
    foreach ($contractors as $contractor) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo "<strong>ID:</strong> " . $contractor['id'] . "<br>";
        echo "<strong>Email:</strong> " . htmlspecialchars($contractor['email']) . "<br>";
        echo "<strong>Status:</strong> " . $contractor['status'] . "<br>";
        echo "<strong>Business:</strong> " . htmlspecialchars($contractor['business_name'] ?? 'No profile') . "<br>";
        
        if ($contractor['service_areas']) {
            $areas = json_decode($contractor['service_areas'], true);
            echo "<strong>Service Areas:</strong> " . (is_array($areas) ? implode(', ', $areas) : 'Invalid JSON') . "<br>";
        }
        
        if ($contractor['service_types']) {
            $types = json_decode($contractor['service_types'], true);
            echo "<strong>Service Types:</strong> " . (is_array($types) ? implode(', ', $types) : 'Invalid JSON') . "<br>";
        }
        echo "</div>";
    }
    
    // Step 2: Check if we have any quote requests
    echo "<h3>Step 2: Check Quote Requests</h3>";
    
    $stmt = $pdo->query("
        SELECT qr.*, cp.first_name, cp.last_name, sc.name_en as service_name
        FROM quote_requests qr
        LEFT JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        ORDER BY qr.created_at DESC
        LIMIT 10
    ");
    $quote_requests = $stmt->fetchAll();
    
    echo "<p>Found " . count($quote_requests) . " quote requests:</p>";
    
    if (empty($quote_requests)) {
        echo "<p style='color: red;'>❌ No quote requests found! Creating a test quote request...</p>";
        
        // Check if we have a customer
        $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' AND status = 'approved' LIMIT 1");
        $customer = $stmt->fetch();
        
        if (!$customer) {
            echo "<p>Creating test customer...</p>";
            
            // Create test customer
            $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, 'customer', 'approved')");
            $stmt->execute(['<EMAIL>', password_hash('password', PASSWORD_DEFAULT)]);
            $customer_id = $pdo->lastInsertId();
            
            // Create customer profile
            $stmt = $pdo->prepare("
                INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district, address) 
                VALUES (?, 'Test', 'Customer', '+94 77 123 4567', 'Colombo', 'Test Address, Colombo')
            ");
            $stmt->execute([$customer_id]);
            
            echo "<p style='color: green;'>✅ Created test customer (ID: $customer_id)</p>";
        } else {
            $customer_id = $customer['id'];
        }
        
        // Create test quote request
        $stmt = $pdo->prepare("
            INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'open')
        ");
        $stmt->execute([
            $customer_id,
            1, // House Construction
            'Test Quote Request - ' . date('Y-m-d H:i:s'),
            'This is a test quote request for debugging purposes.',
            'Test Location, Colombo',
            'Colombo',
            5000000,
            '6 months'
        ]);
        $quote_id = $pdo->lastInsertId();
        
        echo "<p style='color: green;'>✅ Created test quote request (ID: $quote_id)</p>";
        
        // Refresh quote requests
        $stmt = $pdo->query("
            SELECT qr.*, cp.first_name, cp.last_name, sc.name_en as service_name
            FROM quote_requests qr
            LEFT JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
            ORDER BY qr.created_at DESC
            LIMIT 10
        ");
        $quote_requests = $stmt->fetchAll();
    }
    
    foreach ($quote_requests as $quote) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
        echo "<strong>ID:</strong> " . $quote['id'] . "<br>";
        echo "<strong>Title:</strong> " . htmlspecialchars($quote['title']) . "<br>";
        echo "<strong>Service:</strong> " . htmlspecialchars($quote['service_name'] ?? 'Unknown') . " (ID: " . $quote['service_category_id'] . ")<br>";
        echo "<strong>District:</strong> " . $quote['district'] . "<br>";
        echo "<strong>Status:</strong> " . $quote['status'] . "<br>";
        echo "<strong>Specific Contractor:</strong> " . ($quote['specific_contractor_id'] ?? 'General') . "<br>";
        echo "<strong>Customer:</strong> " . htmlspecialchars(($quote['first_name'] ?? '') . ' ' . ($quote['last_name'] ?? '')) . "<br>";
        echo "<strong>Created:</strong> " . $quote['created_at'] . "<br>";
        echo "</div>";
    }
    
    // Step 3: Test contractor quote matching for the first contractor
    if (!empty($contractors) && !empty($quote_requests)) {
        echo "<h3>Step 3: Test Quote Matching</h3>";
        
        $test_contractor = $contractors[0];
        $test_quote = $quote_requests[0];
        
        echo "<p>Testing contractor: " . htmlspecialchars($test_contractor['business_name']) . " (ID: " . $test_contractor['id'] . ")</p>";
        echo "<p>Testing quote: " . htmlspecialchars($test_quote['title']) . " (ID: " . $test_quote['id'] . ")</p>";
        
        // Test the exact query from contractor/quotes.php
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
                   (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
                   CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
                   cp_contractor.service_types, cp_contractor.service_areas
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            JOIN service_categories sc ON qr.service_category_id = sc.id
            JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
            WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
            AND qr.id = ?
        ");
        
        $contractor_id = $test_contractor['id'];
        $stmt->execute([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $test_quote['id']]);
        $result = $stmt->fetch();
        
        if ($result) {
            echo "<p style='color: green;'>✅ SQL query found the quote</p>";
            
            // Test the PHP filtering logic
            if ($result['specific_contractor_id'] == $contractor_id) {
                echo "<p style='color: green;'>✅ Direct quote match</p>";
            } elseif ($result['specific_contractor_id'] === null) {
                echo "<p>Testing general quote matching...</p>";
                
                $contractor_services = json_decode($result['service_types'], true) ?: [];
                $contractor_areas = json_decode($result['service_areas'], true) ?: [];
                
                echo "<p>Contractor services: " . implode(', ', $contractor_services) . "</p>";
                echo "<p>Contractor areas: " . implode(', ', $contractor_areas) . "</p>";
                echo "<p>Quote service ID: " . $result['service_category_id'] . "</p>";
                echo "<p>Quote district: " . $result['district'] . "</p>";
                
                $has_service = in_array((int)$result['service_category_id'], $contractor_services) ||
                              in_array((string)$result['service_category_id'], $contractor_services);
                $has_area = in_array($result['district'], $contractor_areas);
                
                echo "<p>Has service: " . ($has_service ? '✅ Yes' : '❌ No') . "</p>";
                echo "<p>Has area: " . ($has_area ? '✅ Yes' : '❌ No') . "</p>";
                
                if ($has_service && $has_area) {
                    echo "<p style='color: green;'>✅ General quote should be visible to contractor</p>";
                } else {
                    echo "<p style='color: red;'>❌ General quote will NOT be visible to contractor</p>";
                    
                    if (!$has_service) {
                        echo "<p style='color: orange;'>⚠️ Service mismatch. Contractor needs service ID " . $result['service_category_id'] . " in their service_types</p>";
                    }
                    if (!$has_area) {
                        echo "<p style='color: orange;'>⚠️ Area mismatch. Contractor needs '" . $result['district'] . "' in their service_areas</p>";
                    }
                }
            }
        } else {
            echo "<p style='color: red;'>❌ SQL query did not find the quote</p>";
            echo "<p>This indicates a database structure or JOIN issue.</p>";
        }
    }
    
    // Step 4: Check notifications
    echo "<h3>Step 4: Check Notifications</h3>";
    
    if (!empty($contractors)) {
        $contractor_id = $contractors[0]['id'];
        
        $stmt = $pdo->prepare("
            SELECT * FROM notifications 
            WHERE user_id = ? AND type = 'quote_received'
            ORDER BY created_at DESC
            LIMIT 5
        ");
        $stmt->execute([$contractor_id]);
        $notifications = $stmt->fetchAll();
        
        echo "<p>Notifications for contractor " . $contractor_id . ": " . count($notifications) . "</p>";
        
        foreach ($notifications as $notification) {
            echo "<div style='background: #f0f0f0; padding: 10px; margin: 5px 0;'>";
            echo "<strong>Title:</strong> " . htmlspecialchars($notification['title']) . "<br>";
            echo "<strong>Message:</strong> " . htmlspecialchars($notification['message']) . "<br>";
            echo "<strong>Related ID:</strong> " . $notification['related_id'] . "<br>";
            echo "<strong>Created:</strong> " . $notification['created_at'] . "<br>";
            echo "</div>";
        }
    }
    
    // Step 5: Provide fix recommendations
    echo "<h3>Step 5: Fix Recommendations</h3>";
    
    if (empty($contractors)) {
        echo "<p style='color: red;'>🔧 <strong>Issue:</strong> No contractors in the system</p>";
        echo "<p><strong>Solution:</strong> Create contractor accounts and profiles</p>";
    }
    
    if (empty($quote_requests)) {
        echo "<p style='color: red;'>🔧 <strong>Issue:</strong> No quote requests in the system</p>";
        echo "<p><strong>Solution:</strong> Create test quote requests</p>";
    }
    
    echo "<h3>✅ Debug Complete</h3>";
    echo "<p>If contractors still can't see quotes after running this script:</p>";
    echo "<ol>";
    echo "<li>Check contractor login credentials</li>";
    echo "<li>Verify contractor status is 'approved'</li>";
    echo "<li>Ensure contractor has proper service_areas and service_types</li>";
    echo "<li>Check that quote requests match contractor's services and areas</li>";
    echo "<li>Run the fix_general_quotes.php script</li>";
    echo "</ol>";
    
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li><a href='contractor/login.php'>Login as contractor</a> (<EMAIL> / password)</li>";
    echo "<li><a href='customer/login.php'>Login as customer</a> (<EMAIL> / password)</li>";
    echo "<li><a href='fix_general_quotes.php'>Run general quotes fix</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
