<?php
// Include authentication and authorization check
require_once 'includes/auth_check.php';

// Contractor data is now available in $contractor variable

$quote_id = (int)($_GET['id'] ?? 0);

if (!$quote_id) {
    $_SESSION['error'] = 'Invalid quote request.';
    header('Location: quotes.php');
    exit();
}

// Get quote request details
try {
    // First ensure specific_contractor_id column exists
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
        $column_exists = $stmt->fetch();

        if (!$column_exists) {
            $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        }
    } catch (PDOException $e) {
        // Column might already exist, continue
    }

    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, cp.address, u.email, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               CASE WHEN qr.specific_contractor_id = ? THEN 'direct' ELSE 'general' END as quote_type
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        JOIN users u ON qr.customer_id = u.id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE qr.id = ? AND qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
    ");
    $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id'], $quote_id, $_SESSION['user_id']]);
    $quote = $stmt->fetch();
    
    if (!$quote) {
        $_SESSION['error'] = 'Quote request not found or no longer available.';
        header('Location: quotes.php');
        exit();
    }

    // For general quotes, check if contractor matches service and area requirements
    if ($quote['quote_type'] === 'general') {
        // Get contractor data
        $stmt = $pdo->prepare("SELECT service_types, service_areas FROM contractor_profiles WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $contractor_data = $stmt->fetch();

        if ($contractor_data) {
            $contractor_services = json_decode($contractor_data['service_types'], true) ?: [];
            $contractor_areas = json_decode($contractor_data['service_areas'], true) ?: [];

            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);

            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);

            if (!$has_service || !$has_area) {
                $_SESSION['error'] = 'This quote request does not match your service areas or service types.';
                header('Location: quotes.php');
                exit();
            }
        }
    }

    if ($quote['has_responded'] > 0) {
        $_SESSION['error'] = 'You have already responded to this quote request.';
        header('Location: quotes.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: quotes.php');
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $quoted_amount = (float)$_POST['quoted_amount'];
    $estimated_timeline = trim($_POST['estimated_timeline']);
    $description = trim($_POST['description']);
    $terms_conditions = trim($_POST['terms_conditions']);
    
    $errors = [];
    
    // Validation
    if ($quoted_amount <= 0) $errors[] = 'Please enter a valid quote amount.';
    if (empty($estimated_timeline)) $errors[] = 'Estimated timeline is required.';
    if (empty($description)) $errors[] = 'Quote description is required.';
    if (empty($terms_conditions)) $errors[] = 'Terms and conditions are required.';
    
    if (empty($errors)) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO quote_responses (
                    quote_request_id, contractor_id, quoted_amount, estimated_timeline,
                    description, terms_conditions, status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW(), NOW())
            ");
            
            $stmt->execute([
                $quote_id,
                $_SESSION['user_id'],
                $quoted_amount,
                $estimated_timeline,
                $description,
                $terms_conditions
            ]);

            // Create notification for customer
            $stmt = $pdo->prepare("
                INSERT INTO notifications (user_id, title, message, type, related_id, is_read, created_at)
                VALUES (?, ?, ?, 'quote_response', ?, 0, NOW())
            ");
            $stmt->execute([
                $quote['customer_id'],
                'New Quote Response Received',
                'A contractor has responded to your quote request for ' . $quote['service_category'],
                $quote_id
            ]);

            $_SESSION['success'] = 'Quote response submitted successfully!';
            header('Location: quotes.php');
            exit();
            
        } catch (PDOException $e) {
            $errors[] = 'Database error. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Respond to Quote - BrickClick Contractor</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet"> 
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF6B35;
            --accent-yellow: #F7931E;
            --success-green: #28a745;
            --info-blue: #17a2b8;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --dark-gray: #343a40;
        }
        
        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e9ecef 100%);
            font-family: 'Open Sans', sans-serif;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            z-index: 1000;
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            flex-shrink: 0;
        }

        .sidebar-header h3 {
            color: var(--accent-orange);
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }

        .sidebar-header p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }

        .sidebar-nav {
            flex: 1;
            padding: 0.5rem 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .nav-item {
            margin: 0.15rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,107,53,0.1);
            color: var(--accent-orange);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--accent-orange), var(--primary-red));
            color: white;
            box-shadow: 0 3px 10px rgba(255,107,53,0.3);
        }

        .nav-link i {
            margin-right: 0.8rem;
            width: 18px;
            text-align: center;
            font-size: 1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--accent-orange), #e55a2b);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .page-title {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 2rem;
        }
        
        .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }
        
        .content-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .section-title {
            color: var(--primary-dark);
            font-weight: 700;
            margin-bottom: 1.5rem;
            font-size: 1.3rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--accent-orange);
        }
        
        .quote-details {
            background: var(--light-gray);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .detail-row {
            display: flex;
            margin-bottom: 1rem;
        }
        
        .detail-row:last-child {
            margin-bottom: 0;
        }
        
        .detail-label {
            font-weight: 600;
            color: var(--primary-dark);
            width: 150px;
            flex-shrink: 0;
        }
        
        .detail-value {
            color: var(--dark-gray);
        }
        
        .customer-info {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .customer-name {
            color: var(--primary-dark);
            font-weight: 700;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }
        
        .customer-details {
            color: var(--dark-gray);
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            color: var(--primary-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--accent-orange);
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group-text {
            background: var(--accent-orange);
            color: white;
            border: 2px solid var(--accent-orange);
            border-radius: 10px 0 0 10px;
            font-weight: 600;
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        
        .btn-submit {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .btn-cancel {
            background: linear-gradient(135deg, var(--medium-gray), #5a6268);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
        
        .required {
            color: var(--primary-red);
        }
        
        .form-text {
            color: var(--medium-gray);
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>BrickClick</h3>
            <p>Contractor Portal</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link active">
                    <i class="fas fa-file-invoice-dollar"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="portfolio.php" class="nav-link">
                    <i class="fas fa-images"></i>Portfolio
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews & Ratings
                </a>
            </div>
            <div class="nav-item">
                <a href="profile.php" class="nav-link">
                    <i class="fas fa-user-edit"></i>Profile & Services
                </a>
            </div>
            <div class="nav-item">
                <a href="notifications.php" class="nav-link">
                    <i class="fas fa-bell"></i>Notifications
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Respond to Quote Request</h1>
            <p class="page-subtitle">Provide your quote and terms for this project</p>
        </div>

        <!-- Quote Request Details -->
        <div class="content-card">
            <h3 class="section-title">Quote Request Details</h3>
            
            <div class="customer-info">
                <div class="customer-name">
                    <i class="fas fa-user me-2"></i>
                    <?php echo htmlspecialchars($quote['first_name'] . ' ' . $quote['last_name']); ?>
                </div>
                <div class="customer-details">
                    <span>
                        <i class="fas fa-map-marker-alt me-1"></i>
                        <?php echo htmlspecialchars($quote['district']); ?>
                    </span>
                    <span>
                        <i class="fas fa-phone me-1"></i>
                        <?php echo htmlspecialchars($quote['phone']); ?>
                    </span>
                    <span>
                        <i class="fas fa-calendar-alt me-1"></i>
                        Posted: <?php echo date('M j, Y', strtotime($quote['created_at'])); ?>
                    </span>
                </div>
            </div>
            
            <div class="quote-details">
                <div class="detail-row">
                    <div class="detail-label">Service Category:</div>
                    <div class="detail-value"><?php echo htmlspecialchars($quote['service_category']); ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Estimated Budget:</div>
                    <div class="detail-value">Rs. <?php echo number_format($quote['estimated_budget']); ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Timeline:</div>
                    <div class="detail-value"><?php echo htmlspecialchars($quote['project_timeline']); ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Location:</div>
                    <div class="detail-value"><?php echo htmlspecialchars($quote['location']); ?></div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Description:</div>
                    <div class="detail-value"><?php echo nl2br(htmlspecialchars($quote['description'])); ?></div>
                </div>
            </div>
        </div>

        <!-- Quote Response Form -->
        <div class="content-card">
            <h3 class="section-title">Your Quote Response</h3>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Quote Amount <span class="required">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">Rs.</span>
                                <input type="number" class="form-control" name="quoted_amount" 
                                       value="<?php echo htmlspecialchars($_POST['quoted_amount'] ?? ''); ?>" 
                                       min="1" step="0.01" required>
                            </div>
                            <div class="form-text">Enter your quoted amount for this project</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Estimated Timeline <span class="required">*</span></label>
                            <input type="text" class="form-control" name="estimated_timeline" 
                                   value="<?php echo htmlspecialchars($_POST['estimated_timeline'] ?? ''); ?>" 
                                   placeholder="e.g., 2-3 weeks, 1 month" required>
                            <div class="form-text">How long will this project take to complete?</div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Quote Description <span class="required">*</span></label>
                    <textarea class="form-control" name="description" rows="5" required 
                              placeholder="Describe your approach, materials to be used, work process, etc."><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                    <div class="form-text">Provide details about how you will execute this project</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Terms and Conditions <span class="required">*</span></label>
                    <textarea class="form-control" name="terms_conditions" rows="4" required 
                              placeholder="Payment terms, warranty, materials responsibility, etc."><?php echo htmlspecialchars($_POST['terms_conditions'] ?? ''); ?></textarea>
                    <div class="form-text">Specify your terms, payment schedule, and conditions</div>
                </div>
                
                <div class="d-flex gap-3 mt-4">
                    <button type="submit" class="btn btn-submit">
                        <i class="fas fa-paper-plane me-2"></i>Submit Quote Response
                    </button>
                    <a href="quotes.php" class="btn btn-cancel">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
