<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = 'Invalid request method.';
    header('Location: dashboard.php');
    exit();
}

// Get form data
$contractor_id = (int)$_POST['contractor_id'];
$payment_id = (int)$_POST['payment_id'];
$quote_response_id = (int)$_POST['quote_response_id'];
$rating = (int)$_POST['rating'];
$review_text = trim($_POST['review_text']);
$quality_rating = !empty($_POST['quality_rating']) ? (int)$_POST['quality_rating'] : null;
$communication_rating = !empty($_POST['communication_rating']) ? (int)$_POST['communication_rating'] : null;
$timeliness_rating = !empty($_POST['timeliness_rating']) ? (int)$_POST['timeliness_rating'] : null;
$value_rating = !empty($_POST['value_rating']) ? (int)$_POST['value_rating'] : null;
$recommend = isset($_POST['recommend']) ? (int)$_POST['recommend'] : null;

// Validate required fields
if (!$contractor_id || !$payment_id || !$rating || $rating < 1 || $rating > 5) {
    $_SESSION['error'] = 'Please provide a valid rating.';
    header("Location: payment_success.php?payment_id=$payment_id");
    exit();
}

// Verify payment belongs to current customer
try {
    $stmt = $pdo->prepare("
        SELECT pp.*, qr.customer_id 
        FROM project_payments pp
        JOIN quote_responses qres ON pp.quote_response_id = qres.id
        JOIN quote_requests qr ON qres.quote_request_id = qr.id
        WHERE pp.id = ? AND qr.customer_id = ? AND pp.contractor_id = ?
    ");
    $stmt->execute([$payment_id, $_SESSION['user_id'], $contractor_id]);
    $payment = $stmt->fetch();
    
    if (!$payment) {
        $_SESSION['error'] = 'Invalid payment or contractor information.';
        header('Location: dashboard.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error occurred.';
    header('Location: dashboard.php');
    exit();
}

// Check if review already exists
try {
    $stmt = $pdo->prepare("
        SELECT id FROM reviews 
        WHERE customer_id = ? AND contractor_id = ? AND payment_id = ?
    ");
    $stmt->execute([$_SESSION['user_id'], $contractor_id, $payment_id]);
    $existing_review = $stmt->fetch();
    
    if ($existing_review) {
        $_SESSION['error'] = 'You have already submitted a review for this payment.';
        header("Location: payment_success.php?payment_id=$payment_id");
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error checking existing reviews.';
    header("Location: payment_success.php?payment_id=$payment_id");
    exit();
}

try {
    $pdo->beginTransaction();

    // Get quote_request_id from quote_response
    $stmt = $pdo->prepare("SELECT quote_request_id FROM quote_responses WHERE id = ?");
    $stmt->execute([$quote_response_id]);
    $quote_request_data = $stmt->fetch();
    $quote_request_id = $quote_request_data ? $quote_request_data['quote_request_id'] : null;

    // Insert review
    $stmt = $pdo->prepare("
        INSERT INTO reviews (
            customer_id, contractor_id, payment_id, quote_response_id, quote_request_id,
            rating, review_text, quality_rating, communication_rating,
            timeliness_rating, value_rating, recommend, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'approved')
    ");

    $stmt->execute([
        $_SESSION['user_id'],
        $contractor_id,
        $payment_id,
        $quote_response_id,
        $quote_request_id,
        $rating,
        $review_text,
        $quality_rating,
        $communication_rating,
        $timeliness_rating,
        $value_rating,
        $recommend
    ]);
    
    $review_id = $pdo->lastInsertId();
    
    // Update contractor's average rating
    // Check which review field structure we're using
    $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'status'");
    $has_status = $stmt->fetch();

    if ($has_status) {
        $review_condition = "(status IS NULL OR status != 'deleted')";
    } else {
        $review_condition = "(is_approved IS NULL OR is_approved != 0)";
    }

    $stmt = $pdo->prepare("
        UPDATE contractor_profiles
        SET average_rating = (
            SELECT AVG(rating)
            FROM reviews
            WHERE contractor_id = ? AND $review_condition
        ),
        total_reviews = (
            SELECT COUNT(*)
            FROM reviews
            WHERE contractor_id = ? AND $review_condition
        )
        WHERE user_id = ?
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
    
    // Create notification for contractor
    $stmt = $pdo->prepare("
        INSERT INTO notifications (user_id, title, message, type, related_id) 
        VALUES (?, ?, ?, 'review_received', ?)
    ");
    
    $notification_title = "New Review Received";
    $notification_message = "You have received a new " . $rating . "-star review from a customer. Check your profile to see the feedback.";
    
    $stmt->execute([$contractor_id, $notification_title, $notification_message, $review_id]);
    
    $pdo->commit();
    
    $_SESSION['success'] = 'Thank you for your review! Your feedback has been submitted successfully.';
    header("Location: payment_success.php?payment_id=$payment_id");
    exit();
    
} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Review submission error: " . $e->getMessage());
    $_SESSION['error'] = 'Failed to submit review. Database error: ' . $e->getMessage();
    header("Location: payment_success.php?payment_id=$payment_id");
    exit();
}
?>
