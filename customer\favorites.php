<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

// Handle remove favorite action
if (isset($_POST['remove_favorite'])) {
    $contractor_id = (int)$_POST['contractor_id'];
    try {
        $stmt = $pdo->prepare("DELETE FROM customer_favorites WHERE customer_id = ? AND contractor_id = ?");
        $stmt->execute([$_SESSION['user_id'], $contractor_id]);
        $_SESSION['success'] = 'Contractor removed from favorites successfully!';
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Error removing contractor from favorites.';
    }
    header('Location: favorites.php');
    exit();
}

// Get customer profile
try {
    $stmt = $pdo->prepare("SELECT cp.*, u.email FROM customer_profiles cp JOIN users u ON cp.user_id = u.id WHERE cp.user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $customer = $stmt->fetch();
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: ../login.php');
    exit();
}

// Get unread notifications count
$unread_count = 0;
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$_SESSION['user_id']]);
    $unread_count = $stmt->fetchColumn();
} catch (PDOException $e) {
    // Ignore error, keep count as 0
}

// Get favorite contractors
try {
    $stmt = $pdo->prepare("
        SELECT cp.*, u.email, u.status, cf.created_at as favorited_at,
               (SELECT COUNT(*) FROM reviews r WHERE r.contractor_id = u.id AND r.is_approved = 1) as review_count,
               (SELECT COUNT(*) FROM quote_responses qr WHERE qr.contractor_id = u.id) as total_quotes_sent
        FROM customer_favorites cf
        JOIN users u ON cf.contractor_id = u.id
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE cf.customer_id = ? AND u.status = 'approved'
        ORDER BY cf.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $favorite_contractors = $stmt->fetchAll();
} catch (PDOException $e) {
    $favorite_contractors = [];
}

// Get service categories for display
try {
    $stmt = $pdo->prepare("SELECT * FROM service_categories WHERE is_active = 1 ORDER BY name_en");
    $stmt->execute();
    $service_categories = $stmt->fetchAll();
    $categories_map = [];
    foreach ($service_categories as $cat) {
        $categories_map[$cat['id']] = $cat['name_en'];
    }
} catch (PDOException $e) {
    $categories_map = [];
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>My Favorites - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet"> 

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
    <link href="../lib/lightbox/css/lightbox.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <style>
        .favorites-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }
        
        .favorites-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
            z-index: 1;
        }
        
        .favorites-header .container {
            position: relative;
            z-index: 2;
        }
        
        .contractor-card {
            background: linear-gradient(145deg, #f8f9ff, #e8e9ff);
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.15);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: all 0.4s ease;
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
        }
        
        .contractor-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .contractor-card:hover::before {
            left: 100%;
        }
        
        .contractor-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 60px rgba(102, 126, 234, 0.25);
        }
        
        .contractor-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }
        
        .contractor-card:hover .contractor-avatar {
            transform: scale(1.1);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }
        
        .rating-stars {
            color: #ffc107;
            font-size: 1.1rem;
        }
        
        .service-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .service-tag {
            background: linear-gradient(135deg, #C5172E, #FF6B35);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .btn-remove-favorite {
            background: linear-gradient(135deg, #dc3545, #c82333);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-remove-favorite:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
            color: white;
        }
        
        .btn-contact {
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-contact:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(253, 126, 20, 0.3);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 5rem 3rem;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 25px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .empty-state i {
            font-size: 4rem;
            color: #fd7e14;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .stats-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stats-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        .favorited-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        /* Footer positioning fix */
        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
        }

        .footer {
            margin-top: auto;
        }
    </style>
</head>

<body>
    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
        <a href="../index.php" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="../img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="contractors.php" class="nav-item nav-link">Find Contractors</a>
                <a href="quotes.php" class="nav-item nav-link">My Quotes</a>
                <a href="cost_estimator.php" class="nav-item nav-link">Cost Estimator</a>
                <a href="favorites.php" class="nav-item nav-link active">Favorites</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
            </div>
            <div class="dropdown">
                <a href="#" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($customer['first_name']); ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="notifications.php"><i class="fas fa-bell me-2"></i>Notifications <?php if ($unread_count > 0): ?><span class="badge bg-danger"><?php echo $unread_count; ?></span><?php endif; ?></a></li>
                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <div class="main-content">
        <!-- Favorites Header Start -->
    <div class="favorites-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 mb-3">
                        <i class="fas fa-heart me-3"></i>My Favorite Contractors
                    </h1>
                    <p class="lead mb-0">Your trusted contractors for future projects</p>
                </div>
                <div class="col-lg-4">
                    <div class="stats-card">
                        <div class="stats-number"><?php echo count($favorite_contractors); ?></div>
                        <div class="stats-label">Favorite Contractors</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Favorites Header End -->

    <!-- Main Content Start -->
    <div class="container-fluid py-5">
        <div class="container">
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (empty($favorite_contractors)): ?>
                <!-- Empty State -->
                <div class="empty-state">
                    <i class="fas fa-heart-broken"></i>
                    <h3 class="mb-3">No Favorite Contractors Yet</h3>
                    <p class="text-muted mb-4">Start building your list of trusted contractors by adding them to your favorites while browsing.</p>
                    <a href="contractors.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-search me-2"></i>Find Contractors
                    </a>
                </div>
            <?php else: ?>
                <!-- Favorites Grid -->
                <div class="row">
                    <?php foreach ($favorite_contractors as $contractor): ?>
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="contractor-card h-100">
                                <div class="card-body p-4">
                                    <div class="d-flex align-items-start mb-3">
                                        <div class="contractor-avatar me-3">
                                            <?php echo strtoupper(substr($contractor['business_name'], 0, 1)); ?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h5 class="card-title mb-1"><?php echo htmlspecialchars($contractor['business_name']); ?></h5>
                                            <p class="text-muted mb-1"><?php echo htmlspecialchars($contractor['contact_person']); ?></p>
                                            <div class="favorited-date">
                                                <i class="fas fa-heart me-1"></i>
                                                Added <?php echo date('M j, Y', strtotime($contractor['favorited_at'])); ?>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Rating and Stats -->
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="rating-stars me-3">
                                            <?php
                                            $rating = $contractor['average_rating'];
                                            for ($i = 1; $i <= 5; $i++):
                                            ?>
                                                <i class="fas fa-star<?php echo $i <= $rating ? '' : '-o'; ?>"></i>
                                            <?php endfor; ?>
                                            <span class="ms-1"><?php echo number_format($rating, 1); ?></span>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo $contractor['review_count']; ?> reviews
                                        </small>
                                    </div>

                                    <!-- Business Info -->
                                    <div class="mb-3">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="fw-bold text-primary"><?php echo $contractor['total_projects']; ?></div>
                                                <small class="text-muted">Projects</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="fw-bold text-primary"><?php echo $contractor['total_quotes_sent']; ?></div>
                                                <small class="text-muted">Quotes</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="fw-bold text-primary"><?php echo $contractor['cida_grade'] ?? 'N/A'; ?></div>
                                                <small class="text-muted">CIDA Grade</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Service Types -->
                                    <?php if (!empty($contractor['service_types'])): ?>
                                        <div class="service-tags mb-3">
                                            <?php
                                            $services = json_decode($contractor['service_types'], true);
                                            if ($services && is_array($services)):
                                                foreach (array_slice($services, 0, 3) as $service_id):
                                                    if (isset($categories_map[$service_id])):
                                            ?>
                                                <span class="service-tag"><?php echo htmlspecialchars($categories_map[$service_id]); ?></span>
                                            <?php
                                                    endif;
                                                endforeach;
                                                if (count($services) > 3):
                                            ?>
                                                <span class="service-tag">+<?php echo count($services) - 3; ?> more</span>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Contact Info -->
                                    <div class="mb-3">
                                        <small class="text-muted d-block">
                                            <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($contractor['phone']); ?>
                                        </small>
                                        <?php if (!empty($contractor['business_address'])): ?>
                                            <small class="text-muted d-block">
                                                <i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars(substr($contractor['business_address'], 0, 50)) . (strlen($contractor['business_address']) > 50 ? '...' : ''); ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="d-flex gap-2">
                                        <a href="request_quote.php?contractor=<?php echo $contractor['user_id']; ?>" class="btn btn-contact flex-grow-1">
                                            <i class="fas fa-paper-plane me-1"></i>Request Quote
                                        </a>
                                        <form method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to remove this contractor from your favorites?');">
                                            <input type="hidden" name="contractor_id" value="<?php echo $contractor['user_id']; ?>">
                                            <button type="submit" name="remove_favorite" class="btn btn-remove-favorite">
                                                <i class="fas fa-heart-broken"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Browse More Section -->
                <div class="text-center mt-5">
                    <h4 class="mb-3">Looking for more contractors?</h4>
                    <a href="contractors.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-search me-2"></i>Browse All Contractors
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <!-- Main Content End -->
    </div>

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer mt-5 pt-5 px-0">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Brick & Click</h3>
                    <p class="mb-2 text-light">Sri Lanka's leading construction contractor marketplace</p>
                    <p class="mb-2 text-light"><i class="fa fa-phone-alt text-primary me-3"></i>+94 77 123 4567</p>
                    <p class="mb-2 text-light"><i class="fa fa-envelope text-primary me-3"></i><EMAIL></p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Quick Links</h3>
                    <a class="btn btn-link text-light" href="contractors.php">Find Contractors</a>
                    <a class="btn btn-link text-light" href="quotes.php">My Quotes</a>
                    <a class="btn btn-link text-light" href="cost_estimator.php">Cost Estimator</a>
                    <a class="btn btn-link text-light" href="favorites.php">Favorites</a>
                    <a class="btn btn-link text-light" href="profile.php">Profile</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Services</h3>
                    <a class="btn btn-link text-light" href="contractors.php?category=1">House Construction</a>
                    <a class="btn btn-link text-light" href="contractors.php?category=2">Building Renovation</a>
                    <a class="btn btn-link text-light" href="contractors.php?category=3">Commercial Construction</a>
                    <a class="btn btn-link text-light" href="contractors.php?category=5">Roofing & Waterproofing</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Support</h3>
                    <a class="btn btn-link text-light" href="#">Help Center</a>
                    <a class="btn btn-link text-light" href="#">Contact Support</a>
                    <a class="btn btn-link text-light" href="#">Terms of Service</a>
                    <a class="btn btn-link text-light" href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="container-fluid copyright">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        <span class="text-light">&copy; <a href="#" class="text-primary">Brick & Click</a>, All Right Reserved.</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->



    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square rounded-circle back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>
    <script src="../lib/lightbox/js/lightbox.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
</body>

</html>
