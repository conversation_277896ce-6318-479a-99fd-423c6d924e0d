<?php
session_start();
require_once 'config/database.php';

echo "<h2>🧪 Test Contractor Quotes Fix</h2>";

try {
    // Get a test contractor
    $stmt = $pdo->query("
        SELECT u.id, u.email, cp.business_name 
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved' 
        LIMIT 1
    ");
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p style='color: red;'>❌ No contractors found. Please run fix_contractor_quotes_issue.php first.</p>";
        exit;
    }
    
    $contractor_id = $contractor['id'];
    echo "<p><strong>Testing with contractor:</strong> " . htmlspecialchars($contractor['business_name']) . " (ID: $contractor_id)</p>";
    
    // Test 1: Dashboard pending quotes count
    echo "<h3>Test 1: Dashboard Pending Quotes Count</h3>";
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.id NOT IN (
            SELECT COALESCE(quote_request_id, 0)
            FROM quote_responses
            WHERE contractor_id = ?
        )
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
    $all_pending_quotes = $stmt->fetchAll();
    
    // Filter quotes in PHP
    $pending_quotes = 0;
    foreach ($all_pending_quotes as $quote) {
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $pending_quotes++;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $pending_quotes++;
            }
        }
    }
    
    echo "<p><strong>Dashboard pending quotes:</strong> $pending_quotes</p>";
    
    // Test 2: Quotes page count (simulate the exact query)
    echo "<h3>Test 2: Quotes Page Count</h3>";
    
    // Simulate default "all" filter
    $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
    $params = [$contractor_id];
    $where_conditions[] = "(qr.status IN ('open', 'completed', 'cancelled') OR qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?))";
    $params[] = $contractor_id;
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
               cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");
    
    $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
    $stmt->execute($query_params);
    $all_quotes = $stmt->fetchAll();
    
    // Filter quotes in PHP
    $quotes = [];
    foreach ($all_quotes as $quote) {
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $quotes[] = $quote;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $quotes[] = $quote;
            }
        }
    }
    
    echo "<p><strong>Quotes page total quotes:</strong> " . count($quotes) . "</p>";
    
    // Test 3: Quotes page pending filter
    echo "<h3>Test 3: Quotes Page Pending Filter</h3>";
    
    $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
    $params = [$contractor_id];
    $where_conditions[] = "qr.status = 'open' AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)";
    $params[] = $contractor_id;
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
               cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");
    
    $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
    $stmt->execute($query_params);
    $all_quotes = $stmt->fetchAll();
    
    // Filter quotes in PHP
    $pending_quotes_page = [];
    foreach ($all_quotes as $quote) {
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $pending_quotes_page[] = $quote;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $pending_quotes_page[] = $quote;
            }
        }
    }
    
    echo "<p><strong>Quotes page pending filter:</strong> " . count($pending_quotes_page) . "</p>";
    
    // Summary
    echo "<h3>✅ Test Results Summary</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Location</th><th>Count</th><th>Status</th></tr>";
    echo "<tr><td>Dashboard Pending</td><td>$pending_quotes</td><td>" . ($pending_quotes > 0 ? '✅ Has quotes' : '❌ No quotes') . "</td></tr>";
    echo "<tr><td>Quotes Page All</td><td>" . count($quotes) . "</td><td>" . (count($quotes) > 0 ? '✅ Has quotes' : '❌ No quotes') . "</td></tr>";
    echo "<tr><td>Quotes Page Pending</td><td>" . count($pending_quotes_page) . "</td><td>" . (count($pending_quotes_page) > 0 ? '✅ Has quotes' : '❌ No quotes') . "</td></tr>";
    echo "</table>";
    
    if ($pending_quotes == count($pending_quotes_page)) {
        echo "<p style='color: green;'>🎉 SUCCESS! Dashboard and quotes page pending counts match!</p>";
    } else {
        echo "<p style='color: red;'>❌ MISMATCH! Dashboard shows $pending_quotes but quotes page shows " . count($pending_quotes_page) . "</p>";
    }
    
    if (count($quotes) > 0 || count($pending_quotes_page) > 0) {
        echo "<p style='color: green;'>✅ Contractors should now be able to see quotes!</p>";
        echo "<p><strong>Next steps:</strong></p>";
        echo "<ul>";
        echo "<li><a href='contractor/login.php' target='_blank'>Login as contractor</a></li>";
        echo "<li><a href='contractor/dashboard.php' target='_blank'>Check dashboard</a></li>";
        echo "<li><a href='contractor/quotes.php' target='_blank'>Check quotes page</a></li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ Still no quotes found. Check if:</p>";
        echo "<ul>";
        echo "<li>Quote requests exist in the database</li>";
        echo "<li>Contractor service areas and types are properly configured</li>";
        echo "<li>Service categories match between quotes and contractor profile</li>";
        echo "</ul>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
