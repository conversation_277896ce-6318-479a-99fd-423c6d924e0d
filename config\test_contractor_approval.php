<?php
require_once 'database.php';

echo "<h2>🔍 Contractor Approval System Test</h2>";

try {
    // Check if sample contractor exists
    $stmt = $pdo->prepare("SELECT id, email, user_type, status FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $user = $stmt->fetch();
    
    if (!$user) {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3>❌ Sample Contractor Not Found</h3>";
        echo "<p>Please run the sample contractor data script first:</p>";
        echo "<p><a href='add_sample_contractor.php'>Add Sample Contractor Data</a></p>";
        echo "</div>";
        exit();
    }
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>✅ Sample Contractor Found</h3>";
    echo "<p><strong>Email:</strong> " . htmlspecialchars($user['email']) . "</p>";
    echo "<p><strong>User Type:</strong> " . htmlspecialchars($user['user_type']) . "</p>";
    echo "<p><strong>Current Status:</strong> <span style='font-weight: bold; color: " . 
         ($user['status'] === 'approved' ? 'green' : ($user['status'] === 'pending' ? 'orange' : 'red')) . 
         ";'>" . htmlspecialchars($user['status']) . "</span></p>";
    echo "</div>";
    
    // Test different status scenarios
    echo "<div style='background: #cce5ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🧪 Test Approval System</h3>";
    echo "<p>Click the buttons below to test different contractor statuses:</p>";
    
    if (isset($_POST['set_status'])) {
        $new_status = $_POST['status'];
        $stmt = $pdo->prepare("UPDATE users SET status = ? WHERE id = ?");
        $stmt->execute([$new_status, $user['id']]);
        
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>Status Updated!</strong> Contractor status changed to: <strong>" . htmlspecialchars($new_status) . "</strong>";
        echo "</div>";
        
        // Refresh user data
        $stmt = $pdo->prepare("SELECT id, email, user_type, status FROM users WHERE email = '<EMAIL>'");
        $stmt->execute();
        $user = $stmt->fetch();
    }
    
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<input type='hidden' name='set_status' value='1'>";
    echo "<button type='submit' name='status' value='pending' style='background: orange; color: white; padding: 10px 15px; border: none; border-radius: 5px; margin: 5px;'>Set to Pending</button>";
    echo "<button type='submit' name='status' value='approved' style='background: green; color: white; padding: 10px 15px; border: none; border-radius: 5px; margin: 5px;'>Set to Approved</button>";
    echo "<button type='submit' name='status' value='rejected' style='background: red; color: white; padding: 10px 15px; border: none; border-radius: 5px; margin: 5px;'>Set to Rejected</button>";
    echo "<button type='submit' name='status' value='suspended' style='background: gray; color: white; padding: 10px 15px; border: none; border-radius: 5px; margin: 5px;'>Set to Suspended</button>";
    echo "</form>";
    echo "</div>";
    
    // Test login behavior
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🔐 Login Behavior Test</h3>";
    echo "<p>Current status: <strong>" . htmlspecialchars($user['status']) . "</strong></p>";
    
    switch ($user['status']) {
        case 'approved':
            echo "<p style='color: green;'>✅ <strong>APPROVED:</strong> Contractor can login and access all features</p>";
            break;
        case 'pending':
            echo "<p style='color: orange;'>⏳ <strong>PENDING:</strong> Contractor will see 'pending approval' message and be redirected to login</p>";
            break;
        case 'rejected':
            echo "<p style='color: red;'>❌ <strong>REJECTED:</strong> Contractor will see 'account rejected' message and be redirected to login</p>";
            break;
        case 'suspended':
            echo "<p style='color: gray;'>🚫 <strong>SUSPENDED:</strong> Contractor will see 'account suspended' message and be redirected to login</p>";
            break;
    }
    
    echo "<p><a href='../login.php' target='_blank' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Login Page</a></p>";
    echo "</div>";
    
    // Admin approval test
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>👨‍💼 Admin Approval Process</h3>";
    echo "<p>To test the admin approval workflow:</p>";
    echo "<ol>";
    echo "<li>Set contractor status to 'pending' using the buttons above</li>";
    echo "<li>Login to admin dashboard: <a href='../admin/login.php' target='_blank'>Admin Login</a></li>";
    echo "<li>Go to Contractors section and approve the contractor</li>";
    echo "<li>Try logging in as contractor again</li>";
    echo "</ol>";
    echo "<p><strong>Admin Credentials:</strong> <EMAIL> / admin123</p>";
    echo "</div>";
    
    // Current contractor profile check
    $stmt = $pdo->prepare("SELECT * FROM contractor_profiles WHERE user_id = ?");
    $stmt->execute([$user['id']]);
    $profile = $stmt->fetch();
    
    if ($profile) {
        echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3>📋 Contractor Profile Status</h3>";
        echo "<p><strong>Business Name:</strong> " . htmlspecialchars($profile['business_name']) . "</p>";
        echo "<p><strong>Contact Person:</strong> " . htmlspecialchars($profile['contact_person']) . "</p>";
        echo "<p><strong>CIDA Registration:</strong> " . htmlspecialchars($profile['cida_registration']) . "</p>";
        echo "<p><strong>CIDA Grade:</strong> " . htmlspecialchars($profile['cida_grade']) . "</p>";
        echo "<p>✅ Profile is complete and ready for approval</p>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Contractor Approval System Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h2, h3 { color: #333; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>🏗️ BrickClick Contractor Approval System</h1>
    <p>This page helps you test the contractor approval workflow.</p>
</body>
</html>
