<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : 'all';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Build query
$where_conditions = ["1=1"];
$params = [];

if ($status_filter !== 'all') {
    if ($status_filter === 'completed') {
        // Show quotes where customer has made downpayment
        $where_conditions[] = "qr.id IN (
            SELECT DISTINCT qres.quote_request_id
            FROM quote_responses qres
            JOIN project_payments pp ON qres.id = pp.quote_response_id
            WHERE pp.payment_type = 'down_payment'
            AND pp.payment_status = 'completed'
        )";
    } else {
        $where_conditions[] = "qr.status = ?";
        $params[] = $status_filter;
    }
}

if (!empty($search)) {
    $where_conditions[] = "(COALESCE(cp.first_name, '') LIKE ? OR COALESCE(cp.last_name, '') LIKE ? OR COALESCE(sc.name_en, '') LIKE ? OR COALESCE(qr.description, '') LIKE ? OR CONCAT(COALESCE(cp.first_name, ''), ' ', COALESCE(cp.last_name, '')) LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

$where_clause = implode(' AND ', $where_conditions);

// Get quote requests
try {
    $stmt = $pdo->prepare("
        SELECT qr.*, 
               cp.first_name, cp.last_name, cp.district,
               sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses qres WHERE qres.quote_request_id = qr.id) as response_count
        FROM quote_requests qr
        LEFT JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");
    $stmt->execute($params);
    $quotes = $stmt->fetchAll();
} catch (PDOException $e) {
    $quotes = [];
    $error = 'Database error: ' . $e->getMessage();
}

// Get status counts
try {
    // Get regular status counts
    $stmt = $pdo->query("
        SELECT
            status,
            COUNT(*) as count
        FROM quote_requests
        GROUP BY status
    ");
    $status_counts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

    // Get completed count (quotes with downpayments)
    $stmt = $pdo->query("
        SELECT COUNT(DISTINCT qr.id) as count
        FROM quote_requests qr
        JOIN quote_responses qres ON qr.id = qres.quote_request_id
        JOIN project_payments pp ON qres.id = pp.quote_response_id
        WHERE pp.payment_type = 'down_payment'
        AND pp.payment_status = 'completed'
    ");
    $completed_count = $stmt->fetchColumn();
    $status_counts['completed'] = $completed_count;

} catch (PDOException $e) {
    $status_counts = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Quote Requests - Admin Panel</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF8C00;
            --accent-yellow: #FFD700;
            --light-gray: #F8F9FA;
            --medium-gray: #6C757D;
            --success-green: #28A745;
            --warning-orange: #FFC107;
            --info-blue: #17A2B8;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f5f6fa;
            color: var(--primary-dark);
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-header h3 {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }
        
        .sidebar-header p {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 15px rgba(197, 23, 46, 0.3);
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--accent-orange), var(--accent-yellow));
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(255, 140, 0, 0.3);
        }
        
        .page-title {
            font-weight: 700;
            margin: 0;
            font-size: 2.5rem;
        }
        
        .page-subtitle {
            margin: 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .filters-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .status-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .status-tab {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .status-tab.all {
            background: linear-gradient(135deg, var(--medium-gray), #5a6268);
            color: white;
        }
        
        .status-tab.open {
            background: linear-gradient(135deg, var(--info-blue), #20c997);
            color: white;
        }
        
        .status-tab.in_progress {
            background: linear-gradient(135deg, var(--warning-orange), #e0a800);
            color: white;
        }
        
        .status-tab.completed {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            color: white;
        }
        
        .status-tab.cancelled {
            background: linear-gradient(135deg, var(--primary-red), #bd2130);
            color: white;
        }
        
        .status-tab.active {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .quote-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .quote-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .quote-header {
            display: flex;
            justify-content: between;
            align-items: start;
            margin-bottom: 1.5rem;
        }
        
        .quote-info h5 {
            color: var(--primary-dark);
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .quote-info p {
            color: var(--medium-gray);
            margin: 0;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .status-badge.open {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            color: #0c5460;
        }
        
        .status-badge.in_progress {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
        }
        
        .status-badge.completed {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }
        
        .status-badge.cancelled {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
        
        .quote-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            color: var(--medium-gray);
        }
        
        .detail-item i {
            width: 20px;
            margin-right: 0.5rem;
            color: var(--accent-orange);
        }
        
        .budget-amount {
            font-weight: 700;
            color: var(--success-green);
            font-size: 1.1rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .empty-state i {
            font-size: 4rem;
            color: var(--medium-gray);
            margin-bottom: 1rem;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-shield-alt me-2"></i>Admin Panel</h3>
            <p>Brick & Click Management</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="contractors.php" class="nav-link">
                    <i class="fas fa-hard-hat"></i>Contractors
                </a>
            </div>
            <div class="nav-item">
                <a href="customers.php" class="nav-link">
                    <i class="fas fa-users"></i>Customers
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link active">
                    <i class="fas fa-file-invoice"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews
                </a>
            </div>
            <div class="nav-item">
                <a href="payments.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payments
                </a>
            </div>
            <div class="nav-item">
                <a href="reports.php" class="nav-link">
                    <i class="fas fa-chart-bar"></i>Reports
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Quote Requests</h1>
            <p class="page-subtitle">Monitor and manage customer quote requests</p>
        </div>

        <!-- Filters -->
        <div class="filters-card">
            <div class="status-tabs">
                <a href="quotes.php" class="status-tab all <?php echo $status_filter === 'all' ? 'active' : ''; ?>">
                    All Quotes (<?php echo array_sum($status_counts); ?>)
                </a>
                <a href="quotes.php?status=open" class="status-tab open <?php echo $status_filter === 'open' ? 'active' : ''; ?>">
                    Open (<?php echo $status_counts['open'] ?? 0; ?>)
                </a>
                <!--<a href="quotes.php?status=in_progress" class="status-tab in_progress <?php echo $status_filter === 'in_progress' ? 'active' : ''; ?>">
                    In Progress (<?php echo $status_counts['in_progress'] ?? 0; ?>)
                </a> -->
                <a href="quotes.php?status=completed" class="status-tab completed <?php echo $status_filter === 'completed' ? 'active' : ''; ?>">
                    Down Payment Completed (<?php echo $status_counts['completed'] ?? 0; ?>)
                </a>
                <a href="quotes.php?status=cancelled" class="status-tab cancelled <?php echo $status_filter === 'cancelled' ? 'active' : ''; ?>">
                    Cancelled (<?php echo $status_counts['cancelled'] ?? 0; ?>)
                </a>
            </div>

            <form method="GET" class="row g-3">
                <input type="hidden" name="status" value="<?php echo htmlspecialchars($status_filter); ?>">
                <div class="col-md-8">
                    <input type="text" class="form-control" name="search" placeholder="Search by customer name, service category, or description..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                </div>
            </form>
        </div>

        <!-- Quote Requests List -->
        <?php if (empty($quotes)): ?>
            <div class="empty-state">
                <i class="fas fa-file-invoice"></i>
                <h3>No quote requests found</h3>
                <p class="text-muted">No quote requests match your current filters.</p>
            </div>
        <?php else: ?>
            <?php foreach ($quotes as $quote): ?>
                <div class="quote-card">
                    <div class="quote-header">
                        <div class="quote-info">
                            <h5><?php echo htmlspecialchars($quote['service_category']); ?></h5>
                            <p>Customer: <?php echo htmlspecialchars(($quote['first_name'] ?? '') . ' ' . ($quote['last_name'] ?? '')); ?></p>
                        </div>
                        <span class="status-badge <?php echo $quote['status']; ?>">
                            <?php echo ucfirst(str_replace('_', ' ', $quote['status'])); ?>
                        </span>
                    </div>

                    <div class="quote-details">
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span><?php echo htmlspecialchars($quote['district'] ?? 'N/A'); ?></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-dollar-sign"></i>
                            <span class="budget-amount">LKR <?php echo number_format($quote['estimated_budget'] ?? 0); ?></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-reply"></i>
                            <span><?php echo $quote['response_count']; ?> responses</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-calendar"></i>
                            <span><?php echo date('M j, Y', strtotime($quote['created_at'])); ?></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Project Timeline: <?php echo !empty($quote['project_timeline']) ? htmlspecialchars($quote['project_timeline']) : 'Not specified'; ?></span>
                        </div>
                    </div>

                    <?php if (!empty($quote['description'])): ?>
                        <div class="mt-3">
                            <strong>Project Description:</strong>
                            <p class="text-muted mt-1"><?php echo htmlspecialchars(substr($quote['description'], 0, 200)) . (strlen($quote['description']) > 200 ? '...' : ''); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
