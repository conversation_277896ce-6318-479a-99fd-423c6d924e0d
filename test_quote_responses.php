<?php
session_start();
require_once 'config/database.php';

echo "<h2>Quote Responses Test</h2>";

// Simulate being logged in as a customer
$_SESSION['user_id'] = 1; // Assuming customer ID 1 exists
$_SESSION['user_type'] = 'customer';

// Get a quote ID from URL or use a default
$quote_id = (int)($_GET['id'] ?? 1);

echo "<h3>Testing Quote ID: $quote_id</h3>";

try {
    // Test 1: Check if quote request exists
    echo "<h4>Test 1: Quote Request Details</h4>";
    $stmt = $pdo->prepare("SELECT * FROM quote_requests WHERE id = ?");
    $stmt->execute([$quote_id]);
    $quote_basic = $stmt->fetch();
    
    if ($quote_basic) {
        echo "Quote found:<br>";
        echo "ID: " . $quote_basic['id'] . "<br>";
        echo "Customer ID: " . $quote_basic['customer_id'] . "<br>";
        echo "Service Category ID: " . $quote_basic['service_category_id'] . "<br>";
        echo "Title: " . $quote_basic['title'] . "<br>";
        echo "Status: " . $quote_basic['status'] . "<br>";
    } else {
        echo "Quote not found<br>";
    }

    // Test 2: Check service category
    echo "<h4>Test 2: Service Category</h4>";
    if ($quote_basic) {
        $stmt = $pdo->prepare("SELECT * FROM service_categories WHERE id = ?");
        $stmt->execute([$quote_basic['service_category_id']]);
        $service_cat = $stmt->fetch();
        
        if ($service_cat) {
            echo "Service category found:<br>";
            echo "ID: " . $service_cat['id'] . "<br>";
            echo "Name EN: " . ($service_cat['name_en'] ?? 'NULL') . "<br>";
        } else {
            echo "Service category not found for ID: " . $quote_basic['service_category_id'] . "<br>";
        }
    }

    // Test 3: The original failing query
    echo "<h4>Test 3: Original Query (with JOIN)</h4>";
    try {
        $stmt = $pdo->prepare("
            SELECT qr.*, sc.name_en as service_name
            FROM quote_requests qr 
            JOIN service_categories sc ON qr.service_category_id = sc.id
            WHERE qr.id = ? AND qr.customer_id = ?
        ");
        $stmt->execute([$quote_id, $_SESSION['user_id']]);
        $quote = $stmt->fetch();
        
        if ($quote) {
            echo "Original query successful:<br>";
            echo "Title: " . $quote['title'] . "<br>";
            echo "Service Name: " . $quote['service_name'] . "<br>";
        } else {
            echo "Original query returned no results<br>";
        }
    } catch (PDOException $e) {
        echo "Original query failed: " . $e->getMessage() . "<br>";
    }

    // Test 4: Quote responses
    echo "<h4>Test 4: Quote Responses</h4>";
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM quote_responses WHERE quote_request_id = ?");
    $stmt->execute([$quote_id]);
    $response_count = $stmt->fetch()['count'];
    echo "Response count: $response_count<br>";

    if ($response_count > 0) {
        // Test the complex query
        try {
            $stmt = $pdo->prepare("
                SELECT qres.*, cp.business_name, cp.contact_person, cp.phone, cp.profile_image,
                       cp.average_rating, cp.total_reviews, cp.cida_grade,
                       pp.payment_status, pp.amount as paid_amount, pp.payment_date, pp.id as payment_id,
                       r.id as review_id, r.rating as review_rating, r.review_text
                FROM quote_responses qres
                JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
                LEFT JOIN project_payments pp ON qres.id = pp.quote_response_id AND pp.payment_type = 'down_payment'
                LEFT JOIN reviews r ON pp.id = r.payment_id AND r.customer_id = ?
                WHERE qres.quote_request_id = ?
                ORDER BY qres.created_at DESC
            ");
            $stmt->execute([$_SESSION['user_id'], $quote_id]);
            $responses = $stmt->fetchAll();
            
            echo "Complex query successful. Found " . count($responses) . " responses:<br>";
            foreach ($responses as $response) {
                echo "- Response ID: " . $response['id'] . ", Business: " . ($response['business_name'] ?? 'NULL') . ", Amount: " . $response['quoted_amount'] . "<br>";
            }
        } catch (PDOException $e) {
            echo "Complex query failed: " . $e->getMessage() . "<br>";
        }
    }

    // Test 5: Check all tables exist
    echo "<h4>Test 5: Table Existence</h4>";
    $tables = ['quote_requests', 'quote_responses', 'contractor_profiles', 'service_categories', 'project_payments', 'reviews'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "$table: $count records<br>";
        } catch (PDOException $e) {
            echo "$table: ERROR - " . $e->getMessage() . "<br>";
        }
    }

} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage();
}
?>
