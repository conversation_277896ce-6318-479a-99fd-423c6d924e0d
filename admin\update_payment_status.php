<?php
session_start();
require_once '../config/database.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit();
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
    exit();
}

$payment_id = isset($input['payment_id']) ? (int)$input['payment_id'] : 0;
$new_status = isset($input['status']) ? trim($input['status']) : '';

// Validate input
if (!$payment_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid payment ID']);
    exit();
}

$allowed_statuses = ['completed', 'pending', 'failed', 'refunded'];
if (!in_array($new_status, $allowed_statuses)) {
    echo json_encode(['success' => false, 'message' => 'Invalid status']);
    exit();
}

try {
    // Check if payment exists
    $stmt = $pdo->prepare("SELECT id, payment_status FROM project_payments WHERE id = ?");
    $stmt->execute([$payment_id]);
    $payment = $stmt->fetch();
    
    if (!$payment) {
        echo json_encode(['success' => false, 'message' => 'Payment not found']);
        exit();
    }
    
    // Update payment status
    $update_fields = ['payment_status = ?'];
    $update_params = [$new_status];
    
    // If marking as completed and payment_date is null, set it to current timestamp
    if ($new_status === 'completed' && empty($payment['payment_date'])) {
        $update_fields[] = 'payment_date = NOW()';
    }
    
    $sql = "UPDATE project_payments SET " . implode(', ', $update_fields) . " WHERE id = ?";
    $update_params[] = $payment_id;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($update_params);
    
    if ($stmt->rowCount() > 0) {
        // Log the admin action (optional - you can create an admin_logs table for this)
        $log_stmt = $pdo->prepare("
            INSERT INTO admin_logs (admin_id, action, target_type, target_id, details, created_at) 
            VALUES (?, 'payment_status_update', 'payment', ?, ?, NOW())
        ");
        $log_details = json_encode([
            'old_status' => $payment['payment_status'],
            'new_status' => $new_status,
            'payment_id' => $payment_id
        ]);
        
        // Try to log, but don't fail if admin_logs table doesn't exist
        try {
            $log_stmt->execute([$_SESSION['user_id'], $payment_id, $log_details]);
        } catch (PDOException $e) {
            // Admin logs table might not exist, continue without logging
        }
        
        echo json_encode([
            'success' => true, 
            'message' => 'Payment status updated successfully',
            'new_status' => $new_status
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'No changes made']);
    }
    
} catch (PDOException $e) {
    error_log('Payment status update error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
}
?>
