<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a contractor
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'contractor') {
    header('Location: ../login.php');
    exit();
}

// Check if profile already exists
try {
    $stmt = $pdo->prepare("SELECT id FROM contractor_profiles WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    if ($stmt->fetch()) {
        header('Location: dashboard.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
}

// Get service categories from database
try {
    $stmt = $pdo->prepare("SELECT id, name_en FROM service_categories WHERE is_active = 1 ORDER BY id");
    $stmt->execute();
    $service_categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $service_categories = [];
}

// Districts in Sri Lanka
$districts = [
    'Ampara', 'Anuradhapura', 'Badulla', 'Batticaloa', 'Colombo', 'Galle', 'Gampaha', 
    'Hambantota', 'Jaffna', 'Kalutara', 'Kandy', 'Kegalle', 'Kilinochchi', 'Kurunegala', 
    'Mannar', 'Matale', 'Matara', 'Monaragala', 'Mullaitivu', 'Nuwara Eliya', 'Polonnaruwa', 
    'Puttalam', 'Ratnapura', 'Trincomalee', 'Vavuniya'
];

// CIDA grades
$cida_grades = ['C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10'];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $business_name = trim($_POST['business_name']);
    $contact_person = trim($_POST['contact_person']);
    $phone = trim($_POST['phone']);
    $business_address = trim($_POST['business_address']);
    $service_areas = $_POST['service_areas'] ?? [];
    $service_types = $_POST['service_types'] ?? [];
    $cida_registration = trim($_POST['cida_registration']);
    $cida_grade = $_POST['cida_grade'];
    $business_description = trim($_POST['business_description']);
    $website = trim($_POST['website']);
    
    $errors = [];
    
    // Validation
    if (empty($business_name)) $errors[] = 'Business name is required.';
    if (empty($contact_person)) $errors[] = 'Contact person is required.';
    if (empty($phone)) $errors[] = 'Phone number is required.';
    if (empty($business_address)) $errors[] = 'Business address is required.';
    if (empty($service_areas)) $errors[] = 'At least one service area is required.';
    if (empty($service_types)) $errors[] = 'At least one service type is required.';
    if (empty($cida_registration)) $errors[] = 'CIDA registration number is required.';
    if (empty($cida_grade)) $errors[] = 'CIDA grade is required.';
    if (empty($business_description)) $errors[] = 'Business description is required.';
    
    // Phone validation
    if (!preg_match('/^[0-9+\-\s()]+$/', $phone)) {
        $errors[] = 'Invalid phone number format.';
    }
    
    // Website validation
    if (!empty($website) && !filter_var($website, FILTER_VALIDATE_URL)) {
        $errors[] = 'Invalid website URL.';
    }
    
    if (empty($errors)) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO contractor_profiles (
                    user_id, business_name, contact_person, phone, business_address,
                    service_areas, service_types, cida_registration, cida_grade,
                    business_description, website, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            
            $stmt->execute([
                $_SESSION['user_id'],
                $business_name,
                $contact_person,
                $phone,
                $business_address,
                json_encode($service_areas),
                json_encode($service_types),
                $cida_registration,
                $cida_grade,
                $business_description,
                $website ?: null
            ]);
            
            $_SESSION['success'] = 'Profile created successfully! Your account is pending admin approval.';
            header('Location: dashboard.php');
            exit();
            
        } catch (PDOException $e) {
            $errors[] = 'Database error. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Setup Profile - BrickClick Contractor</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet"> 
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF6B35;
            --accent-yellow: #F7931E;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
        }
        
        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e9ecef 100%);
            font-family: 'Open Sans', sans-serif;
            min-height: 100vh;
        }
        
        .setup-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .setup-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .setup-header {
            background: linear-gradient(135deg, var(--primary-dark), #0a2a3a);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        
        .setup-header h2 {
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .setup-header p {
            opacity: 0.9;
            margin: 0;
        }
        
        .setup-form {
            padding: 3rem 2rem;
        }
        
        .form-section {
            margin-bottom: 2.5rem;
        }
        
        .section-title {
            color: var(--primary-dark);
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--accent-orange);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            color: var(--primary-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--accent-orange);
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }
        
        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
        }
        
        .form-check {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }
        
        .form-check:hover {
            background: #e9ecef;
        }
        
        .form-check-input:checked {
            background-color: var(--accent-orange);
            border-color: var(--accent-orange);
        }
        
        .btn-setup {
            background: linear-gradient(135deg, var(--accent-orange), #e55a2b);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-setup:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
        
        .required {
            color: var(--primary-red);
        }
    </style>
</head>

<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="setup-header">
                <h2>Complete Your Contractor Profile</h2>
                <p>Please provide your business information to get started</p>
            </div>
            
            <div class="setup-form">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <!-- Business Information -->
                    <div class="form-section">
                        <h3 class="section-title">Business Information</h3>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Business Name <span class="required">*</span></label>
                                    <input type="text" class="form-control" name="business_name" 
                                           value="<?php echo htmlspecialchars($_POST['business_name'] ?? ''); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Contact Person <span class="required">*</span></label>
                                    <input type="text" class="form-control" name="contact_person" 
                                           value="<?php echo htmlspecialchars($_POST['contact_person'] ?? ''); ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Phone Number <span class="required">*</span></label>
                                    <input type="tel" class="form-control" name="phone" 
                                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Website (Optional)</label>
                                    <input type="url" class="form-control" name="website" 
                                           value="<?php echo htmlspecialchars($_POST['website'] ?? ''); ?>" 
                                           placeholder="https://www.example.com">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Business Address <span class="required">*</span></label>
                            <textarea class="form-control" name="business_address" rows="3" required><?php echo htmlspecialchars($_POST['business_address'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Business Description <span class="required">*</span></label>
                            <textarea class="form-control" name="business_description" rows="4" 
                                      placeholder="Describe your business, experience, and specializations..." required><?php echo htmlspecialchars($_POST['business_description'] ?? ''); ?></textarea>
                        </div>
                    </div>
                    
                    <!-- CIDA Information -->
                    <div class="form-section">
                        <h3 class="section-title">CIDA Registration</h3>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">CIDA Registration Number <span class="required">*</span></label>
                                    <input type="text" class="form-control" name="cida_registration" 
                                           value="<?php echo htmlspecialchars($_POST['cida_registration'] ?? ''); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">CIDA Grade <span class="required">*</span></label>
                                    <select class="form-select" name="cida_grade" required>
                                        <option value="">Select CIDA Grade</option>
                                        <?php foreach ($cida_grades as $grade): ?>
                                            <option value="<?php echo $grade; ?>" 
                                                    <?php echo (($_POST['cida_grade'] ?? '') === $grade) ? 'selected' : ''; ?>>
                                                <?php echo $grade; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Service Areas -->
                    <div class="form-section">
                        <h3 class="section-title">Service Areas <span class="required">*</span></h3>
                        <p class="text-muted mb-3">Select the districts where you provide services:</p>
                        
                        <div class="checkbox-grid">
                            <?php foreach ($districts as $district): ?>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="service_areas[]" 
                                           value="<?php echo $district; ?>" id="area_<?php echo $district; ?>"
                                           <?php echo in_array($district, $_POST['service_areas'] ?? []) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="area_<?php echo $district; ?>">
                                        <?php echo $district; ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Service Types -->
                    <div class="form-section">
                        <h3 class="section-title">Service Types <span class="required">*</span></h3>
                        <p class="text-muted mb-3">Select the types of construction services you provide:</p>
                        
                        <div class="checkbox-grid">
                            <?php foreach ($service_categories as $category): ?>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="service_types[]"
                                           value="<?php echo $category['id']; ?>" id="service_<?php echo $category['id']; ?>"
                                           <?php echo in_array($category['id'], $_POST['service_types'] ?? []) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="service_<?php echo $category['id']; ?>">
                                        <?php echo htmlspecialchars($category['name_en']); ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-setup">
                        <i class="fas fa-check me-2"></i>Complete Profile Setup
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
