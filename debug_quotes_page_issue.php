<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔍 Debug Contractor Quotes Page Issues</h2>";

try {
    // Get a test contractor
    $stmt = $pdo->query("
        SELECT u.id, u.email, cp.business_name, cp.service_areas, cp.service_types, u.status
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved' 
        LIMIT 1
    ");
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p style='color: red;'>❌ No approved contractors found.</p>";
        exit;
    }
    
    $contractor_id = $contractor['id'];
    echo "<p><strong>Testing with contractor:</strong> " . htmlspecialchars($contractor['business_name']) . " (ID: $contractor_id)</p>";
    echo "<p><strong>Service Areas:</strong> " . htmlspecialchars($contractor['service_areas']) . "</p>";
    echo "<p><strong>Service Types:</strong> " . htmlspecialchars($contractor['service_types']) . "</p>";
    
    // Simulate session
    $_SESSION['user_id'] = $contractor_id;
    $_SESSION['user_type'] = 'contractor';
    
    // Test different status filters
    $status_filters = ['all', 'pending', 'responded', 'completed', 'cancelled'];
    
    foreach ($status_filters as $status_filter) {
        echo "<h3>Testing Status Filter: '$status_filter'</h3>";
        
        // Build query conditions (exact copy from quotes.php)
        $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
        $params = [$contractor_id];
        
        if ($status_filter !== 'all') {
            if ($status_filter === 'responded') {
                $where_conditions[] = "qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?)";
                $params[] = $contractor_id;
            } elseif ($status_filter === 'pending') {
                $where_conditions[] = "qr.status = 'open' AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)";
                $params[] = $contractor_id;
            } elseif ($status_filter === 'completed') {
                $where_conditions[] = "qr.id IN (
                    SELECT qres.quote_request_id
                    FROM quote_responses qres
                    JOIN project_payments pp ON qres.id = pp.quote_response_id
                    WHERE qres.contractor_id = ?
                    AND pp.payment_type = 'down_payment'
                    AND pp.payment_status = 'completed'
                )";
                $params[] = $contractor_id;
            } elseif ($status_filter === 'cancelled') {
                $where_conditions[] = "qr.id IN (
                    SELECT quote_request_id
                    FROM quote_responses
                    WHERE contractor_id = ?
                    AND status = 'rejected'
                )";
                $params[] = $contractor_id;
            }
        } else {
            $where_conditions[] = "(qr.status IN ('open', 'completed', 'cancelled') OR qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?))";
            $params[] = $contractor_id;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        // Execute query (exact copy from quotes.php)
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
                   (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
                   (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
                   (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
                   CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
                   cp_contractor.service_types, cp_contractor.service_areas
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
            JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
            WHERE $where_clause
            ORDER BY qr.created_at DESC
        ");
        
        $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
        $stmt->execute($query_params);
        $all_quotes = $stmt->fetchAll();
        
        echo "<p>SQL query returned: " . count($all_quotes) . " quotes</p>";
        
        // Filter quotes in PHP (exact copy from quotes.php)
        $quotes = [];
        foreach ($all_quotes as $quote) {
            $match_reason = "";
            $is_match = false;
            
            // Always show direct quotes for this contractor
            if ($quote['specific_contractor_id'] == $contractor_id) {
                $quotes[] = $quote;
                $is_match = true;
                $match_reason = "Direct quote for this contractor";
            }
            // For general quotes (no specific contractor), check service and area match
            elseif ($quote['specific_contractor_id'] === null) {
                $contractor_services = json_decode($quote['service_types'], true) ?: [];
                $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
                
                $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                              in_array((string)$quote['service_category_id'], $contractor_services);
                $has_area = in_array($quote['district'], $contractor_areas);
                
                if ($has_service && $has_area) {
                    $quotes[] = $quote;
                    $is_match = true;
                    $match_reason = "General quote - service and area match";
                } else {
                    $match_reason = "General quote - no match (service: " . ($has_service ? 'yes' : 'no') . ", area: " . ($has_area ? 'yes' : 'no') . ")";
                }
            } else {
                $match_reason = "Direct quote for another contractor (ID: " . $quote['specific_contractor_id'] . ")";
            }
            
            if ($is_match) {
                echo "<div style='border: 1px solid green; padding: 5px; margin: 2px;'>";
                echo "<strong>Quote ID:</strong> " . $quote['id'] . " | ";
                echo "<strong>Title:</strong> " . htmlspecialchars($quote['title']) . " | ";
                echo "<strong>Status:</strong> " . $quote['status'] . " | ";
                echo "<strong>Type:</strong> " . $quote['quote_type'] . " | ";
                echo "<strong>Has Responded:</strong> " . ($quote['has_responded'] ? 'Yes' : 'No') . " | ";
                echo "<strong>My Quote Status:</strong> " . ($quote['my_quote_status'] ?: 'None') . "<br>";
                echo "<strong>Reason:</strong> " . $match_reason;
                echo "</div>";
            }
        }
        
        echo "<p><strong>Final filtered quotes for '$status_filter':</strong> " . count($quotes) . "</p>";
        echo "<hr>";
    }
    
    // Test quote counts (exact copy from quotes.php)
    echo "<h3>Testing Quote Counts</h3>";
    
    $stmt = $pdo->prepare("
        SELECT qr.id, qr.status, qr.specific_contractor_id, qr.service_category_id, qr.district,
               cp_contractor.service_types, cp_contractor.service_areas,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT COUNT(*) FROM quote_responses qres
                JOIN project_payments pp ON qres.id = pp.quote_response_id
                WHERE qres.quote_request_id = qr.id AND qres.contractor_id = ?
                AND pp.payment_type = 'down_payment' AND pp.payment_status = 'completed') as has_payment,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? AND status = 'rejected') as is_rejected
        FROM quote_requests qr
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND (qr.status IN ('open', 'completed', 'cancelled') OR qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?))
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id]);
    $all_count_quotes = $stmt->fetchAll();
    
    echo "<p>Count query returned: " . count($all_count_quotes) . " quotes</p>";
    
    // Filter and count in PHP (exact copy from quotes.php)
    $total = 0;
    $pending = 0;
    $responded = 0;
    $completed = 0;
    $cancelled = 0;
    
    foreach ($all_count_quotes as $quote) {
        $should_include = false;
        
        // Always include direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $should_include = true;
        }
        // For general quotes, check service and area match
        elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            $should_include = ($has_service && $has_area);
        }
        
        if ($should_include) {
            $total++;
            
            if ($quote['status'] === 'open' && $quote['has_responded'] == 0) {
                $pending++;
            }
            if ($quote['has_responded'] > 0) {
                $responded++;
            }
            if ($quote['has_payment'] > 0) {
                $completed++;
            }
            if ($quote['is_rejected'] > 0) {
                $cancelled++;
            }
        }
    }
    
    echo "<p><strong>Quote Counts:</strong></p>";
    echo "<ul>";
    echo "<li>Total: $total</li>";
    echo "<li>Pending: $pending</li>";
    echo "<li>Responded: $responded</li>";
    echo "<li>Completed: $completed</li>";
    echo "<li>Cancelled: $cancelled</li>";
    echo "</ul>";
    
    // Check for potential issues
    echo "<h3>Potential Issues Analysis</h3>";
    
    if ($total == 0) {
        echo "<p style='color: red;'>❌ No quotes found for this contractor</p>";
        echo "<p><strong>Possible causes:</strong></p>";
        echo "<ul>";
        echo "<li>No quotes in the system</li>";
        echo "<li>Contractor service areas/types don't match any quotes</li>";
        echo "<li>Database query issues</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: green;'>✅ Found $total quotes for this contractor</p>";
    }
    
    // Check if there are quotes in the system at all
    $stmt = $pdo->query("SELECT COUNT(*) FROM quote_requests");
    $total_quotes_in_system = $stmt->fetchColumn();
    
    echo "<p><strong>Total quotes in system:</strong> $total_quotes_in_system</p>";
    
    if ($total_quotes_in_system == 0) {
        echo "<p style='color: orange;'>⚠️ No quotes in the system at all</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
