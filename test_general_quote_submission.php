<?php
session_start();
require_once 'config/database.php';

echo "<h2>🧪 Test General Quote Submission</h2>";

try {
    // Step 1: Get or create a test customer
    $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' AND status = 'approved' LIMIT 1");
    $customer_id = $stmt->fetchColumn();
    
    if (!$customer_id) {
        echo "<p style='color: red;'>❌ No approved customer found. Creating test customer...</p>";
        
        // Create test customer
        $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, 'customer', 'approved')");
        $stmt->execute(['<EMAIL>', password_hash('password', PASSWORD_DEFAULT)]);
        $customer_id = $pdo->lastInsertId();
        
        // Create customer profile
        $stmt = $pdo->prepare("INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$customer_id, 'Test', 'Customer', '0771234567', 'Colombo']);
        
        echo "<p style='color: green;'>✅ Created test customer (ID: $customer_id)</p>";
    } else {
        echo "<p style='color: green;'>✅ Using existing customer (ID: $customer_id)</p>";
    }
    
    // Step 2: Check contractors
    $stmt = $pdo->query("
        SELECT u.id, cp.business_name, cp.service_areas, cp.service_types
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        AND cp.service_areas IS NOT NULL AND cp.service_areas != '' AND cp.service_areas != '[]'
        AND cp.service_types IS NOT NULL AND cp.service_types != '' AND cp.service_types != '[]'
    ");
    $contractors = $stmt->fetchAll();
    
    echo "<p>Available contractors: " . count($contractors) . "</p>";
    
    if (count($contractors) == 0) {
        echo "<p style='color: red;'>❌ No contractors with complete data found. Cannot test general quotes.</p>";
        exit;
    }
    
    // Display contractor info
    foreach ($contractors as $contractor) {
        $areas = json_decode($contractor['service_areas'], true);
        $services = json_decode($contractor['service_types'], true);
        echo "<p><strong>" . htmlspecialchars($contractor['business_name']) . " (ID: {$contractor['id']})</strong><br>";
        echo "Areas: " . implode(', ', $areas) . "<br>";
        echo "Services: " . implode(', ', $services) . "</p>";
    }
    
    // Step 3: Simulate general quote submission
    echo "<h3>Step 3: Simulate General Quote Submission</h3>";
    
    // Use data that should match the first contractor
    $first_contractor = $contractors[0];
    $contractor_areas = json_decode($first_contractor['service_areas'], true);
    $contractor_services = json_decode($first_contractor['service_types'], true);
    
    $test_district = $contractor_areas[0]; // Use first area
    $test_service_id = $contractor_services[0]; // Use first service
    
    echo "<p>Testing with District: <strong>$test_district</strong>, Service ID: <strong>$test_service_id</strong></p>";
    
    // Simulate the process_quote_request.php logic
    $pdo->beginTransaction();
    
    // Insert quote request (general quote - no specific_contractor_id)
    $stmt = $pdo->prepare("
        INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, specific_contractor_id, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'open')
    ");
    
    $title = "Test General Quote - " . date('Y-m-d H:i:s');
    $description = "This is a test general quote to verify the system is working.";
    $location = "Test Location, $test_district";
    $estimated_budget = 1000000;
    $project_timeline = '3 months';
    
    $stmt->execute([
        $customer_id, 
        $test_service_id, 
        $title, 
        $description, 
        $location, 
        $test_district, 
        $estimated_budget, 
        $project_timeline, 
        null, // specific_contractor_id = NULL for general quotes
    ]);
    
    $quote_request_id = $pdo->lastInsertId();
    echo "<p style='color: green;'>✅ Created quote request (ID: $quote_request_id)</p>";
    
    // Step 4: Find matching contractors (same logic as process_quote_request.php)
    echo "<h3>Step 4: Find Matching Contractors</h3>";
    
    $stmt = $pdo->prepare("
        SELECT u.id, cp.business_name, cp.contact_person, cp.service_areas, cp.service_types
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.status = 'approved'
        AND u.user_type = 'contractor'
        AND cp.service_areas IS NOT NULL
        AND cp.service_areas != ''
        AND cp.service_types IS NOT NULL
        AND cp.service_types != ''
        AND (
            JSON_CONTAINS(cp.service_areas, ?)
            OR cp.service_areas LIKE ?
        )
        AND (
            JSON_CONTAINS(cp.service_types, ?)
            OR cp.service_types LIKE ?
        )
    ");

    $district_json = json_encode($test_district);
    $service_json = json_encode($test_service_id);
    $district_like = "%\"$test_district\"%";
    $service_like = "%\"$test_service_id\"%";

    $stmt->execute([
        $district_json,
        $district_like,
        $service_json,
        $service_like
    ]);

    $potential_contractors = $stmt->fetchAll();
    echo "<p>SQL query found " . count($potential_contractors) . " potential contractors</p>";

    // Filter results in PHP to ensure proper matching
    $contractors_to_notify = [];
    foreach ($potential_contractors as $contractor) {
        $service_areas = json_decode($contractor['service_areas'], true);
        $service_types = json_decode($contractor['service_types'], true);

        $has_area = false;
        $has_service = false;

        // Check service areas
        if (is_array($service_areas)) {
            $has_area = in_array($test_district, $service_areas);
        }

        // Check service types (handle both string and integer formats)
        if (is_array($service_types)) {
            $has_service = in_array($test_service_id, $service_types) ||
                          in_array((string)$test_service_id, $service_types);
        }

        if ($has_area && $has_service) {
            $contractors_to_notify[] = [
                'id' => $contractor['id'],
                'business_name' => $contractor['business_name'],
                'contact_person' => $contractor['contact_person']
            ];
            echo "<p>✅ Matched contractor: " . htmlspecialchars($contractor['business_name']) . " (Area: " . ($has_area ? "✓" : "✗") . ", Service: " . ($has_service ? "✓" : "✗") . ")</p>";
        } else {
            echo "<p>❌ No match: " . htmlspecialchars($contractor['business_name']) . " (Area: " . ($has_area ? "✓" : "✗") . ", Service: " . ($has_service ? "✓" : "✗") . ")</p>";
        }
    }

    echo "<p><strong>Final result: " . count($contractors_to_notify) . " contractors will be notified</strong></p>";
    
    // Step 5: Create notifications
    if (count($contractors_to_notify) > 0) {
        echo "<h3>Step 5: Create Notifications</h3>";
        
        foreach ($contractors_to_notify as $contractor) {
            $notification_title = "New Quote Request";
            $notification_message = "You have received a new quote request for: $title in $location, $test_district";
            
            $stmt = $pdo->prepare("
                INSERT INTO notifications (user_id, title, message, type, related_id) 
                VALUES (?, ?, ?, 'quote_received', ?)
            ");
            $stmt->execute([$contractor['id'], $notification_title, $notification_message, $quote_request_id]);
            
            echo "<p>✅ Created notification for: " . htmlspecialchars($contractor['business_name']) . "</p>";
        }
    }
    
    $pdo->commit();
    
    // Step 6: Test contractor dashboard visibility
    if (count($contractors_to_notify) > 0) {
        echo "<h3>Step 6: Test Contractor Dashboard Visibility</h3>";
        
        $test_contractor = $contractors_to_notify[0];
        $contractor_id = $test_contractor['id'];
        
        echo "<p>Testing visibility for: " . htmlspecialchars($test_contractor['business_name']) . " (ID: $contractor_id)</p>";
        
        // Test the query from contractor quotes page
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, cp.district, sc.name_en as service_category,
                   (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
                   CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
            WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
            AND qr.status = 'open' 
            AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)
            AND qr.id = ?
        ");
        
        $stmt->execute([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $quote_request_id]);
        $quote_result = $stmt->fetch();
        
        if ($quote_result) {
            echo "<p style='color: green;'>✅ Quote is visible in contractor's dashboard!</p>";
            echo "<p>Quote details: " . htmlspecialchars($quote_result['title']) . " - " . htmlspecialchars($quote_result['service_category']) . "</p>";
        } else {
            echo "<p style='color: red;'>❌ Quote is NOT visible in contractor's dashboard</p>";
        }
    }
    
    echo "<h3>Summary</h3>";
    echo "<p>✅ Quote request created successfully</p>";
    echo "<p>✅ " . count($contractors_to_notify) . " contractors notified</p>";
    echo "<p>✅ Notifications created</p>";
    echo "<p><strong>Test completed successfully!</strong></p>";
    
} catch (PDOException $e) {
    $pdo->rollBack();
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
