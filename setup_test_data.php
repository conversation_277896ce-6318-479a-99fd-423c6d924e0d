<?php
require_once 'config/database.php';

echo "<h2>🔧 Setting Up Test Data for Contractor Dashboard</h2>";

try {
    $pdo->beginTransaction();
    
    // 1. Create or verify contractor user exists
    echo "<h3>1. Setting up Contractor User</h3>";
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $contractor_user = $stmt->fetch();
    
    if (!$contractor_user) {
        echo "<p>Creating contractor user...</p>";
        $stmt = $pdo->prepare("
            INSERT INTO users (email, password, user_type, status, created_at, updated_at) 
            VALUES ('<EMAIL>', ?, 'contractor', 'approved', NOW(), NOW())
        ");
        $stmt->execute([password_hash('password', PASSWORD_DEFAULT)]);
        $contractor_id = $pdo->lastInsertId();
        echo "<p>✅ Created contractor user with ID: $contractor_id</p>";
    } else {
        $contractor_id = $contractor_user['id'];
        echo "<p>✅ Contractor user exists with ID: $contractor_id</p>";
    }
    
    // 2. Create or verify contractor profile
    echo "<h3>2. Setting up Contractor Profile</h3>";
    $stmt = $pdo->prepare("SELECT id FROM contractor_profiles WHERE user_id = ?");
    $stmt->execute([$contractor_id]);
    $profile = $stmt->fetch();
    
    if (!$profile) {
        echo "<p>Creating contractor profile...</p>";
        $stmt = $pdo->prepare("
            INSERT INTO contractor_profiles (
                user_id, business_name, contact_person, phone, business_address,
                service_areas, service_types, cida_registration, cida_grade,
                business_description, average_rating, total_reviews, total_projects,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        $stmt->execute([
            $contractor_id,
            'Elite Construction Solutions',
            'Rajesh Fernando',
            '+94 77 123 4567',
            'No. 45, Galle Road, Colombo 03, Sri Lanka',
            json_encode(['Colombo', 'Gampaha', 'Kalutara', 'Kandy']),
            json_encode(['House Construction', 'Building Renovation', 'Commercial Construction']),
            'CIDA/REG/2023/001234',
            'C5',
            'Elite Construction Solutions specializes in high-quality residential and commercial construction projects.',
            4.8,
            0,
            0
        ]);
        echo "<p>✅ Created contractor profile</p>";
    } else {
        echo "<p>✅ Contractor profile exists</p>";
    }
    
    // 3. Create customer user for testing
    echo "<h3>3. Setting up Test Customer</h3>";
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $customer_user = $stmt->fetch();
    
    if (!$customer_user) {
        echo "<p>Creating test customer...</p>";
        $stmt = $pdo->prepare("
            INSERT INTO users (email, password, user_type, status, created_at, updated_at) 
            VALUES ('<EMAIL>', ?, 'customer', 'approved', NOW(), NOW())
        ");
        $stmt->execute([password_hash('password', PASSWORD_DEFAULT)]);
        $customer_id = $pdo->lastInsertId();
        
        // Create customer profile
        $stmt = $pdo->prepare("
            INSERT INTO customer_profiles (
                user_id, first_name, last_name, phone, district, address, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        $stmt->execute([
            $customer_id,
            'John',
            'Doe',
            '+94 77 987 6543',
            'Colombo',
            '123 Main Street, Colombo 05'
        ]);
        echo "<p>✅ Created test customer with ID: $customer_id</p>";
    } else {
        $customer_id = $customer_user['id'];
        echo "<p>✅ Test customer exists with ID: $customer_id</p>";
    }
    
    // 4. Create service category if needed
    echo "<h3>4. Setting up Service Categories</h3>";
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'service_categories'");
    $stmt->execute();
    $service_table_exists = $stmt->fetch();
    
    $service_category_id = 1; // Default fallback
    if ($service_table_exists) {
        $stmt = $pdo->prepare("SELECT id FROM service_categories WHERE name_en = 'House Construction' LIMIT 1");
        $stmt->execute();
        $service = $stmt->fetch();
        if ($service) {
            $service_category_id = $service['id'];
            echo "<p>✅ Using existing service category ID: $service_category_id</p>";
        }
    } else {
        echo "<p>⚠️ Service categories table doesn't exist, using default ID</p>";
    }
    
    // 5. Create quote requests
    echo "<h3>5. Creating Quote Requests</h3>";
    
    // Clear existing test data
    $stmt = $pdo->prepare("DELETE FROM quote_responses WHERE contractor_id = ?");
    $stmt->execute([$contractor_id]);
    $stmt = $pdo->prepare("DELETE FROM quote_requests WHERE customer_id = ?");
    $stmt->execute([$customer_id]);
    
    // Create quote requests
    $quote_requests = [
        [
            'title' => 'Modern House Construction',
            'description' => 'Looking to build a 3-bedroom modern house with contemporary design. Plot size is 15 perches.',
            'location' => 'Nugegoda, Colombo',
            'district' => 'Colombo',
            'estimated_budget' => 8000000.00,
            'project_timeline' => '6-8 months'
        ],
        [
            'title' => 'House Renovation Project',
            'description' => 'Complete renovation of a 2-story house including electrical, plumbing, and interior work.',
            'location' => 'Kandy, Central Province',
            'district' => 'Kandy',
            'estimated_budget' => 4200000.00,
            'project_timeline' => '3-4 months'
        ],
        [
            'title' => 'Swimming Pool Construction',
            'description' => 'Build a swimming pool in backyard. Size approximately 25ft x 15ft with modern filtration.',
            'location' => 'Gampaha, Western Province',
            'district' => 'Gampaha',
            'estimated_budget' => 1500000.00,
            'project_timeline' => '2-3 months'
        ]
    ];
    
    $quote_ids = [];
    foreach ($quote_requests as $request) {
        $stmt = $pdo->prepare("
            INSERT INTO quote_requests (
                customer_id, service_category_id, title, description, location, district,
                estimated_budget, project_timeline, status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'open', NOW(), NOW())
        ");
        $stmt->execute([
            $customer_id,
            $service_category_id,
            $request['title'],
            $request['description'],
            $request['location'],
            $request['district'],
            $request['estimated_budget'],
            $request['project_timeline']
        ]);
        $quote_ids[] = $pdo->lastInsertId();
    }
    echo "<p>✅ Created " . count($quote_ids) . " quote requests</p>";
    
    // 6. Create quote responses (contractor responds to first 2 quotes)
    echo "<h3>6. Creating Quote Responses</h3>";
    
    $responses = [
        [
            'quote_id' => $quote_ids[0],
            'amount' => 7800000.00,
            'timeline' => '7 months',
            'description' => 'Complete modern house construction with high-quality materials and finishes.'
        ],
        [
            'quote_id' => $quote_ids[1],
            'amount' => 4200000.00,
            'timeline' => '4 months',
            'description' => 'Complete renovation package including all electrical, plumbing, and interior work.'
        ]
    ];
    
    foreach ($responses as $response) {
        $stmt = $pdo->prepare("
            INSERT INTO quote_responses (
                quote_request_id, contractor_id, quoted_amount, estimated_timeline,
                description, status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, 'pending', NOW(), NOW())
        ");
        $stmt->execute([
            $response['quote_id'],
            $contractor_id,
            $response['amount'],
            $response['timeline'],
            $response['description']
        ]);
    }
    echo "<p>✅ Created " . count($responses) . " quote responses</p>";
    
    // 7. Create portfolio projects
    echo "<h3>7. Creating Portfolio Projects</h3>";
    
    // Clear existing portfolios
    $stmt = $pdo->prepare("DELETE FROM contractor_portfolios WHERE contractor_id = ?");
    $stmt->execute([$contractor_id]);
    
    $portfolios = [
        [
            'name' => 'Modern Villa - Colombo 07',
            'description' => 'A stunning 4-bedroom modern villa with contemporary architecture and luxury finishes.',
            'location' => 'Colombo 07',
            'value' => 25000000.00,
            'completion_date' => '2023-08-15'
        ],
        [
            'name' => 'Commercial Building - Kandy',
            'description' => '3-story commercial building with modern office spaces and retail units.',
            'location' => 'Kandy',
            'value' => 45000000.00,
            'completion_date' => '2023-06-30'
        ],
        [
            'name' => 'Luxury Apartment Complex',
            'description' => '12-unit luxury apartment complex with modern amenities.',
            'location' => 'Gampaha',
            'value' => 85000000.00,
            'completion_date' => '2023-09-20'
        ]
    ];
    
    foreach ($portfolios as $portfolio) {
        $stmt = $pdo->prepare("
            INSERT INTO contractor_portfolios (
                contractor_id, project_name, project_description, project_location,
                completion_date, project_value, project_images, is_featured, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, '[]', 1, NOW(), NOW())
        ");
        $stmt->execute([
            $contractor_id,
            $portfolio['name'],
            $portfolio['description'],
            $portfolio['location'],
            $portfolio['completion_date'],
            $portfolio['value']
        ]);
    }
    echo "<p>✅ Created " . count($portfolios) . " portfolio projects</p>";
    
    // 8. Create reviews (if reviews table exists and has compatible structure)
    echo "<h3>8. Creating Reviews</h3>";
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'reviews'");
    $stmt->execute();
    $reviews_table_exists = $stmt->fetch();

    if ($reviews_table_exists) {
        try {
            // Check table structure to determine which columns to use
            $stmt = $pdo->prepare("DESCRIBE reviews");
            $stmt->execute();
            $columns = $stmt->fetchAll();

            $required_fields = [];
            $available_fields = [];
            foreach ($columns as $column) {
                $available_fields[] = $column['Field'];
                // Track required fields (NOT NULL and no default)
                if ($column['Null'] === 'NO' && $column['Default'] === null && $column['Extra'] !== 'auto_increment') {
                    $required_fields[] = $column['Field'];
                }
            }

            echo "<p>Required fields: " . implode(', ', $required_fields) . "</p>";

            // Check if we can create reviews with available data
            $can_create_reviews = true;
            $missing_fields = [];

            // Check for required foreign key fields
            if (in_array('payment_id', $required_fields)) {
                // Need project_payments table
                $stmt = $pdo->prepare("SHOW TABLES LIKE 'project_payments'");
                $stmt->execute();
                if (!$stmt->fetch()) {
                    $can_create_reviews = false;
                    $missing_fields[] = 'project_payments table';
                }
            }

            if (in_array('quote_response_id', $required_fields)) {
                // We have quote responses, so this should be OK
            }

            if (!$can_create_reviews) {
                echo "<p>⚠️ Cannot create reviews - missing: " . implode(', ', $missing_fields) . "</p>";
            } else {
                // Clear existing reviews
                $stmt = $pdo->prepare("DELETE FROM reviews WHERE contractor_id = ?");
                $stmt->execute([$contractor_id]);

                // Try to create simple reviews if possible
                if (in_array('payment_id', $required_fields) || in_array('quote_response_id', $required_fields)) {
                    echo "<p>⚠️ Reviews table requires payment_id or quote_response_id - skipping for now</p>";
                    echo "<p>Reviews will be created automatically when payments are processed</p>";
                } else {
                    // Simple reviews table structure
                    $reviews = [
                        [
                            'rating' => 5,
                            'text' => 'Excellent work! The construction quality is outstanding and completed on time.'
                        ],
                        [
                            'rating' => 4,
                            'text' => 'Very professional team. Good quality work and reasonable pricing.'
                        ]
                    ];

                    foreach ($reviews as $index => $review) {
                        $quote_request_id = isset($quote_ids[$index]) ? $quote_ids[$index] : $quote_ids[0];

                        if (in_array('is_approved', $available_fields)) {
                            $stmt = $pdo->prepare("
                                INSERT INTO reviews (
                                    customer_id, contractor_id, quote_request_id, rating, review_text, is_approved, created_at, updated_at
                                ) VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())
                            ");
                        } else {
                            $stmt = $pdo->prepare("
                                INSERT INTO reviews (
                                    customer_id, contractor_id, quote_request_id, rating, review_text, status, created_at, updated_at
                                ) VALUES (?, ?, ?, ?, ?, 'approved', NOW(), NOW())
                            ");
                        }
                        $stmt->execute([
                            $customer_id,
                            $contractor_id,
                            $quote_request_id,
                            $review['rating'],
                            $review['text']
                        ]);
                    }
                    echo "<p>✅ Created " . count($reviews) . " reviews</p>";
                }
            }
        } catch (Exception $e) {
            echo "<p>⚠️ Could not create reviews: " . $e->getMessage() . "</p>";
            echo "<p>This is normal if the reviews table has complex foreign key requirements</p>";
        }
    } else {
        echo "<p>⚠️ Reviews table doesn't exist, skipping review creation</p>";
    }
    
    $pdo->commit();
    
    echo "<h3>✅ Test Data Setup Complete!</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>Expected Dashboard Results:</h4>";
    echo "<ul>";
    echo "<li><strong>Total Quotes:</strong> 2 (contractor responded to 2 quotes)</li>";
    echo "<li><strong>Pending Quotes:</strong> 1 (1 open quote not responded to)</li>";
    echo "<li><strong>Total Reviews:</strong> 0-2 (depends on reviews table structure)</li>";
    echo "<li><strong>Portfolio Projects:</strong> " . count($portfolios) . "</li>";
    echo "</ul>";
    echo "<p><em>Note: Reviews may show 0 if the reviews table requires payment records or has complex foreign key constraints. The other metrics should display correctly.</em></p>";
    echo "</div>";
    
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>Login Credentials:</h4>";
    echo "<p><strong>Email:</strong> <EMAIL></p>";
    echo "<p><strong>Password:</strong> password</p>";
    echo "</div>";
    
    echo "<p><a href='test_contractor_login.php?login_as=$contractor_id' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Login as Contractor</a></p>";
    echo "<p><a href='contractor/dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Dashboard</a></p>";
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo "<h3 style='color: red;'>❌ Error Setting Up Test Data</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Setup Test Data</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h2, h3 { color: #333; }
        a { text-decoration: none; }
        a:hover { opacity: 0.8; }
    </style>
</head>
<body>
</body>
</html>
