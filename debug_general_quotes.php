<?php
require_once 'config/database.php';

echo "<h2>🔍 Debug General Quote System</h2>";

try {
    // Step 1: Check if there are any quote requests
    echo "<h3>Step 1: Check Quote Requests</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM quote_requests");
    $quote_count = $stmt->fetchColumn();
    echo "<p>Total quote requests: $quote_count</p>";
    
    if ($quote_count > 0) {
        $stmt = $pdo->query("
            SELECT id, title, district, service_category_id, specific_contractor_id, status, created_at 
            FROM quote_requests 
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $recent_quotes = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Title</th><th>District</th><th>Service ID</th><th>Specific Contractor</th><th>Status</th><th>Created</th></tr>";
        foreach ($recent_quotes as $quote) {
            $specific = $quote['specific_contractor_id'] ? $quote['specific_contractor_id'] : 'GENERAL';
            echo "<tr>";
            echo "<td>{$quote['id']}</td>";
            echo "<td>" . htmlspecialchars($quote['title']) . "</td>";
            echo "<td>{$quote['district']}</td>";
            echo "<td>{$quote['service_category_id']}</td>";
            echo "<td>$specific</td>";
            echo "<td>{$quote['status']}</td>";
            echo "<td>{$quote['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 2: Check contractors
    echo "<h3>Step 2: Check Contractors</h3>";
    $stmt = $pdo->query("
        SELECT u.id, u.email, u.status, cp.business_name, cp.service_areas, cp.service_types
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        AND cp.service_areas IS NOT NULL AND cp.service_areas != '' AND cp.service_areas != '[]'
        AND cp.service_types IS NOT NULL AND cp.service_types != '' AND cp.service_types != '[]'
    ");
    $contractors = $stmt->fetchAll();
    
    echo "<p>Approved contractors with complete data: " . count($contractors) . "</p>";
    
    if (count($contractors) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Business Name</th><th>Service Areas</th><th>Service Types</th></tr>";
        foreach ($contractors as $contractor) {
            echo "<tr>";
            echo "<td>{$contractor['id']}</td>";
            echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
            echo "<td>" . htmlspecialchars($contractor['service_areas']) . "</td>";
            echo "<td>" . htmlspecialchars($contractor['service_types']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 3: Check notifications
    echo "<h3>Step 3: Check Notifications</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM notifications WHERE type = 'quote_received'");
    $notification_count = $stmt->fetchColumn();
    echo "<p>Quote received notifications: $notification_count</p>";
    
    if ($notification_count > 0) {
        $stmt = $pdo->query("
            SELECT n.*, u.email 
            FROM notifications n 
            JOIN users u ON n.user_id = u.id 
            WHERE n.type = 'quote_received' 
            ORDER BY n.created_at DESC 
            LIMIT 5
        ");
        $notifications = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>User Email</th><th>Title</th><th>Message</th><th>Related ID</th><th>Created</th></tr>";
        foreach ($notifications as $notification) {
            echo "<tr>";
            echo "<td>{$notification['id']}</td>";
            echo "<td>{$notification['email']}</td>";
            echo "<td>" . htmlspecialchars($notification['title']) . "</td>";
            echo "<td>" . htmlspecialchars($notification['message']) . "</td>";
            echo "<td>{$notification['related_id']}</td>";
            echo "<td>{$notification['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 4: Test a specific scenario
    if (count($contractors) > 0 && $quote_count > 0) {
        echo "<h3>Step 4: Test Contractor Quote Visibility</h3>";
        
        $test_contractor = $contractors[0];
        $contractor_id = $test_contractor['id'];
        
        echo "<p>Testing with contractor: " . htmlspecialchars($test_contractor['business_name']) . " (ID: $contractor_id)</p>";
        
        // Get contractor's service data
        $service_areas = json_decode($test_contractor['service_areas'], true);
        $service_types = json_decode($test_contractor['service_types'], true);
        
        echo "<p>Contractor serves areas: " . implode(', ', $service_areas) . "</p>";
        echo "<p>Contractor provides services: " . implode(', ', $service_types) . "</p>";
        
        // Test the quotes query from contractor/quotes.php
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, cp.district, sc.name_en as service_category,
                   (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
                   CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
            WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
            AND qr.status = 'open' 
            AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)
            ORDER BY qr.created_at DESC
        ");
        
        $stmt->execute([$contractor_id, $contractor_id, $contractor_id, $contractor_id]);
        $all_quotes_raw = $stmt->fetchAll();
        
        echo "<p>Raw quotes from database: " . count($all_quotes_raw) . "</p>";
        
        // Filter quotes using the same logic as quotes.php
        $filtered_quotes = [];
        foreach ($all_quotes_raw as $quote) {
            // Always include direct quotes for this contractor
            if ($quote['specific_contractor_id'] == $contractor_id) {
                $filtered_quotes[] = $quote;
                continue;
            }

            // For general quotes (no specific contractor), check service and area match
            if ($quote['specific_contractor_id'] === null) {
                // Check if contractor provides this service
                $has_service = in_array((int)$quote['service_category_id'], $service_types) ||
                              in_array((string)$quote['service_category_id'], $service_types);

                // Check if contractor serves this area
                $has_area = in_array($quote['district'], $service_areas);

                if ($has_service && $has_area) {
                    $filtered_quotes[] = $quote;
                }
            }
        }
        
        echo "<p>Filtered quotes for this contractor: " . count($filtered_quotes) . "</p>";
        
        if (count($filtered_quotes) > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Quote ID</th><th>Title</th><th>District</th><th>Service ID</th><th>Type</th><th>Match Reason</th></tr>";
            foreach ($filtered_quotes as $quote) {
                $match_reason = "";
                if ($quote['specific_contractor_id'] == $contractor_id) {
                    $match_reason = "Direct quote";
                } else {
                    $has_service = in_array((int)$quote['service_category_id'], $service_types) ||
                                  in_array((string)$quote['service_category_id'], $service_types);
                    $has_area = in_array($quote['district'], $service_areas);
                    $match_reason = "Service: " . ($has_service ? "✓" : "✗") . ", Area: " . ($has_area ? "✓" : "✗");
                }
                
                echo "<tr>";
                echo "<td>{$quote['id']}</td>";
                echo "<td>" . htmlspecialchars($quote['title']) . "</td>";
                echo "<td>{$quote['district']}</td>";
                echo "<td>{$quote['service_category_id']}</td>";
                echo "<td>{$quote['quote_type']}</td>";
                echo "<td>$match_reason</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // Step 5: Check service categories
    echo "<h3>Step 5: Check Service Categories</h3>";
    $stmt = $pdo->query("SELECT id, name_en FROM service_categories ORDER BY id");
    $categories = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Name</th></tr>";
    foreach ($categories as $category) {
        echo "<tr><td>{$category['id']}</td><td>" . htmlspecialchars($category['name_en']) . "</td></tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database Error: " . $e->getMessage() . "</p>";
}
?>
