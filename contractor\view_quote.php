<?php
// Include authentication and authorization check
require_once 'includes/auth_check.php';

// Contractor data is now available in $contractor variable

$quote_id = (int)($_GET['id'] ?? 0);

if (!$quote_id) {
    $_SESSION['error'] = 'Invalid quote request.';
    header('Location: quotes.php');
    exit();
}

// Get quote request details
try {
    // First ensure specific_contractor_id column exists
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
        $column_exists = $stmt->fetch();

        if (!$column_exists) {
            $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        }
    } catch (PDOException $e) {
        // Column might already exist, continue
    }

    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, cp.address, u.email, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               (SELECT description FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_description,
               (SELECT estimated_timeline FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_timeline,
               (SELECT terms_conditions FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_terms,
               CASE WHEN qr.specific_contractor_id = ? THEN 'direct' ELSE 'general' END as quote_type
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        JOIN users u ON qr.customer_id = u.id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE qr.id = ?
    ");
    $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'], $quote_id]);
    $quote = $stmt->fetch();
    
    if (!$quote) {
        $_SESSION['error'] = 'Quote request not found.';
        header('Location: quotes.php');
        exit();
    }
    
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: quotes.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Quote Details - BrickClick Contractor</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet"> 
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-orange: #FF6B35;
            --primary-dark: #C5172E;
            --secondary-blue: #06202B;
            --accent-yellow: #FFD700;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --success-green: #28a745;
            --warning-orange: #ffc107;
            --danger-red: #dc3545;
            --info-blue: #17a2b8;
        }

        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e9ecef 100%);
            font-family: 'Open Sans', sans-serif;
            min-height: 100vh;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, var(--secondary-blue) 0%, #0a2a3a 100%);
            z-index: 1000;
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            flex-shrink: 0;
        }

        .sidebar-header h3 {
            color: var(--primary-orange);
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }

        .sidebar-header p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }

        .sidebar-nav {
            flex: 1;
            padding: 0.5rem 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .nav-item {
            margin: 0.15rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,107,53,0.1);
            color: var(--primary-orange);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--primary-orange), var(--primary-dark));
            color: white;
            box-shadow: 0 3px 10px rgba(255,107,53,0.3);
        }

        .nav-link i {
            margin-right: 0.8rem;
            width: 18px;
            text-align: center;
            font-size: 1rem;
        }

        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-orange));
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(197, 23, 46, 0.2);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
        }

        .page-subtitle {
            opacity: 0.9;
            margin: 0.5rem 0 0 0;
        }

        .quote-details-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .card-header {
            background: linear-gradient(135deg, var(--secondary-blue), #0a2a3a);
            color: white;
            padding: 1.5rem 2rem;
            border-bottom: none;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }

        .card-body {
            padding: 2rem;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .info-section h5 {
            color: var(--primary-dark);
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-orange);
        }

        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .info-item i {
            color: var(--primary-orange);
            width: 20px;
            margin-right: 1rem;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
        }

        .status-open {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
        }

        .status-completed {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }

        .status-cancelled {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }

        .my-response-card {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            border: 2px solid var(--success-green);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .response-status {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .status-pending {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
        }

        .status-accepted {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }

        .status-rejected {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-orange), var(--primary-dark));
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(197, 23, 46, 0.3);
        }

        .btn-secondary {
            background: var(--medium-gray);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>BrickClick</h3>
            <p>Contractor Portal</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link active">
                    <i class="fas fa-file-invoice-dollar"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="portfolio.php" class="nav-link">
                    <i class="fas fa-images"></i>Portfolio
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews & Ratings
                </a>
            </div>
            <div class="nav-item">
                <a href="profile.php" class="nav-link">
                    <i class="fas fa-user-edit"></i>Profile & Services
                </a>
            </div>
            <div class="nav-item">
                <a href="payment_history.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payment History
                </a>
            </div>
            <div class="nav-item">
                <a href="notifications.php" class="nav-link">
                    <i class="fas fa-bell"></i>Notifications
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Quote Request Details</h1>
            <p class="page-subtitle">View complete quote request information</p>
        </div>

        <!-- Quote Details -->
        <div class="quote-details-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-file-invoice-dollar me-2"></i><?php echo htmlspecialchars($quote['service_category']); ?>
                    <?php if ($quote['quote_type'] === 'direct'): ?>
                        <span class="badge bg-primary ms-2">
                            <i class="fas fa-star me-1"></i>Direct Request
                        </span>
                    <?php else: ?>
                        <span class="badge bg-info ms-2">
                            <i class="fas fa-broadcast-tower me-1"></i>General Request
                        </span>
                    <?php endif; ?>
                </h3>
            </div>
            
            <div class="card-body">
                <div class="info-grid">
                    <!-- Customer Information -->
                    <div class="info-section">
                        <h5><i class="fas fa-user me-2"></i>Customer Information</h5>
                        <div class="info-item">
                            <i class="fas fa-user"></i>
                            <span><?php echo htmlspecialchars($quote['first_name'] . ' ' . $quote['last_name']); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-envelope"></i>
                            <span><?php echo htmlspecialchars($quote['email']); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-phone"></i>
                            <span><?php echo htmlspecialchars($quote['phone']); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span><?php echo htmlspecialchars($quote['district']); ?></span>
                        </div>
                        <?php if ($quote['address']): ?>
                        <div class="info-item">
                            <i class="fas fa-home"></i>
                            <span><?php echo htmlspecialchars($quote['address']); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Project Information -->
                    <div class="info-section">
                        <h5><i class="fas fa-project-diagram me-2"></i>Project Information</h5>
                        <div class="info-item">
                            <i class="fas fa-tools"></i>
                            <span><?php echo htmlspecialchars($quote['service_category']); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>Budget: Rs. <?php echo number_format($quote['estimated_budget']); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span>Posted: <?php echo date('M j, Y g:i A', strtotime($quote['created_at'])); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-info-circle"></i>
                            <span class="status-badge status-<?php echo $quote['status']; ?>">
                                <?php echo ucfirst($quote['status']); ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Project Description -->
                <div class="info-section">
                    <h5><i class="fas fa-file-alt me-2"></i>Project Description</h5>
                    <p class="mb-0"><?php echo nl2br(htmlspecialchars($quote['description'])); ?></p>
                </div>

                <?php if ($quote['has_responded'] > 0): ?>
                <!-- My Response -->
                <div class="my-response-card">
                    <h5><i class="fas fa-reply me-2"></i>Your Response</h5>
                    <div class="response-status status-<?php echo $quote['my_quote_status']; ?>">
                        Status: <?php echo ucfirst($quote['my_quote_status']); ?>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Quoted Amount:</strong> Rs. <?php echo number_format($quote['my_quote_amount']); ?>
                        </div>
                        <div class="col-md-6">
                            <strong>Timeline:</strong> <?php echo htmlspecialchars($quote['my_quote_timeline']); ?>
                        </div>
                    </div>
                    <?php if ($quote['my_quote_description']): ?>
                    <div class="mt-2">
                        <strong>Description:</strong><br>
                        <?php echo nl2br(htmlspecialchars($quote['my_quote_description'])); ?>
                    </div>
                    <?php endif; ?>
                    <?php if ($quote['my_quote_terms']): ?>
                    <div class="mt-2">
                        <strong>Terms & Conditions:</strong><br>
                        <?php echo nl2br(htmlspecialchars($quote['my_quote_terms'])); ?>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Actions -->
                <div class="d-flex gap-3 mt-4">
                    <?php if ($quote['has_responded'] == 0 && $quote['status'] === 'open'): ?>
                        <a href="respond_quote.php?id=<?php echo $quote['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-reply me-2"></i>Respond to Quote
                        </a>
                    <?php endif; ?>
                    <a href="quotes.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Quotes
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
