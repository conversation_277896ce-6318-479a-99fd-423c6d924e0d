<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = 'Invalid request method. Expected POST, got ' . $_SERVER['REQUEST_METHOD'];
    header('Location: quotes.php');
    exit();
}

// Debug logging
error_log("Payment processing started for user: " . $_SESSION['user_id']);

// Check if payment session data exists
if (!isset($_SESSION['payment_data'])) {
    $_SESSION['error'] = 'Invalid payment session.';
    header('Location: quotes.php');
    exit();
}

$payment_data = $_SESSION['payment_data'];
$quote_response_id = $payment_data['quote_response_id'];
$amount = $payment_data['amount'];
$payment_type = $payment_data['payment_type'];
$payment_method = $payment_data['payment_method'];

// Get form data based on payment method
$payment_details = [];
if ($payment_method === 'card') {
    $payment_details = [
        'cardholder_name' => trim($_POST['cardholder_name'] ?? 'Test User'),
        'card_number' => str_replace(' ', '', $_POST['card_number'] ?? '****************'),
        'expiry_date' => $_POST['expiry_date'] ?? '12/25',
        'cvv' => $_POST['cvv'] ?? '123'
    ];

    // For demo purposes, skip validation - auto-succeed payment
    // In production, proper validation would be required

} elseif ($payment_method === 'bank') {
    $payment_details = [
        'bank_name' => $_POST['bank_name'] ?? 'Test Bank',
        'account_number' => trim($_POST['account_number'] ?? '**********')
    ];

} else { // mobile payment
    $payment_details = [
        'mobile_service' => $_POST['mobile_service'] ?? 'Dialog',
        'mobile_number' => trim($_POST['mobile_number'] ?? '**********'),
        'mobile_pin' => $_POST['mobile_pin'] ?? '1234'
    ];
}

// Get quote response details
try {
    $stmt = $pdo->prepare("
        SELECT qres.*, qr.customer_id, qr.title
        FROM quote_responses qres
        JOIN quote_requests qr ON qres.quote_request_id = qr.id
        WHERE qres.id = ? AND qr.customer_id = ?
    ");
    $stmt->execute([$quote_response_id, $_SESSION['user_id']]);
    $quote_response = $stmt->fetch();

    if (!$quote_response) {
        // Debug: Let's check if the quote response exists at all
        $stmt = $pdo->prepare("SELECT * FROM quote_responses WHERE id = ?");
        $stmt->execute([$quote_response_id]);
        $debug_quote = $stmt->fetch();

        if (!$debug_quote) {
            $_SESSION['error'] = 'Quote response not found (ID: ' . $quote_response_id . ')';
        } else {
            $_SESSION['error'] = 'Quote response not accessible or not accepted. Status: ' . $debug_quote['status'];
        }
        header('Location: quotes.php');
        exit();
    }
} catch (PDOException $e) {
    error_log("Database error in payment processing: " . $e->getMessage());
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    header('Location: quotes.php');
    exit();
}

// Check if payment already exists
try {
    $stmt = $pdo->prepare("
        SELECT * FROM project_payments 
        WHERE quote_response_id = ? AND payment_type = ? AND payment_status = 'completed'
    ");
    $stmt->execute([$quote_response_id, $payment_type]);
    $existing_payment = $stmt->fetch();
    
    if ($existing_payment) {
        $_SESSION['error'] = 'Payment has already been completed for this project.';
        header('Location: quotes.php');
        exit();
    }
} catch (PDOException $e) {
    error_log("Database error checking existing payments: " . $e->getMessage());
    $_SESSION['error'] = 'Database error checking existing payments: ' . $e->getMessage();
    header('Location: quotes.php');
    exit();
}

try {
    $pdo->beginTransaction();
    
    // Generate transaction ID
    $transaction_id = 'TXN_' . time() . '_' . rand(1000, 9999);
    
    // In a real application, here you would:
    // 1. Send payment details to payment gateway (PayHere, Stripe, etc.)
    // 2. Wait for gateway response
    // 3. Set payment_status based on gateway response
    
    // For demo purposes, we'll simulate successful payment processing
    // In production, this would be determined by the payment gateway response
    $payment_status = 'completed';
    $payment_date = date('Y-m-d H:i:s');
    
    // Store payment details securely (in production, encrypt sensitive data)
    $payment_details_json = json_encode([
        'method' => $payment_method,
        'last_four' => $payment_method === 'card' ? substr($payment_details['card_number'], -4) : null,
        'bank' => $payment_method === 'bank' ? $payment_details['bank_name'] : null,
        'mobile_service' => $payment_method === 'mobile' ? $payment_details['mobile_service'] : null
    ]);
    
    // Insert payment record
    $stmt = $pdo->prepare("
        INSERT INTO project_payments (
            quote_response_id, customer_id, contractor_id, amount, 
            payment_type, payment_status, payment_method, transaction_id, 
            payment_date, payment_details
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $quote_response_id,
        $_SESSION['user_id'],
        $quote_response['contractor_id'],
        $amount,
        $payment_type,
        $payment_status,
        $payment_method,
        $transaction_id,
        $payment_date,
        $payment_details_json
    ]);
    
    $payment_id = $pdo->lastInsertId();
    
    // Create notification for contractor
    $notification_title = "Down Payment Received";
    $notification_message = "You have received a " . ucfirst(str_replace('_', ' ', $payment_type)) . " of Rs. " . number_format($amount, 2) . " for project: " . $quote_response['title'] . ". You can now start the project.";
    
    $stmt = $pdo->prepare("
        INSERT INTO notifications (user_id, title, message, type, related_id) 
        VALUES (?, ?, ?, 'payment_received', ?)
    ");
    $stmt->execute([$quote_response['contractor_id'], $notification_title, $notification_message, $payment_id]);
    
    // Create notification for customer
    $customer_notification = "Payment successful! Your " . ucfirst(str_replace('_', ' ', $payment_type)) . " of Rs. " . number_format($amount, 2) . " has been processed successfully. The contractor has been notified.";
    $stmt = $pdo->prepare("
        INSERT INTO notifications (user_id, title, message, type, related_id) 
        VALUES (?, 'Payment Successful', ?, 'payment_success', ?)
    ");
    $stmt->execute([$_SESSION['user_id'], $customer_notification, $payment_id]);
    
    $pdo->commit();
    
    // Clear payment session data
    unset($_SESSION['payment_data']);
    
    // Redirect to payment success page
    $_SESSION['success'] = 'Payment completed successfully! The contractor has been notified about your down payment.';
    header("Location: payment_success.php?payment_id=$payment_id");
    exit();
    
} catch (PDOException $e) {
    $pdo->rollBack();
    // Log the actual error for debugging
    error_log("Payment processing error: " . $e->getMessage());
    $_SESSION['error'] = 'Payment processing failed. Error: ' . $e->getMessage();
    header("Location: payment_portal.php");
    exit();
}
?>
