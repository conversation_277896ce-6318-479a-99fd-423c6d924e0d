<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔐 Final Contractor Dashboard Test</h2>";

// Test the specific contractor
$email = '<EMAIL>';
$stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND user_type = 'contractor'");
$stmt->execute([$email]);
$user = $stmt->fetch();

if (!$user) {
    echo "<p>❌ User not found!</p>";
    exit;
}

echo "<h3>👤 User Details:</h3>";
echo "<p><strong>User ID:</strong> {$user['id']}</p>";
echo "<p><strong>Email:</strong> {$user['email']}</p>";
echo "<p><strong>Status:</strong> {$user['status']}</p>";
echo "<p><strong>User Type:</strong> {$user['user_type']}</p>";

// Get contractor profile
$stmt = $pdo->prepare("SELECT * FROM contractor_profiles WHERE user_id = ?");
$stmt->execute([$user['id']]);
$contractor = $stmt->fetch();

if (!$contractor) {
    echo "<p>❌ Contractor profile not found!</p>";
    exit;
}

echo "<h3>👷 Contractor Profile:</h3>";
echo "<p><strong>Business Name:</strong> {$contractor['business_name']}</p>";
echo "<p><strong>Service Types:</strong> {$contractor['service_types']}</p>";
echo "<p><strong>Service Areas:</strong> {$contractor['service_areas']}</p>";

$contractor_id = $user['id'];
$contractor_services = json_decode($contractor['service_types'], true) ?: [];
$contractor_areas = json_decode($contractor['service_areas'], true) ?: [];

echo "<p><strong>Parsed Services:</strong> [" . implode(', ', $contractor_services) . "]</p>";
echo "<p><strong>Parsed Areas:</strong> [" . implode(', ', $contractor_areas) . "]</p>";

// Test dashboard queries
echo "<h3>📊 Dashboard Queries Test:</h3>";

// 1. Pending quotes count
$stmt = $pdo->prepare("
    SELECT qr.*
    FROM quote_requests qr
    WHERE qr.status = 'open'
    AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
    AND qr.id NOT IN (
        SELECT COALESCE(quote_request_id, 0)
        FROM quote_responses
        WHERE contractor_id = ?
    )
");
$stmt->execute([$contractor_id, $contractor_id]);
$all_pending_quotes = $stmt->fetchAll();

$pending_quotes = [];
foreach ($all_pending_quotes as $quote) {
    if ($quote['specific_contractor_id'] == $contractor_id) {
        $pending_quotes[] = $quote;
    } elseif ($quote['specific_contractor_id'] === null) {
        $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                      in_array((string)$quote['service_category_id'], $contractor_services);
        $has_area = in_array($quote['district'], $contractor_areas);
        if ($has_service && $has_area) {
            $pending_quotes[] = $quote;
        }
    }
}

echo "<p><strong>Pending Quotes Count:</strong> " . count($pending_quotes) . "</p>";

// 2. Recent quotes
$stmt = $pdo->prepare("
    SELECT qr.*, cp.first_name, cp.last_name, cp.district,
           (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
    FROM quote_requests qr
    JOIN customer_profiles cp ON qr.customer_id = cp.user_id
    WHERE qr.status = 'open'
    AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
    ORDER BY qr.created_at DESC
    LIMIT 10
");
$stmt->execute([$contractor_id, $contractor_id]);
$all_recent_quotes = $stmt->fetchAll();

$recent_quotes = [];
foreach ($all_recent_quotes as $quote) {
    if ($quote['specific_contractor_id'] == $contractor_id) {
        $recent_quotes[] = $quote;
    } elseif ($quote['specific_contractor_id'] === null) {
        $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                      in_array((string)$quote['service_category_id'], $contractor_services);
        $has_area = in_array($quote['district'], $contractor_areas);
        if ($has_service && $has_area) {
            $recent_quotes[] = $quote;
        }
    }
    if (count($recent_quotes) >= 5) break;
}

echo "<p><strong>Recent Quotes Count:</strong> " . count($recent_quotes) . "</p>";

if (count($recent_quotes) > 0) {
    echo "<h4>Recent Quotes List:</h4>";
    echo "<ul>";
    foreach ($recent_quotes as $quote) {
        echo "<li><strong>ID {$quote['id']}:</strong> " . htmlspecialchars($quote['title']) . " - {$quote['first_name']} {$quote['last_name']} ({$quote['district']})</li>";
    }
    echo "</ul>";
}

// 3. Test the quotes page query
echo "<h3>📋 Quotes Page Test:</h3>";

$where_conditions = ["qr.status = 'open'"];
$where_conditions[] = "(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)";
$where_conditions[] = "qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)";
$where_clause = implode(' AND ', $where_conditions);

$stmt = $pdo->prepare("
    SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
           (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
           (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
           (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
           CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type
    FROM quote_requests qr
    JOIN customer_profiles cp ON qr.customer_id = cp.user_id
    LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
    WHERE $where_clause
    ORDER BY qr.created_at DESC
");

$query_params = [$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id];
$stmt->execute($query_params);
$all_quotes_raw = $stmt->fetchAll();

$all_quotes = [];
foreach ($all_quotes_raw as $quote) {
    if ($quote['specific_contractor_id'] == $contractor_id) {
        $all_quotes[] = $quote;
    } elseif ($quote['specific_contractor_id'] === null) {
        $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                      in_array((string)$quote['service_category_id'], $contractor_services);
        $has_area = in_array($quote['district'], $contractor_areas);
        if ($has_service && $has_area) {
            $all_quotes[] = $quote;
        }
    }
}

echo "<p><strong>Quotes Page - Total Quotes:</strong> " . count($all_quotes) . "</p>";

if (count($all_quotes) > 0) {
    echo "<h4>All Quotes List:</h4>";
    echo "<ul>";
    foreach ($all_quotes as $quote) {
        echo "<li><strong>ID {$quote['id']}:</strong> " . htmlspecialchars($quote['title']) . " - {$quote['service_category']} - {$quote['first_name']} {$quote['last_name']} ({$quote['district']})</li>";
    }
    echo "</ul>";
}

// Summary
echo "<h3>📊 Final Summary:</h3>";
echo "<div style='background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; margin: 20px 0;'>";
echo "<h4>Dashboard Should Show:</h4>";
echo "<p><strong>Pending Quotes Badge:</strong> " . count($pending_quotes) . "</p>";
echo "<p><strong>Recent Quotes Section:</strong> " . count($recent_quotes) . " quotes</p>";
echo "<p><strong>Quotes Page Total:</strong> " . count($all_quotes) . " quotes</p>";

if (count($all_quotes) > 0) {
    echo "<p style='color: green;'><strong>✅ SUCCESS:</strong> The contractor should see quotes!</p>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Log <NAME_EMAIL></li>";
    echo "<li>Go to contractor dashboard</li>";
    echo "<li>Check the pending quotes count</li>";
    echo "<li>Check the recent quotes section</li>";
    echo "<li>Go to Quote Requests page</li>";
    echo "</ol>";
} else {
    echo "<p style='color: red;'><strong>❌ ISSUE:</strong> No quotes will be displayed!</p>";
    echo "<p>This means either:</p>";
    echo "<ul>";
    echo "<li>No open quote requests exist</li>";
    echo "<li>Service/area matching is still not working</li>";
    echo "<li>All quotes have been responded to</li>";
    echo "</ul>";
}
echo "</div>";

// Quick login link
echo "<h3>🔗 Quick Access:</h3>";
echo "<p><a href='contractor/login.php' target='_blank'>Contractor Login Page</a></p>";
echo "<p>Use email: <EMAIL></p>";
?>
