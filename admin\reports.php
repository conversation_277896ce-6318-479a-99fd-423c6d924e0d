<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

// Get comprehensive statistics
try {
    // User statistics
    $stmt = $pdo->query("
        SELECT 
            user_type,
            status,
            COUNT(*) as count
        FROM users 
        WHERE user_type IN ('customer', 'contractor')
        GROUP BY user_type, status
    ");
    $user_stats = $stmt->fetchAll();
    
    // Quote statistics
    $stmt = $pdo->query("
        SELECT 
            status,
            COUNT(*) as count,
            AVG(estimated_budget) as avg_budget
        FROM quote_requests 
        GROUP BY status
    ");
    $quote_stats = $stmt->fetchAll();
    
    // Monthly trends (last 12 months)
    $stmt = $pdo->query("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(CASE WHEN user_type = 'customer' THEN 1 END) as customers,
            COUNT(CASE WHEN user_type = 'contractor' THEN 1 END) as contractors
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
    ");
    $monthly_registrations = $stmt->fetchAll();
    
    // Service category performance
    $stmt = $pdo->query("
        SELECT 
            sc.name_en,
            COUNT(qr.id) as quote_count,
            AVG(qr.estimated_budget) as avg_budget,
            COUNT(DISTINCT qr.customer_id) as unique_customers
        FROM service_categories sc
        LEFT JOIN quote_requests qr ON sc.id = qr.service_category_id
        WHERE sc.is_active = 1
        GROUP BY sc.id, sc.name_en
        ORDER BY quote_count DESC
    ");
    $service_performance = $stmt->fetchAll();
    
    // Top contractors by performance
    $stmt = $pdo->query("
        SELECT 
            cp.business_name,
            cp.average_rating,
            cp.total_reviews,
            cp.total_projects,
            COUNT(qr.id) as quotes_received,
            u.created_at
        FROM contractor_profiles cp
        JOIN users u ON cp.user_id = u.id
        LEFT JOIN quote_responses qr ON u.id = qr.contractor_id
        WHERE u.status = 'approved'
        GROUP BY cp.user_id
        ORDER BY cp.average_rating DESC, cp.total_reviews DESC
        LIMIT 10
    ");
    $top_contractors = $stmt->fetchAll();
    
    // Revenue insights (estimated)
    $stmt = $pdo->query("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as quote_count,
            SUM(estimated_budget) as total_budget,
            AVG(estimated_budget) as avg_budget
        FROM quote_requests 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
    ");
    $revenue_trends = $stmt->fetchAll();
    
    // Geographic distribution
    $stmt = $pdo->query("
        SELECT 
            district,
            COUNT(CASE WHEN user_type = 'customer' THEN 1 END) as customers,
            COUNT(CASE WHEN user_type = 'contractor' THEN 1 END) as contractors
        FROM users u
        LEFT JOIN customer_profiles cp ON u.id = cp.user_id
        LEFT JOIN contractor_profiles ctp ON u.id = ctp.user_id
        WHERE u.user_type IN ('customer', 'contractor')
        AND (cp.district IS NOT NULL OR JSON_LENGTH(ctp.service_areas) > 0)
        GROUP BY district
        ORDER BY (customers + contractors) DESC
        LIMIT 10
    ");
    $geographic_stats = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'Database error: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Reports & Analytics - Admin Panel</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF8C00;
            --accent-yellow: #FFD700;
            --light-gray: #F8F9FA;
            --medium-gray: #6C757D;
            --success-green: #28A745;
            --warning-orange: #FFC107;
            --info-blue: #17A2B8;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f5f6fa;
            color: var(--primary-dark);
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-header h3 {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }
        
        .sidebar-header p {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 15px rgba(197, 23, 46, 0.3);
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-red), #e41e3f, #ff6b6b);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(197, 23, 46, 0.4);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .page-title {
            font-weight: 700;
            margin: 0;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
        }

        .page-subtitle {
            margin: 0;
            font-size: 1.2rem;
            opacity: 0.9;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
        }
        
        .report-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .report-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--light-gray);
        }
        
        .report-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-dark);
            margin: 0;
        }
        
        .report-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-left: 2rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .report-icon:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .report-icon.users {
            background: linear-gradient(135deg, var(--info-blue), #20c997);
        }
        
        .report-icon.quotes {
            background: linear-gradient(135deg, var(--accent-orange), var(--accent-yellow));
        }
        
        .report-icon.revenue {
            background: linear-gradient(135deg, var(--success-green), #20c997);
        }
        
        .report-icon.performance {
            background: linear-gradient(135deg, var(--primary-red), var(--accent-orange));
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .metric-item {
            text-align: center;
            padding: 1.5rem;
            background: var(--light-gray);
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        
        .metric-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .metric-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-red);
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            color: var(--medium-gray);
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 2rem;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .table {
            margin: 0;
        }
        
        .table th {
            border: none;
            background: var(--light-gray);
            color: var(--primary-dark);
            font-weight: 600;
            padding: 1rem;
        }
        
        .table td {
            border: none;
            padding: 1rem;
            vertical-align: middle;
        }
        
        .rating-stars {
            color: var(--accent-yellow);
        }
        
        .export-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .btn-export {
            background: linear-gradient(135deg, var(--primary-dark), #0a2a3a);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }
        
        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(6, 32, 43, 0.3);
            color: white;
        }
        
        .trend-indicator {
            display: inline-flex;
            align-items: center;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .trend-indicator.positive {
            color: var(--success-green);
        }
        
        .trend-indicator.negative {
            color: var(--primary-red);
        }
        
        .trend-indicator i {
            margin-right: 0.25rem;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-shield-alt me-2"></i>Admin Panel</h3>
            <p>Brick & Click Management</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="contractors.php" class="nav-link">
                    <i class="fas fa-hard-hat"></i>Contractors
                </a>
            </div>
            <div class="nav-item">
                <a href="customers.php" class="nav-link">
                    <i class="fas fa-users"></i>Customers
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews
                </a>
            </div>
            <div class="nav-item">
                <a href="payments.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payments
                </a>
            </div>
            <div class="nav-item">
                <a href="reports.php" class="nav-link active">
                    <i class="fas fa-chart-bar"></i>Reports
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Reports & Analytics</h1>
            <p class="page-subtitle">Comprehensive insights into platform performance and user behavior</p>
        </div>

        <!-- Export Buttons -->
        <div class="export-buttons">
            <a href="#" class="btn-export" onclick="exportReport('pdf')">
                <i class="fas fa-file-pdf me-2"></i>Export PDF
            </a>
            <a href="#" class="btn-export" onclick="exportReport('excel')">
                <i class="fas fa-file-excel me-2"></i>Export Excel
            </a>
            <a href="#" class="btn-export" onclick="printReport()">
                <i class="fas fa-print me-2"></i>Print Report
            </a>
        </div>

        <!-- User Statistics -->
        <div class="report-card">
            <div class="report-header">
                <h2 class="report-title">User Statistics</h2>
                <div class="report-icon users">
                    <i class="fas fa-users"></i>
                </div>
            </div>

            <div class="metric-grid">
                <?php
                $total_customers = 0;
                $total_contractors = 0;
                $pending_contractors = 0;
                $approved_contractors = 0;

                foreach ($user_stats as $stat) {
                    if ($stat['user_type'] === 'customer') {
                        $total_customers += $stat['count'];
                    } elseif ($stat['user_type'] === 'contractor') {
                        $total_contractors += $stat['count'];
                        if ($stat['status'] === 'pending') $pending_contractors = $stat['count'];
                        if ($stat['status'] === 'approved') $approved_contractors = $stat['count'];
                    }
                }
                ?>
                <div class="metric-item">
                    <div class="metric-number"><?php echo number_format($total_customers); ?></div>
                    <div class="metric-label">Total Customers</div>
                </div>
                <div class="metric-item">
                    <div class="metric-number"><?php echo number_format($total_contractors); ?></div>
                    <div class="metric-label">Total Contractors</div>
                </div>
                <div class="metric-item">
                    <div class="metric-number"><?php echo number_format($approved_contractors); ?></div>
                    <div class="metric-label">Approved Contractors</div>
                </div>
                <div class="metric-item">
                    <div class="metric-number"><?php echo number_format($pending_contractors); ?></div>
                    <div class="metric-label">Pending Approvals</div>
                </div>
            </div>

            <div class="chart-container">
                <canvas id="userRegistrationChart"></canvas>
            </div>
        </div>

        <!-- Quote Statistics -->
        <div class="report-card">
            <div class="report-header">
                <h2 class="report-title">Quote Request Analytics</h2>
                <div class="report-icon quotes">
                    <i class="fas fa-file-invoice"></i>
                </div>
            </div>

            <div class="metric-grid">
                <?php
                $total_quotes = 0;
                $open_quotes = 0;
                $closed_quotes = 0;
                $total_budget = 0;

                foreach ($quote_stats as $stat) {
                    $total_quotes += $stat['count'];
                    if ($stat['status'] === 'open') $open_quotes = $stat['count'];
                    if ($stat['status'] === 'closed') $closed_quotes = $stat['count'];
                    $total_budget += $stat['avg_budget'] * $stat['count'];
                }
                $avg_budget = $total_quotes > 0 ? $total_budget / $total_quotes : 0;
                ?>
                <div class="metric-item">
                    <div class="metric-number"><?php echo number_format($total_quotes); ?></div>
                    <div class="metric-label">Total Quotes</div>
                </div>
                <div class="metric-item">
                    <div class="metric-number"><?php echo number_format($open_quotes); ?></div>
                    <div class="metric-label">Active Quotes</div>
                </div>
                <div class="metric-item">
                    <div class="metric-number"><?php echo number_format($closed_quotes); ?></div>
                    <div class="metric-label">Completed Quotes</div>
                </div>
                <div class="metric-item">
                    <div class="metric-number">LKR <?php echo number_format($avg_budget, 0); ?></div>
                    <div class="metric-label">Avg. Budget</div>
                </div>
            </div>

            <div class="chart-container">
                <canvas id="quoteStatusChart"></canvas>
            </div>
        </div>

        <!-- Service Performance -->
        <div class="report-card">
            <div class="report-header">
                <h2 class="report-title">Service Category Performance</h2>
                <div class="report-icon performance">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Service Category</th>
                            <th>Quote Requests</th>
                            <th>Avg. Budget</th>
                            <th>Unique Customers</th>
                            <th>Performance</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($service_performance as $service): ?>
                        <tr>
                            <td><strong><?php echo htmlspecialchars($service['name_en']); ?></strong></td>
                            <td><?php echo number_format($service['quote_count']); ?></td>
                            <td>LKR <?php echo number_format($service['avg_budget'] ?? 0, 0); ?></td>
                            <td><?php echo number_format($service['unique_customers']); ?></td>
                            <td>
                                <?php if ($service['quote_count'] > 10): ?>
                                    <span class="trend-indicator positive">
                                        <i class="fas fa-arrow-up"></i>High
                                    </span>
                                <?php elseif ($service['quote_count'] > 5): ?>
                                    <span class="trend-indicator" style="color: var(--warning-orange);">
                                        <i class="fas fa-minus"></i>Medium
                                    </span>
                                <?php else: ?>
                                    <span class="trend-indicator negative">
                                        <i class="fas fa-arrow-down"></i>Low
                                    </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Top Contractors -->
        <div class="report-card">
            <div class="report-header">
                <h2 class="report-title">Top Performing Contractors</h2>
                <div class="report-icon performance">
                    <i class="fas fa-trophy"></i>
                </div>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Business Name</th>
                            <th>Rating</th>
                            <th>Reviews</th>
                            <th>Projects</th>
                            <th>Quotes Received</th>
                            <th>Member Since</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($top_contractors as $contractor): ?>
                        <tr>
                            <td><strong><?php echo htmlspecialchars($contractor['business_name']); ?></strong></td>
                            <td>
                                <div class="rating-stars">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="fas fa-star<?php echo $i <= $contractor['average_rating'] ? '' : '-o'; ?>"></i>
                                    <?php endfor; ?>
                                    <span class="ms-1"><?php echo number_format($contractor['average_rating'], 1); ?></span>
                                </div>
                            </td>
                            <td><?php echo number_format($contractor['total_reviews']); ?></td>
                            <td><?php echo number_format($contractor['total_projects']); ?></td>
                            <td><?php echo number_format($contractor['quotes_received']); ?></td>
                            <td><?php echo date('M Y', strtotime($contractor['created_at'])); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart.js Scripts -->
    <script>
        // User Registration Chart
        const userCtx = document.getElementById('userRegistrationChart').getContext('2d');
        const userChart = new Chart(userCtx, {
            type: 'line',
            data: {
                labels: [<?php echo "'" . implode("','", array_column($monthly_registrations, 'month')) . "'"; ?>],
                datasets: [{
                    label: 'Customers',
                    data: [<?php echo implode(',', array_column($monthly_registrations, 'customers')); ?>],
                    borderColor: '#17A2B8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Contractors',
                    data: [<?php echo implode(',', array_column($monthly_registrations, 'contractors')); ?>],
                    borderColor: '#C5172E',
                    backgroundColor: 'rgba(197, 23, 46, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Monthly User Registrations (Last 12 Months)'
                    },
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                }
            }
        });

        // Quote Status Chart
        const quoteCtx = document.getElementById('quoteStatusChart').getContext('2d');
        const quoteChart = new Chart(quoteCtx, {
            type: 'doughnut',
            data: {
                labels: [<?php echo "'" . implode("','", array_column($quote_stats, 'status')) . "'"; ?>],
                datasets: [{
                    data: [<?php echo implode(',', array_column($quote_stats, 'count')); ?>],
                    backgroundColor: [
                        '#28A745',
                        '#C5172E',
                        '#FF8C00',
                        '#17A2B8'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Quote Request Status Distribution'
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });

        // Export Functions
        function exportReport(format) {
            if (format === 'pdf') {
                window.print();
            } else if (format === 'excel') {
                // This would typically integrate with a server-side export library
                alert('Excel export functionality would be implemented with server-side processing.');
            }
        }

        function printReport() {
            window.print();
        }

        // Print styles
        const printStyles = `
            @media print {
                .sidebar, .export-buttons { display: none !important; }
                .main-content { margin-left: 0 !important; }
                .report-card { page-break-inside: avoid; margin-bottom: 2rem; }
                .chart-container { height: 300px !important; }
            }
        `;

        const styleSheet = document.createElement("style");
        styleSheet.type = "text/css";
        styleSheet.innerText = printStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>
