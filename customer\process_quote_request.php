<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: request_quote.php');
    exit();
}

$customer_id = $_SESSION['user_id'];
$service_category_id = (int)$_POST['service_category_id'];
$title = trim($_POST['title']);
$description = trim($_POST['description']);
$location = trim($_POST['location']);
$district = $_POST['district'];
$project_timeline = $_POST['project_timeline'];
$estimated_budget = !empty($_POST['estimated_budget']) ? (float)$_POST['estimated_budget'] : null;
$budget_range = $_POST['budget_range'] ?? '';
$specific_contractor = !empty($_POST['specific_contractor']) ? (int)$_POST['specific_contractor'] : null;

// Basic validation
if (empty($title) || empty($description) || empty($location) || empty($district) || $service_category_id <= 0) {
    $_SESSION['error'] = 'Please fill in all required fields.';
    header('Location: request_quote.php');
    exit();
}

try {
    $pdo->beginTransaction();
    
    // Insert quote request
    $stmt = $pdo->prepare("
        INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, specific_contractor_id, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'open')
    ");
    $stmt->execute([$customer_id, $service_category_id, $title, $description, $location, $district, $estimated_budget, $project_timeline, $specific_contractor]);
    $quote_request_id = $pdo->lastInsertId();
    
    // Get contractors to notify
    $contractors_to_notify = [];
    $debug_info = [];

    if ($specific_contractor) {
        // Send to specific contractor only
        $stmt = $pdo->prepare("
            SELECT u.id, cp.business_name, cp.contact_person
            FROM users u
            JOIN contractor_profiles cp ON u.id = cp.user_id
            WHERE u.id = ? AND u.status = 'approved'
        ");
        $stmt->execute([$specific_contractor]);
        $contractor = $stmt->fetch();
        if ($contractor) {
            $contractors_to_notify[] = $contractor;
            $debug_info[] = "Specific contractor found: " . $contractor['business_name'];
        } else {
            $debug_info[] = "Specific contractor not found or not approved";
        }
    } else {
        // Find contractors who provide this service in this area
        $debug_info[] = "Searching for general contractors - Service: $service_category_id, District: $district";

        // Get all approved contractors with service data
        $stmt = $pdo->prepare("
            SELECT u.id, cp.business_name, cp.contact_person, cp.service_areas, cp.service_types
            FROM users u
            JOIN contractor_profiles cp ON u.id = cp.user_id
            WHERE u.status = 'approved'
            AND u.user_type = 'contractor'
            AND cp.service_areas IS NOT NULL
            AND cp.service_areas != ''
            AND cp.service_areas != '[]'
            AND cp.service_types IS NOT NULL
            AND cp.service_types != ''
            AND cp.service_types != '[]'
        ");

        $stmt->execute();
        $all_contractors = $stmt->fetchAll();
        $debug_info[] = "Found " . count($all_contractors) . " contractors with complete data";

        // Filter contractors in PHP for more reliable matching
        $contractors_to_notify = [];
        foreach ($all_contractors as $contractor) {
            $service_areas = json_decode($contractor['service_areas'], true);
            $service_types = json_decode($contractor['service_types'], true);

            $has_area = false;
            $has_service = false;

            // Check service areas
            if (is_array($service_areas) && !empty($service_areas)) {
                $has_area = in_array($district, $service_areas);
                $debug_info[] = "Contractor {$contractor['id']} areas: " . implode(',', $service_areas) . " - Looking for: $district - Match: " . ($has_area ? 'YES' : 'NO');
            } else {
                $debug_info[] = "Contractor {$contractor['id']} has invalid service_areas: " . $contractor['service_areas'];
            }

            // Check service types (handle both string and integer formats)
            if (is_array($service_types) && !empty($service_types)) {
                $has_service = in_array($service_category_id, $service_types) ||
                              in_array((string)$service_category_id, $service_types);
                $debug_info[] = "Contractor {$contractor['id']} services: " . implode(',', $service_types) . " - Looking for: $service_category_id - Match: " . ($has_service ? 'YES' : 'NO');
            } else {
                $debug_info[] = "Contractor {$contractor['id']} has invalid service_types: " . $contractor['service_types'];
            }

            if ($has_area && $has_service) {
                $contractors_to_notify[] = [
                    'id' => $contractor['id'],
                    'business_name' => $contractor['business_name'],
                    'contact_person' => $contractor['contact_person']
                ];
                $debug_info[] = "✅ MATCHED contractor: " . $contractor['business_name'] . " (ID: {$contractor['id']})";
            } else {
                $debug_info[] = "❌ NO MATCH for contractor: " . $contractor['business_name'] . " (ID: {$contractor['id']}) - Area: " . ($has_area ? 'YES' : 'NO') . ", Service: " . ($has_service ? 'YES' : 'NO');
            }
        }

        $debug_info[] = "Final matching contractors: " . count($contractors_to_notify);
    }
    
    // Create notifications for contractors
    foreach ($contractors_to_notify as $contractor) {
        $notification_title = "New Quote Request";
        $notification_message = "You have received a new quote request for: $title in $location, $district";
        
        $stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, title, message, type, related_id) 
            VALUES (?, ?, ?, 'quote_received', ?)
        ");
        $stmt->execute([$contractor['id'], $notification_title, $notification_message, $quote_request_id]);
    }
    
    // Create notification for customer
    $customer_notification = "Quote request submitted successfully. You will receive notifications when contractors respond.";
    $stmt = $pdo->prepare("
        INSERT INTO notifications (user_id, title, message, type, related_id) 
        VALUES (?, 'Quote Request Submitted', ?, 'general', ?)
    ");
    $stmt->execute([$customer_id, $customer_notification, $quote_request_id]);
    
    $pdo->commit();

    // Add debugging information for development
    $debug_suffix = "";
    if (isset($_GET['debug']) && $_GET['debug'] == '1') {
        $debug_suffix = " Debug: " . implode("; ", $debug_info);
    }

    // Always show contractor count for general quotes
    $contractor_names = array_map(function($c) { return $c['business_name']; }, $contractors_to_notify);
    $contractor_list = count($contractor_names) > 0 ? " (" . implode(", ", $contractor_names) . ")" : "";

    $_SESSION['success'] = "Quote request submitted successfully! " . count($contractors_to_notify) . " contractors have been notified$contractor_list.$debug_suffix";
    header('Location: quotes.php');
    exit();
    
} catch (PDOException $e) {
    $pdo->rollBack();
    $_SESSION['error'] = 'Failed to submit quote request. Please try again.';
    header('Location: request_quote.php');
    exit();
}
?>
