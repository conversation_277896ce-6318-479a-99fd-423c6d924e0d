<?php
require_once 'database.php';

try {
    // Start transaction
    $pdo->beginTransaction();
    
    echo "<h2>Fixing Sample Data</h2>";
    
    // Get contractor ID
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>' AND user_type = 'contractor'");
    $stmt->execute();
    $contractor_id = $stmt->fetchColumn();
    
    if (!$contractor_id) {
        echo "<p>❌ Contractor not found!</p>";
        exit;
    }
    
    echo "<p>✅ Contractor ID: $contractor_id</p>";
    
    // Get customer ID
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>' AND user_type = 'customer'");
    $stmt->execute();
    $customer_id = $stmt->fetchColumn();
    
    if (!$customer_id) {
        echo "<p>❌ Customer not found!</p>";
        exit;
    }
    
    echo "<p>✅ Customer ID: $customer_id</p>";
    
    // Delete existing sample data to avoid duplicates
    $pdo->prepare("DELETE FROM quote_responses WHERE contractor_id = ?")->execute([$contractor_id]);
    $pdo->prepare("DELETE FROM quote_requests WHERE customer_id = ?")->execute([$customer_id]);
    $pdo->prepare("DELETE FROM reviews WHERE contractor_id = ?")->execute([$contractor_id]);
    
    echo "<p>🧹 Cleaned existing sample data</p>";
    
    // Get service category ID
    $stmt = $pdo->prepare("SELECT id FROM service_categories WHERE name_en = 'House Construction' LIMIT 1");
    $stmt->execute();
    $service_category_id = $stmt->fetchColumn() ?: 1;
    
    // Insert quote requests with correct customer ID
    $stmt = $pdo->prepare("
        INSERT INTO quote_requests (
            customer_id, service_category_id, title, description, location, district,
            estimated_budget, project_timeline, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'open', NOW(), NOW())
    ");
    
    $stmt->execute([
        $customer_id,
        $service_category_id,
        'Modern Villa Construction',
        'Looking to build a 3-bedroom modern villa with contemporary design. The plot size is 20 perches. Need complete construction including electrical, plumbing, and interior work.',
        'Nugegoda, Colombo',
        'Colombo',
        8500000.00,
        '6-8 months'
    ]);
    $quote_request_1 = $pdo->lastInsertId();
    
    $stmt->execute([
        $customer_id,
        $service_category_id,
        'House Renovation Project',
        'Complete renovation of a 2-story house built in 1985. Need to update electrical wiring, plumbing, flooring, and interior design.',
        'Kandy, Central Province',
        'Kandy',
        4200000.00,
        '3-4 months'
    ]);
    $quote_request_2 = $pdo->lastInsertId();
    
    echo "<p>✅ Added 2 quote requests</p>";
    
    // Insert quote responses from contractor
    $stmt = $pdo->prepare("
        INSERT INTO quote_responses (
            quote_request_id, contractor_id, quoted_amount, estimated_timeline,
            description, terms_conditions, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW(), NOW())
    ");
    
    $stmt->execute([
        $quote_request_1,
        $contractor_id,
        7800000.00,
        '7 months',
        'We propose to construct a modern 3-bedroom house with high-quality materials and finishes. Our package includes architectural design, structural engineering, complete electrical and plumbing work.',
        'Payment terms: 25% advance, 25% at foundation completion, 25% at roofing, 25% at final completion. 1-year warranty included.'
    ]);
    
    $stmt->execute([
        $quote_request_2,
        $contractor_id,
        4200000.00,
        '4 months',
        'Complete renovation package including electrical rewiring, plumbing upgrades, premium flooring, modern kitchen with granite countertops, bathroom renovations.',
        'Payment terms: 25% advance, 25% after demolition, 25% at 50% completion, 25% at final completion. 6-month warranty on renovation work.'
    ]);
    
    echo "<p>✅ Added 2 quote responses</p>";
    
    // Insert reviews with correct customer and contractor IDs
    $stmt = $pdo->prepare("
        INSERT INTO reviews (
            customer_id, contractor_id, quote_request_id, rating, comment, is_approved, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())
    ");
    
    $stmt->execute([
        $customer_id,
        $contractor_id,
        $quote_request_1,
        5,
        'Excellent work! Elite Construction delivered exactly what they promised. The villa construction was completed on time and the quality is outstanding.'
    ]);
    
    $stmt->execute([
        $customer_id,
        $contractor_id,
        $quote_request_2,
        4,
        'Very satisfied with the renovation work. Professional team, good quality work, and completed within the agreed timeline.'
    ]);
    
    echo "<p>✅ Added 2 reviews</p>";
    
    // Add one more open quote request that contractor hasn't responded to
    $stmt = $pdo->prepare("
        INSERT INTO quote_requests (
            customer_id, service_category_id, title, description, location, district,
            estimated_budget, project_timeline, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'open', NOW(), NOW())
    ");
    
    $stmt->execute([
        $customer_id,
        $service_category_id,
        'Swimming Pool Construction',
        'Looking to build a swimming pool in the backyard. Size approximately 25ft x 15ft with modern filtration system.',
        'Gampaha, Western Province',
        'Gampaha',
        1500000.00,
        '2-3 months'
    ]);
    
    echo "<p>✅ Added 1 pending quote request</p>";
    
    // Commit transaction
    $pdo->commit();
    
    echo "<h3>✅ Sample Data Fixed Successfully!</h3>";
    echo "<p>Now the contractor dashboard should show:</p>";
    echo "<ul>";
    echo "<li>Total Quotes: 2 (contractor has responded to 2 quotes)</li>";
    echo "<li>Pending Quotes: 1 (1 open quote not responded to)</li>";
    echo "<li>Total Reviews: 2 (2 approved reviews)</li>";
    echo "<li>Portfolio Projects: 4 (from previous sample data)</li>";
    echo "</ul>";
    
    echo "<p><a href='../contractor/dashboard.php'>Check Contractor Dashboard</a></p>";
    
} catch (PDOException $e) {
    $pdo->rollBack();
    echo "<h2>❌ Error Fixing Sample Data</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
