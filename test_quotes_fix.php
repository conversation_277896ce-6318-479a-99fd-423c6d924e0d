<?php
session_start();
require_once 'config/database.php';

echo "<h2>🎯 Test Quotes Page Fix</h2>";

try {
    // Get a contractor to test with
    $stmt = $pdo->query("
        SELECT u.id, cp.business_name, cp.service_types, cp.service_areas
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        AND cp.service_types IS NOT NULL AND cp.service_types != '' AND cp.service_types != '[]'
        AND cp.service_areas IS NOT NULL AND cp.service_areas != '' AND cp.service_areas != '[]'
        LIMIT 1
    ");
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p style='color: red;'>❌ No contractors with complete data found</p>";
        exit;
    }
    
    $contractor_id = $contractor['id'];
    echo "<p><strong>Testing contractor:</strong> " . htmlspecialchars($contractor['business_name']) . " (ID: $contractor_id)</p>";
    
    // Set session to simulate login
    $_SESSION['user_id'] = $contractor_id;
    $_SESSION['user_type'] = 'contractor';
    
    echo "<h3>Step 1: Dashboard Count (Expected)</h3>";
    
    // Dashboard logic - EXACT COPY
    $stmt = $pdo->prepare("
        SELECT qr.*, cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.id NOT IN (
            SELECT COALESCE(quote_request_id, 0)
            FROM quote_responses
            WHERE contractor_id = ?
        )
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
    $dashboard_quotes = $stmt->fetchAll();
    
    // Filter quotes in PHP (same as dashboard)
    $dashboard_count = 0;
    foreach ($dashboard_quotes as $quote) {
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $dashboard_count++;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $dashboard_count++;
            }
        }
    }
    
    echo "<p><strong>Dashboard pending count:</strong> $dashboard_count</p>";
    
    echo "<h3>Step 2: Quotes Page Count (After Fix)</h3>";
    
    // Simulate the fixed quotes page logic
    $status_filter = 'pending';
    $search = '';
    
    // Build query conditions
    $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
    $params = [$contractor_id];
    
    if ($status_filter === 'pending') {
        $where_conditions[] = "qr.status = 'open' AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)";
        $params[] = $contractor_id;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Get contractor data first
    $stmt = $pdo->prepare("SELECT service_types, service_areas FROM contractor_profiles WHERE user_id = ?");
    $stmt->execute([$contractor_id]);
    $contractor_data = $stmt->fetch();
    
    // Get all quotes that could be relevant to this contractor
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");
    
    $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
    $stmt->execute($query_params);
    $all_quotes_raw = $stmt->fetchAll();
    
    echo "<p><strong>SQL returned:</strong> " . count($all_quotes_raw) . " quotes</p>";
    
    // Filter quotes in PHP using the same logic as dashboard
    $quotes_page_count = 0;
    foreach ($all_quotes_raw as $quote) {
        // Always include direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $quotes_page_count++;
            continue;
        }
        
        // For general quotes (no specific contractor), check service and area match
        if ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($contractor_data['service_types'], true) ?: [];
            $contractor_areas = json_decode($contractor_data['service_areas'], true) ?: [];
            
            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            
            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $quotes_page_count++;
            }
        }
    }
    
    echo "<p><strong>Quotes page pending count:</strong> $quotes_page_count</p>";
    
    echo "<h3>Step 3: Result</h3>";
    
    if ($dashboard_count == $quotes_page_count) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 1rem; border-radius: 5px; margin: 1rem 0;'>";
        echo "<h4 style='color: #155724; margin: 0 0 0.5rem 0;'>🎉 SUCCESS!</h4>";
        echo "<p style='color: #155724; margin: 0;'>Dashboard and quotes page now show the same count: <strong>$dashboard_count quotes</strong></p>";
        echo "</div>";
        
        echo "<h4>Test the actual pages:</h4>";
        echo "<ul>";
        echo "<li><a href='contractor/dashboard.php' target='_blank'>Dashboard</a> - Should show $dashboard_count pending quotes</li>";
        echo "<li><a href='contractor/quotes.php?status=pending&debug=1' target='_blank'>Quotes Page (Pending)</a> - Should show $quotes_page_count quotes</li>";
        echo "<li><a href='contractor/quotes.php?status=all&debug=1' target='_blank'>Quotes Page (All)</a> - Should show all quotes</li>";
        echo "</ul>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 1rem; border-radius: 5px; margin: 1rem 0;'>";
        echo "<h4 style='color: #721c24; margin: 0 0 0.5rem 0;'>❌ Still Not Fixed</h4>";
        echo "<p style='color: #721c24; margin: 0;'>Dashboard: $dashboard_count, Quotes Page: $quotes_page_count</p>";
        echo "</div>";
    }
    
    echo "<h3>Step 4: Summary</h3>";
    echo "<p><strong>What was fixed:</strong></p>";
    echo "<ul>";
    echo "<li>Removed the JOIN with contractor_profiles from the main query</li>";
    echo "<li>Fetch contractor data separately to avoid JOIN issues</li>";
    echo "<li>Use the exact same filtering logic as the dashboard</li>";
    echo "<li>Apply filtering in PHP after getting all relevant quotes</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
