<?php
session_start();
require_once 'config/database.php';

echo "<h2>🧪 Test Contractor Quotes Page</h2>";

try {
    // Get a test contractor
    $stmt = $pdo->query("
        SELECT u.id, u.email, cp.business_name, cp.service_areas, cp.service_types, u.status
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved' 
        LIMIT 1
    ");
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p style='color: red;'>❌ No approved contractors found.</p>";
        exit;
    }
    
    $contractor_id = $contractor['id'];
    echo "<p><strong>Testing with contractor:</strong> " . htmlspecialchars($contractor['business_name']) . " (ID: $contractor_id)</p>";
    
    // Simulate session for testing
    $_SESSION['user_id'] = $contractor_id;
    $_SESSION['user_type'] = 'contractor';
    
    // Test the exact query from quotes.php for pending quotes
    echo "<h3>Test Pending Quotes Query</h3>";
    
    $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
    $params = [$contractor_id];
    
    // Add status filter for pending quotes
    $where_conditions[] = "qr.status = 'open'";
    $where_conditions[] = "qr.id NOT IN (
        SELECT COALESCE(quote_request_id, 0)
        FROM quote_responses
        WHERE contractor_id = ?
    )";
    $params[] = $contractor_id;
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
               cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");
    
    // Add contractor ID parameters for the subqueries and JOIN
    $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
    $stmt->execute($query_params);
    $all_quotes = $stmt->fetchAll();
    
    echo "<p>SQL query returned: " . count($all_quotes) . " quotes</p>";
    
    // Filter quotes in PHP (exact copy from quotes.php)
    $quotes = [];
    foreach ($all_quotes as $quote) {
        $match_reason = "";
        $is_match = false;
        
        // Always show direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $quotes[] = $quote;
            $is_match = true;
            $match_reason = "Direct quote for this contractor";
        }
        // For general quotes (no specific contractor), check service and area match
        elseif ($quote['specific_contractor_id'] === null) {
            // Get contractor's service types and areas (these come from the JOIN with contractor_profiles)
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            
            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $quotes[] = $quote;
                $is_match = true;
                $match_reason = "General quote - service and area match";
            } else {
                $match_reason = "General quote - no match (service: " . ($has_service ? 'yes' : 'no') . ", area: " . ($has_area ? 'yes' : 'no') . ")";
            }
        } else {
            $match_reason = "Direct quote for another contractor (ID: " . $quote['specific_contractor_id'] . ")";
        }
        
        echo "<div style='border: 1px solid " . ($is_match ? 'green' : 'red') . "; padding: 10px; margin: 5px 0;'>";
        echo "<strong>Quote ID:</strong> " . $quote['id'] . "<br>";
        echo "<strong>Title:</strong> " . htmlspecialchars($quote['title']) . "<br>";
        echo "<strong>Customer:</strong> " . htmlspecialchars($quote['first_name'] . ' ' . $quote['last_name']) . "<br>";
        echo "<strong>Service Category:</strong> " . htmlspecialchars($quote['service_category']) . " (ID: " . $quote['service_category_id'] . ")<br>";
        echo "<strong>District:</strong> " . $quote['district'] . "<br>";
        echo "<strong>Quote Type:</strong> " . $quote['quote_type'] . "<br>";
        echo "<strong>Specific Contractor ID:</strong> " . ($quote['specific_contractor_id'] ?: 'NULL (General)') . "<br>";
        echo "<strong>Has Responded:</strong> " . ($quote['has_responded'] ? 'Yes' : 'No') . "<br>";
        echo "<strong>Match:</strong> " . ($is_match ? '✅ YES' : '❌ NO') . "<br>";
        echo "<strong>Reason:</strong> " . $match_reason . "<br>";
        echo "</div>";
    }
    
    echo "<p><strong>Final filtered quotes count:</strong> " . count($quotes) . "</p>";
    
    // Test quote counts (exact copy from quotes.php)
    echo "<h3>Test Quote Counts</h3>";
    
    $stmt = $pdo->prepare("
        SELECT qr.id, qr.status, qr.specific_contractor_id, qr.service_category_id, qr.district,
               cp_contractor.service_types, cp_contractor.service_areas,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT COUNT(*) FROM quote_responses qres
                JOIN project_payments pp ON qres.id = pp.quote_response_id
                WHERE qres.quote_request_id = qr.id AND qres.contractor_id = ?
                AND pp.payment_type = 'down_payment' AND pp.payment_status = 'completed') as has_payment,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? AND status = 'rejected') as is_rejected
        FROM quote_requests qr
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id]);
    $all_count_quotes = $stmt->fetchAll();
    
    // Filter and count
    $pending_count = 0;
    $completed_count = 0;
    $cancelled_count = 0;
    $all_count = 0;
    
    foreach ($all_count_quotes as $quote) {
        $is_relevant = false;
        
        // Check if this quote is relevant to the contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $is_relevant = true;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $is_relevant = true;
            }
        }
        
        if ($is_relevant) {
            $all_count++;
            
            if ($quote['status'] === 'open' && $quote['has_responded'] == 0) {
                $pending_count++;
            } elseif ($quote['has_payment'] > 0) {
                $completed_count++;
            } elseif ($quote['is_rejected'] > 0) {
                $cancelled_count++;
            }
        }
    }
    
    echo "<p><strong>Quote Counts:</strong></p>";
    echo "<ul>";
    echo "<li>All: $all_count</li>";
    echo "<li>Pending: $pending_count</li>";
    echo "<li>Completed: $completed_count</li>";
    echo "<li>Cancelled: $cancelled_count</li>";
    echo "</ul>";
    
    echo "<h3>✅ Test Complete</h3>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>Quotes page query returned: " . count($all_quotes) . " quotes</li>";
    echo "<li>After filtering: " . count($quotes) . " quotes</li>";
    echo "<li>Pending quotes: $pending_count</li>";
    echo "</ul>";
    
    if (count($quotes) > 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 Quotes page should show quotes for this contractor!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Quotes page won't show any quotes for this contractor.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
