<?php
session_start();
require_once '../config/database.php';
require_once '../includes/FileUpload.php';

// Check if user is logged in and is a contractor
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'contractor') {
    header('Location: ../login.php');
    exit();
}

// Get service categories from database
try {
    $stmt = $pdo->prepare("SELECT id, name_en FROM service_categories WHERE is_active = 1 ORDER BY id");
    $stmt->execute();
    $service_categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $service_categories = [];
}

// Districts in Sri Lanka
$districts = [
    'Ampara', 'Anuradhapura', 'Badulla', 'Batticaloa', 'Colombo', 'Galle', 'Gampaha', 
    'Hambantota', 'Jaffna', 'Kalut<PERSON>', 'Ka<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> Eliya', 'Polo<PERSON><PERSON><PERSON>', 
    '<PERSON><PERSON><PERSON>', 'Ratnapura', 'Trincomalee', 'Vavuniya'
];

// CIDA grades
$cida_grades = ['C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10'];

// Get contractor profile and check status
try {
    $stmt = $pdo->prepare("
        SELECT cp.*, u.email, u.status
        FROM contractor_profiles cp
        JOIN users u ON cp.user_id = u.id
        WHERE cp.user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $contractor = $stmt->fetch();

    if (!$contractor) {
        header('Location: setup_profile.php');
        exit();
    }

    // Check if contractor is approved
    if ($contractor['status'] !== 'approved') {
        $status_messages = [
            'pending' => 'Your account is pending approval. Please wait for admin verification.',
            'rejected' => 'Your account has been rejected. Please contact support for more information.',
            'suspended' => 'Your account has been suspended. Please contact support.'
        ];

        $_SESSION['error'] = $status_messages[$contractor['status']] ?? 'Your account is not active.';
        header('Location: ../login.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: ../login.php');
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $business_name = trim($_POST['business_name']);
    $contact_person = trim($_POST['contact_person']);
    $phone = trim($_POST['phone']);
    $business_address = trim($_POST['business_address']);
    $service_areas = $_POST['service_areas'] ?? [];
    $service_types = $_POST['service_types'] ?? [];
    $cida_registration = trim($_POST['cida_registration']);
    $cida_grade = $_POST['cida_grade'];
    $business_description = trim($_POST['business_description']);
    $website = trim($_POST['website']);

    $errors = [];
    $profile_image_path = $contractor['profile_image']; // Keep existing image by default

    // Debug: Check if file was uploaded
    if (isset($_FILES['profile_image'])) {
        error_log("Profile image upload attempt - Error code: " . $_FILES['profile_image']['error'] . ", Size: " . $_FILES['profile_image']['size'] . ", Type: " . $_FILES['profile_image']['type'] . ", Name: " . $_FILES['profile_image']['name']);
    }

    // Handle profile image upload
    if (isset($_FILES['profile_image'])) {
        if ($_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            // Create a custom uploader with correct path for contractor directory
            $uploader = new FileUpload('../uploads/', ['jpg', 'jpeg', 'png', 'gif', 'image/jpeg', 'image/png', 'image/gif'], 5242880);
            $uploaded_filename = $uploader->uploadSingle($_FILES['profile_image'], 'profile_pictures');

            if ($uploaded_filename) {
                // Delete old profile image if it exists
                if ($contractor['profile_image'] && file_exists('../uploads/profile_pictures/' . $contractor['profile_image'])) {
                    unlink('../uploads/profile_pictures/' . $contractor['profile_image']);
                }
                $profile_image_path = $uploaded_filename;
                $_SESSION['success'] = 'Profile picture uploaded successfully!';
            } else {
                $errors = array_merge($errors, $uploader->getErrors());
            }
        } elseif ($_FILES['profile_image']['error'] !== UPLOAD_ERR_NO_FILE) {
            // Handle other upload errors
            $uploader = new FileUpload('../uploads/', ['jpg', 'jpeg', 'png', 'gif', 'image/jpeg', 'image/png', 'image/gif'], 5242880);
            $errors[] = 'Profile image upload failed: ' . $uploader->getUploadErrorMessage($_FILES['profile_image']['error']);
        }
    }
    
    // Validation
    if (empty($business_name)) $errors[] = 'Business name is required.';
    if (empty($contact_person)) $errors[] = 'Contact person is required.';
    if (empty($phone)) $errors[] = 'Phone number is required.';
    if (empty($business_address)) $errors[] = 'Business address is required.';
    if (empty($service_areas)) $errors[] = 'At least one service area is required.';
    if (empty($service_types)) $errors[] = 'At least one service type is required.';
    if (empty($cida_registration)) $errors[] = 'CIDA registration number is required.';
    if (empty($cida_grade)) $errors[] = 'CIDA grade is required.';
    if (empty($business_description)) $errors[] = 'Business description is required.';
    
    // Phone validation
    if (!preg_match('/^[0-9+\-\s()]+$/', $phone)) {
        $errors[] = 'Invalid phone number format.';
    }
    
    // Website validation
    if (!empty($website) && !filter_var($website, FILTER_VALIDATE_URL)) {
        $errors[] = 'Invalid website URL.';
    }
    
    if (empty($errors)) {
        try {
            $stmt = $pdo->prepare("
                UPDATE contractor_profiles SET
                    business_name = ?, contact_person = ?, phone = ?, business_address = ?,
                    service_areas = ?, service_types = ?, cida_registration = ?, cida_grade = ?,
                    business_description = ?, website = ?, profile_image = ?, updated_at = NOW()
                WHERE user_id = ?
            ");

            $stmt->execute([
                $business_name,
                $contact_person,
                $phone,
                $business_address,
                json_encode($service_areas),
                json_encode($service_types),
                $cida_registration,
                $cida_grade,
                $business_description,
                $website ?: null,
                $profile_image_path,
                $_SESSION['user_id']
            ]);
            
            $_SESSION['success'] = 'Profile updated successfully!';
            header('Location: profile.php');
            exit();
            
        } catch (PDOException $e) {
            $errors[] = 'Database error. Please try again.';
        }
    }
}

// Decode JSON fields for display
$current_service_areas = json_decode($contractor['service_areas'], true) ?? [];
$current_service_types = json_decode($contractor['service_types'], true) ?? [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Profile & Services - BrickClick Contractor</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet"> 
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF6B35;
            --accent-yellow: #F7931E;
            --success-green: #28a745;
            --info-blue: #17a2b8;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --dark-gray: #343a40;
        }
        
        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e9ecef 100%);
            font-family: 'Open Sans', sans-serif;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            z-index: 1000;
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            flex-shrink: 0;
        }

        .sidebar-header h3 {
            color: var(--accent-orange);
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }

        .sidebar-header p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }

        .sidebar-nav {
            flex: 1;
            padding: 0.5rem 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .nav-item {
            margin: 0.15rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,107,53,0.1);
            color: var(--accent-orange);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--accent-orange), var(--primary-red));
            color: white;
            box-shadow: 0 3px 10px rgba(255,107,53,0.3);
        }

        .nav-link i {
            margin-right: 0.8rem;
            width: 18px;
            text-align: center;
            font-size: 1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--info-blue), #138496);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .page-title {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 2rem;
        }
        
        .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }
        
        .profile-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .form-section {
            margin-bottom: 2.5rem;
        }
        
        .section-title {
            color: var(--primary-dark);
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--accent-orange);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }

        .profile-picture-upload {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            border: 2px dashed #e9ecef;
            transition: all 0.3s ease;
        }

        .profile-picture-upload:hover {
            border-color: var(--accent-orange);
            background: rgba(255, 107, 53, 0.05);
        }

        .current-picture {
            flex-shrink: 0;
        }

        .profile-preview {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--accent-orange);
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .no-picture {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #e9ecef, #f8f9fa);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: var(--medium-gray);
            border: 3px solid #e9ecef;
        }

        .no-picture i {
            font-size: 2rem;
            margin-bottom: 0.25rem;
        }

        .no-picture span {
            font-size: 0.75rem;
            text-align: center;
        }

        .upload-controls {
            flex-grow: 1;
        }
        
        .form-label {
            color: var(--primary-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--accent-orange);
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }
        
        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
        }
        
        .form-check {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }
        
        .form-check:hover {
            background: #e9ecef;
        }
        
        .form-check-input:checked {
            background-color: var(--accent-orange);
            border-color: var(--accent-orange);
        }
        
        .btn-update {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-update:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
        
        .required {
            color: var(--primary-red);
        }
        
        .profile-summary {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .summary-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .summary-item:last-child {
            margin-bottom: 0;
        }
        
        .summary-item i {
            color: var(--info-blue);
            width: 20px;
            margin-right: 1rem;
        }
        
        .summary-label {
            font-weight: 600;
            color: var(--primary-dark);
            width: 120px;
            flex-shrink: 0;
        }
        
        .summary-value {
            color: var(--dark-gray);
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>BrickClick</h3>
            <p>Contractor Portal</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice-dollar"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="portfolio.php" class="nav-link">
                    <i class="fas fa-images"></i>Portfolio
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews & Ratings
                </a>
            </div>
            <div class="nav-item">
                <a href="profile.php" class="nav-link active">
                    <i class="fas fa-user-edit"></i>Profile & Services
                </a>
            </div>
            <div class="nav-item">
                <a href="payment_history.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payment History
                </a>
            </div>
            <div class="nav-item">
                <a href="notifications.php" class="nav-link">
                    <i class="fas fa-bell"></i>Notifications
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Profile & Services</h1>
            <p class="page-subtitle">Manage your business information and service offerings</p>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- Profile Summary -->
        <div class="profile-summary">
            <div class="row">
                <div class="col-md-6">
                    <div class="summary-item">
                        <i class="fas fa-building"></i>
                        <span class="summary-label">Business:</span>
                        <span class="summary-value"><?php echo htmlspecialchars($contractor['business_name']); ?></span>
                    </div>
                    <div class="summary-item">
                        <i class="fas fa-user"></i>
                        <span class="summary-label">Contact:</span>
                        <span class="summary-value"><?php echo htmlspecialchars($contractor['contact_person']); ?></span>
                    </div>
                    <div class="summary-item">
                        <i class="fas fa-envelope"></i>
                        <span class="summary-label">Email:</span>
                        <span class="summary-value"><?php echo htmlspecialchars($contractor['email']); ?></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="summary-item">
                        <i class="fas fa-phone"></i>
                        <span class="summary-label">Phone:</span>
                        <span class="summary-value"><?php echo htmlspecialchars($contractor['phone']); ?></span>
                    </div>
                    <div class="summary-item">
                        <i class="fas fa-certificate"></i>
                        <span class="summary-label">CIDA Grade:</span>
                        <span class="summary-value"><?php echo htmlspecialchars($contractor['cida_grade']); ?></span>
                    </div>
                    <div class="summary-item">
                        <i class="fas fa-star"></i>
                        <span class="summary-label">Rating:</span>
                        <span class="summary-value"><?php echo number_format($contractor['average_rating'], 1); ?> (<?php echo $contractor['total_reviews']; ?> reviews)</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Form -->
        <div class="profile-card">
            <form method="POST" action="" enctype="multipart/form-data">
                <!-- Business Information -->
                <div class="form-section">
                    <h3 class="section-title">Business Information</h3>

                    <!-- Profile Picture Upload -->
                    <div class="form-group mb-4">
                        <label class="form-label">Profile Picture</label>
                        <div class="profile-picture-upload">
                            <div class="current-picture">
                                <?php if ($contractor['profile_image']): ?>
                                    <img src="../uploads/profile_pictures/<?php echo htmlspecialchars($contractor['profile_image']); ?>"
                                         alt="Current Profile Picture" class="profile-preview">
                                <?php else: ?>
                                    <div class="no-picture">
                                        <i class="fas fa-user-circle"></i>
                                        <span>No profile picture</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="upload-controls">
                                <input type="file" class="form-control" name="profile_image" id="profile_image"
                                       accept="image/jpeg,image/jpg,image/png,image/gif">
                                <small class="form-text text-muted">
                                    Upload a professional profile picture (JPG, PNG, GIF - Max 5MB)
                                </small>
                                <div id="upload-status" class="mt-2"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Business Name <span class="required">*</span></label>
                                <input type="text" class="form-control" name="business_name" 
                                       value="<?php echo htmlspecialchars($_POST['business_name'] ?? $contractor['business_name']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Contact Person <span class="required">*</span></label>
                                <input type="text" class="form-control" name="contact_person" 
                                       value="<?php echo htmlspecialchars($_POST['contact_person'] ?? $contractor['contact_person']); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Phone Number <span class="required">*</span></label>
                                <input type="tel" class="form-control" name="phone" 
                                       value="<?php echo htmlspecialchars($_POST['phone'] ?? $contractor['phone']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Website (Optional)</label>
                                <input type="url" class="form-control" name="website" 
                                       value="<?php echo htmlspecialchars($_POST['website'] ?? $contractor['website']); ?>" 
                                       placeholder="https://www.example.com">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Business Address <span class="required">*</span></label>
                        <textarea class="form-control" name="business_address" rows="3" required><?php echo htmlspecialchars($_POST['business_address'] ?? $contractor['business_address']); ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Business Description <span class="required">*</span></label>
                        <textarea class="form-control" name="business_description" rows="4" 
                                  placeholder="Describe your business, experience, and specializations..." required><?php echo htmlspecialchars($_POST['business_description'] ?? $contractor['business_description']); ?></textarea>
                    </div>
                </div>
                
                <!-- CIDA Information -->
                <div class="form-section">
                    <h3 class="section-title">CIDA Registration</h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">CIDA Registration Number <span class="required">*</span></label>
                                <input type="text" class="form-control" name="cida_registration" 
                                       value="<?php echo htmlspecialchars($_POST['cida_registration'] ?? $contractor['cida_registration']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">CIDA Grade <span class="required">*</span></label>
                                <select class="form-select" name="cida_grade" required>
                                    <option value="">Select CIDA Grade</option>
                                    <?php foreach ($cida_grades as $grade): ?>
                                        <option value="<?php echo $grade; ?>" 
                                                <?php echo (($_POST['cida_grade'] ?? $contractor['cida_grade']) === $grade) ? 'selected' : ''; ?>>
                                            <?php echo $grade; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Service Areas -->
                <div class="form-section">
                    <h3 class="section-title">Service Areas <span class="required">*</span></h3>
                    <p class="text-muted mb-3">Select the districts where you provide services:</p>
                    
                    <div class="checkbox-grid">
                        <?php foreach ($districts as $district): ?>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="service_areas[]" 
                                       value="<?php echo $district; ?>" id="area_<?php echo $district; ?>"
                                       <?php echo in_array($district, $_POST['service_areas'] ?? $current_service_areas) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="area_<?php echo $district; ?>">
                                    <?php echo $district; ?>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Service Types -->
                <div class="form-section">
                    <h3 class="section-title">Service Types <span class="required">*</span></h3>
                    <p class="text-muted mb-3">Select the types of construction services you provide:</p>
                    
                    <div class="checkbox-grid">
                        <?php foreach ($service_categories as $category): ?>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="service_types[]"
                                       value="<?php echo $category['id']; ?>" id="service_<?php echo $category['id']; ?>"
                                       <?php echo in_array($category['id'], $_POST['service_types'] ?? $current_service_types) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="service_<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($category['name_en']); ?>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-update">
                    <i class="fas fa-save me-2"></i>Update Profile
                </button>
            </form>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // Profile picture preview
    document.getElementById('profile_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const statusDiv = document.getElementById('upload-status');

        if (file) {
            // Check file size (5MB limit)
            if (file.size > 5242880) {
                statusDiv.innerHTML = '<div class="alert alert-danger">File size must be less than 5MB</div>';
                this.value = '';
                return;
            }

            // Check file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                statusDiv.innerHTML = '<div class="alert alert-danger">Please select a valid image file (JPG, PNG, GIF)</div>';
                this.value = '';
                return;
            }

            statusDiv.innerHTML = '<div class="alert alert-success">File selected: ' + file.name + ' (' + (file.size / 1024 / 1024).toFixed(2) + ' MB)</div>';

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                const currentPicture = document.querySelector('.current-picture');
                currentPicture.innerHTML = '<img src="' + e.target.result + '" alt="Profile Preview" class="profile-preview">';
            };
            reader.readAsDataURL(file);
        } else {
            statusDiv.innerHTML = '';
        }
    });
    </script>
</body>
</html>
