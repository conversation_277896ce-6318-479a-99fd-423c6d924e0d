<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔍 Contractor Payment History Test</h2>";

// Test with different contractor IDs
$test_contractor_ids = [25, 1, 2, 3]; // Add more IDs as needed

foreach ($test_contractor_ids as $contractor_id) {
    echo "<h3>Testing Contractor ID: $contractor_id</h3>";
    
    try {
        // Get payment history for the contractor
        $stmt = $pdo->prepare("
            SELECT pp.*, qr.title as service_category, cp.first_name, cp.last_name, qres.quoted_amount,
                   qr.description, qr.location, qr.district
            FROM project_payments pp
            JOIN quote_responses qres ON pp.quote_response_id = qres.id
            JOIN quote_requests qr ON qres.quote_request_id = qr.id
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            WHERE pp.contractor_id = ?
            ORDER BY pp.created_at DESC
        ");
        $stmt->execute([$contractor_id]);
        $payments = $stmt->fetchAll();
        
        // Get payment statistics
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_payments,
                SUM(amount) as total_earned,
                SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END) as completed_earnings,
                SUM(CASE WHEN payment_status = 'pending' THEN amount ELSE 0 END) as pending_earnings
            FROM project_payments 
            WHERE contractor_id = ?
        ");
        $stmt->execute([$contractor_id]);
        $stats = $stmt->fetch();
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h4>📊 Payment Statistics:</h4>";
        echo "<p><strong>Total Payments:</strong> " . $stats['total_payments'] . "</p>";
        echo "<p><strong>Total Earned:</strong> Rs. " . number_format($stats['total_earned'] ?: 0) . "</p>";
        echo "<p><strong>Completed Earnings:</strong> Rs. " . number_format($stats['completed_earnings'] ?: 0) . "</p>";
        echo "<p><strong>Pending Earnings:</strong> Rs. " . number_format($stats['pending_earnings'] ?: 0) . "</p>";
        echo "</div>";
        
        if (empty($payments)) {
            echo "<p style='color: #666;'>No payments found for this contractor.</p>";
        } else {
            echo "<h4>💰 Payment History (" . count($payments) . " payments):</h4>";
            echo "<div style='background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>";
            
            foreach ($payments as $payment) {
                echo "<div style='padding: 15px; border-bottom: 1px solid #eee;'>";
                echo "<div style='display: flex; justify-content: space-between; align-items: start;'>";
                
                echo "<div>";
                echo "<h5 style='margin: 0 0 5px 0; color: #333;'>" . htmlspecialchars($payment['service_category']) . "</h5>";
                echo "<p style='margin: 0 0 5px 0; color: #666;'>";
                echo "<i class='fas fa-user'></i> " . htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']);
                echo "</p>";
                echo "<p style='margin: 0; color: #888; font-size: 0.9em;'>";
                echo "<i class='fas fa-map-marker-alt'></i> " . htmlspecialchars($payment['location'] . ', ' . $payment['district']);
                echo "</p>";
                echo "</div>";
                
                echo "<div style='text-align: right;'>";
                echo "<div style='font-size: 1.2em; font-weight: bold; color: #28a745;'>Rs. " . number_format($payment['amount']) . "</div>";
                echo "<div style='font-size: 0.9em; color: #666;'>";
                echo $payment['payment_date'] ? date('M j, Y', strtotime($payment['payment_date'])) : date('M j, Y', strtotime($payment['created_at']));
                echo "</div>";
                echo "</div>";
                
                echo "</div>";
                
                echo "<div style='margin-top: 10px; display: flex; justify-content: space-between; align-items: center;'>";
                echo "<div>";
                $status_color = $payment['payment_status'] === 'completed' ? '#28a745' : ($payment['payment_status'] === 'pending' ? '#ffc107' : '#dc3545');
                echo "<span style='background: $status_color; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em;'>";
                echo ucfirst($payment['payment_status']);
                echo "</span>";
                
                if ($payment['payment_type']) {
                    echo "<span style='margin-left: 10px; color: #666; font-size: 0.9em;'>";
                    echo "<i class='fas fa-info-circle'></i> " . ucfirst(str_replace('_', ' ', $payment['payment_type']));
                    echo "</span>";
                }
                echo "</div>";
                
                echo "<div style='color: #666; font-size: 0.9em;'>";
                echo "Transaction #" . $payment['id'];
                if ($payment['transaction_id']) {
                    echo " | " . htmlspecialchars($payment['transaction_id']);
                }
                echo "</div>";
                echo "</div>";
                
                echo "<div style='margin-top: 5px; font-size: 0.9em; color: #888;'>";
                echo "Quoted: Rs. " . number_format($payment['quoted_amount']);
                echo "</div>";
                
                echo "</div>";
            }
            echo "</div>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr style='margin: 30px 0;'>";
}

// Show all payments in the system
echo "<h3>🌍 All Payments in System</h3>";
try {
    $stmt = $pdo->query("
        SELECT pp.*, qr.title as service_category, cp.first_name, cp.last_name, 
               conp.business_name, qres.quoted_amount
        FROM project_payments pp
        JOIN quote_responses qres ON pp.quote_response_id = qres.id
        JOIN quote_requests qr ON qres.quote_request_id = qr.id
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN contractor_profiles conp ON pp.contractor_id = conp.user_id
        ORDER BY pp.created_at DESC
        LIMIT 10
    ");
    $all_payments = $stmt->fetchAll();
    
    if (empty($all_payments)) {
        echo "<p>No payments found in the system.</p>";
    } else {
        echo "<table style='width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>";
        echo "<thead style='background: #f8f9fa;'>";
        echo "<tr>";
        echo "<th style='padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;'>ID</th>";
        echo "<th style='padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;'>Contractor</th>";
        echo "<th style='padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;'>Customer</th>";
        echo "<th style='padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;'>Service</th>";
        echo "<th style='padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;'>Amount</th>";
        echo "<th style='padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;'>Status</th>";
        echo "<th style='padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;'>Date</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($all_payments as $payment) {
            echo "<tr style='border-bottom: 1px solid #f8f9fa;'>";
            echo "<td style='padding: 12px;'>" . $payment['id'] . "</td>";
            echo "<td style='padding: 12px;'>" . ($payment['business_name'] ?: 'ID: ' . $payment['contractor_id']) . "</td>";
            echo "<td style='padding: 12px;'>" . htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']) . "</td>";
            echo "<td style='padding: 12px;'>" . htmlspecialchars($payment['service_category']) . "</td>";
            echo "<td style='padding: 12px;'>Rs. " . number_format($payment['amount']) . "</td>";
            echo "<td style='padding: 12px;'>";
            $status_color = $payment['payment_status'] === 'completed' ? '#28a745' : ($payment['payment_status'] === 'pending' ? '#ffc107' : '#dc3545');
            echo "<span style='background: $status_color; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em;'>";
            echo ucfirst($payment['payment_status']);
            echo "</span>";
            echo "</td>";
            echo "<td style='padding: 12px;'>";
            echo $payment['payment_date'] ? date('M j, Y', strtotime($payment['payment_date'])) : date('M j, Y', strtotime($payment['created_at']));
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h2, h3, h4 {
    color: #333;
}

.fas {
    margin-right: 5px;
}
</style>
