<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔍 Debug Quotes Query</h2>";

// Simulate contractor login if not logged in
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'contractor') {
    // Get a test contractor
    $stmt = $pdo->query("
        SELECT u.id, u.email, cp.business_name 
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved' 
        LIMIT 1
    ");
    $contractor = $stmt->fetch();
    
    if ($contractor) {
        $_SESSION['user_id'] = $contractor['id'];
        $_SESSION['user_type'] = 'contractor';
        $_SESSION['email'] = $contractor['email'];
        echo "<p style='color: green;'>✅ Simulated login as: " . htmlspecialchars($contractor['business_name']) . " (ID: " . $contractor['id'] . ")</p>";
    } else {
        echo "<p style='color: red;'>❌ No contractors found. Please run fix_contractor_quotes_issue.php first.</p>";
        exit;
    }
}

$contractor_id = $_SESSION['user_id'];
echo "<p><strong>Testing for contractor ID:</strong> $contractor_id</p>";

try {
    // Test 1: Dashboard pending quotes query (NEW FIXED VERSION)
    echo "<h3>Test 1: Dashboard Pending Quotes Query (Fixed)</h3>";
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.id NOT IN (
            SELECT COALESCE(quote_request_id, 0)
            FROM quote_responses
            WHERE contractor_id = ?
        )
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
    $all_pending_quotes = $stmt->fetchAll();
    
    echo "<p>SQL returned: " . count($all_pending_quotes) . " quotes</p>";
    
    // Apply PHP filtering
    $pending_quotes = 0;
    foreach ($all_pending_quotes as $quote) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
        echo "<strong>Quote ID:</strong> " . $quote['id'] . "<br>";
        echo "<strong>Title:</strong> " . htmlspecialchars($quote['title']) . "<br>";
        echo "<strong>Service Category ID:</strong> " . $quote['service_category_id'] . "<br>";
        echo "<strong>District:</strong> " . $quote['district'] . "<br>";
        echo "<strong>Specific Contractor ID:</strong> " . ($quote['specific_contractor_id'] ?? 'NULL') . "<br>";
        
        // Always count direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $pending_quotes++;
            echo "<strong>Result:</strong> ✅ INCLUDED (Direct quote)<br>";
        } elseif ($quote['specific_contractor_id'] === null) {
            // For general quotes, check service and area match
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];

            echo "<strong>Contractor Services:</strong> " . implode(', ', $contractor_services) . "<br>";
            echo "<strong>Contractor Areas:</strong> " . implode(', ', $contractor_areas) . "<br>";

            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);

            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);

            echo "<strong>Has Service:</strong> " . ($has_service ? '✅ Yes' : '❌ No') . "<br>";
            echo "<strong>Has Area:</strong> " . ($has_area ? '✅ Yes' : '❌ No') . "<br>";

            if ($has_service && $has_area) {
                $pending_quotes++;
                echo "<strong>Result:</strong> ✅ INCLUDED (General quote match)<br>";
            } else {
                echo "<strong>Result:</strong> ❌ EXCLUDED (No match)<br>";
            }
        } else {
            echo "<strong>Result:</strong> ❌ EXCLUDED (Direct quote for other contractor)<br>";
        }
        echo "</div>";
    }
    
    echo "<p><strong>Dashboard should show: $pending_quotes pending quotes</strong></p>";
    
    // Test 2: Quotes page query (EXACT COPY)
    echo "<h3>Test 2: Quotes Page Query (Exact Copy)</h3>";
    
    // Simulate the exact logic from quotes.php
    $status_filter = $_GET['status'] ?? 'all';
    $search = $_GET['search'] ?? '';
    
    echo "<p>Status filter: $status_filter</p>";
    echo "<p>Search: " . ($search ?: 'none') . "</p>";
    
    // Build query conditions - Show quotes for this contractor only
    $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
    $params = [$contractor_id];

    if ($status_filter !== 'all') {
        if ($status_filter === 'responded') {
            $where_conditions[] = "qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?)";
            $params[] = $contractor_id;
        } elseif ($status_filter === 'pending') {
            $where_conditions[] = "qr.status = 'open' AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)";
            $params[] = $contractor_id;
        } elseif ($status_filter === 'completed') {
            $where_conditions[] = "qr.id IN (
                SELECT qres.quote_request_id
                FROM quote_responses qres
                JOIN project_payments pp ON qres.id = pp.quote_response_id
                WHERE qres.contractor_id = ?
                AND pp.payment_type = 'down_payment'
                AND pp.payment_status = 'completed'
            )";
            $params[] = $contractor_id;
        } elseif ($status_filter === 'cancelled') {
            $where_conditions[] = "qr.id IN (
                SELECT quote_request_id
                FROM quote_responses
                WHERE contractor_id = ?
                AND status = 'rejected'
            )";
            $params[] = $contractor_id;
        }
    } else {
        $where_conditions[] = "(qr.status IN ('open', 'completed', 'cancelled') OR qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?))";
        $params[] = $contractor_id;
    }

    if (!empty($search)) {
        $where_conditions[] = "(qr.service_category LIKE ? OR qr.description LIKE ? OR cp.first_name LIKE ? OR cp.last_name LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
    }

    $where_clause = implode(' AND ', $where_conditions);
    
    echo "<p><strong>WHERE clause:</strong> $where_clause</p>";
    echo "<p><strong>Parameters:</strong> " . implode(', ', $params) . "</p>";

    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
               cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");

    // Add contractor ID parameters for the subqueries and JOIN
    $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
    
    echo "<p><strong>Final query parameters:</strong> " . implode(', ', $query_params) . "</p>";
    
    $stmt->execute($query_params);
    $all_quotes = $stmt->fetchAll();
    
    echo "<p>SQL returned: " . count($all_quotes) . " quotes</p>";
    
    // Filter quotes in PHP to handle JSON logic properly
    $quotes = [];
    foreach ($all_quotes as $quote) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0; background: #f9f9f9;'>";
        echo "<strong>Quote ID:</strong> " . $quote['id'] . "<br>";
        echo "<strong>Title:</strong> " . htmlspecialchars($quote['title']) . "<br>";
        echo "<strong>Service:</strong> " . htmlspecialchars($quote['service_category'] ?? 'Unknown') . "<br>";
        echo "<strong>District:</strong> " . $quote['district'] . "<br>";
        echo "<strong>Status:</strong> " . $quote['status'] . "<br>";
        echo "<strong>Specific Contractor ID:</strong> " . ($quote['specific_contractor_id'] ?? 'NULL') . "<br>";
        
        // Always show direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $quotes[] = $quote;
            echo "<strong>Result:</strong> ✅ INCLUDED (Direct quote)<br>";
        } elseif ($quote['specific_contractor_id'] === null) {
            // For general quotes, check service and area match
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];

            echo "<strong>Contractor Services:</strong> " . implode(', ', $contractor_services) . "<br>";
            echo "<strong>Contractor Areas:</strong> " . implode(', ', $contractor_areas) . "<br>";

            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);

            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);

            echo "<strong>Has Service:</strong> " . ($has_service ? '✅ Yes' : '❌ No') . "<br>";
            echo "<strong>Has Area:</strong> " . ($has_area ? '✅ Yes' : '❌ No') . "<br>";

            if ($has_service && $has_area) {
                $quotes[] = $quote;
                echo "<strong>Result:</strong> ✅ INCLUDED (General quote match)<br>";
            } else {
                echo "<strong>Result:</strong> ❌ EXCLUDED (No match)<br>";
            }
        } else {
            echo "<strong>Result:</strong> ❌ EXCLUDED (Direct quote for other contractor)<br>";
        }
        echo "</div>";
    }
    
    echo "<p><strong>Quotes page should show: " . count($quotes) . " quotes</strong></p>";
    
    // Test 3: Check contractor profile data
    echo "<h3>Test 3: Contractor Profile Data</h3>";
    
    $stmt = $pdo->prepare("SELECT * FROM contractor_profiles WHERE user_id = ?");
    $stmt->execute([$contractor_id]);
    $profile = $stmt->fetch();
    
    if ($profile) {
        echo "<p><strong>Business Name:</strong> " . htmlspecialchars($profile['business_name']) . "</p>";
        echo "<p><strong>Service Areas (Raw):</strong> " . htmlspecialchars($profile['service_areas']) . "</p>";
        echo "<p><strong>Service Types (Raw):</strong> " . htmlspecialchars($profile['service_types']) . "</p>";
        
        $areas = json_decode($profile['service_areas'], true);
        $types = json_decode($profile['service_types'], true);
        
        echo "<p><strong>Service Areas (Parsed):</strong> " . (is_array($areas) ? implode(', ', $areas) : 'Invalid JSON') . "</p>";
        echo "<p><strong>Service Types (Parsed):</strong> " . (is_array($types) ? implode(', ', $types) : 'Invalid JSON') . "</p>";
    } else {
        echo "<p style='color: red;'>❌ No contractor profile found!</p>";
    }
    
    echo "<h3>✅ Debug Complete</h3>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>Dashboard should show: $pending_quotes pending quotes</li>";
    echo "<li>Quotes page should show: " . count($quotes) . " quotes</li>";
    echo "</ul>";
    
    if ($pending_quotes != count($quotes)) {
        echo "<p style='color: red;'>❌ MISMATCH! Dashboard and quotes page show different counts.</p>";
    } else {
        echo "<p style='color: green;'>✅ Dashboard and quotes page counts match!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
