<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

$payment_id = (int)($_GET['payment_id'] ?? 0);

if (!$payment_id) {
    $_SESSION['error'] = 'Invalid payment reference.';
    header('Location: quotes.php');
    exit();
}

// Get payment details
try {
    $stmt = $pdo->prepare("
        SELECT pp.*, qres.quoted_amount, qr.title, qr.description, qr.location, qr.district,
               cp.business_name, cp.contact_person, cp.phone,
               cust.first_name, cust.last_name
        FROM project_payments pp
        JOIN quote_responses qres ON pp.quote_response_id = qres.id
        JOIN quote_requests qr ON qres.quote_request_id = qr.id
        JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
        JOIN customer_profiles cust ON pp.customer_id = cust.user_id
        WHERE pp.id = ? AND pp.customer_id = ?
    ");
    $stmt->execute([$payment_id, $_SESSION['user_id']]);
    $payment = $stmt->fetch();
    
    if (!$payment) {
        $_SESSION['error'] = 'Payment record not found.';
        header('Location: quotes.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: quotes.php');
    exit();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Payment Successful - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Inter:wght@700;800&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .success-container {
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .success-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #28a745, #20c997);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .payment-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .btn-action {
            margin: 10px;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .btn-primary-custom {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,123,255,0.3);
            color: white;
        }
        
        .next-steps {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            text-align: left;
        }
        
        .step-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            background: #007bff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            font-size: 14px;
        }

        /* Star Rating Styles */
        .rating-stars {
            display: flex;
            justify-content: center;
            flex-direction: row-reverse;
            gap: 5px;
        }

        .rating-stars input[type="radio"] {
            display: none;
        }

        .rating-stars .star {
            cursor: pointer;
            font-size: 2rem;
            color: #ddd;
            transition: color 0.2s;
        }

        .rating-stars input[type="radio"]:checked ~ .star,
        .rating-stars .star:hover,
        .rating-stars .star:hover ~ .star {
            color: #ffc107;
        }
    </style>
</head>

<body>
    <div class="container-xxl bg-white p-0">
        <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <!-- Spinner End -->

        <!-- Navbar Start -->
        <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
            <a href="../index.php" class="navbar-brand ms-4 ms-lg-0">
                <h1 class="text-primary m-0"><img class="me-3" src="../img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
            </a>
            <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarCollapse">
                <div class="navbar-nav ms-auto p-4 p-lg-0">
                    <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                    <a href="contractors.php" class="nav-item nav-link">Find Contractors</a>
                    <a href="quotes.php" class="nav-item nav-link">My Quotes</a>
                    <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                    <a href="profile.php" class="nav-item nav-link">Profile</a>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($payment['first_name']); ?>
                        </a>
                        <div class="dropdown-menu rounded-0 m-0">
                            <a href="profile.php" class="dropdown-item">My Profile</a>
                            <a href="../logout.php" class="dropdown-item">Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
        <!-- Navbar End -->

        <!-- Success Content Start -->
        <div class="container-xxl py-5">
            <div class="success-container">
                
                <!-- Success Message -->
                <div class="success-card">
                    <div class="success-icon">
                        <i class="fas fa-check fa-2x text-white"></i>
                    </div>
                    
                    <h2 class="text-success mb-3">Payment Successful!</h2>
                    <p class="lead mb-4">Your down payment has been processed successfully.</p>
                    
                    <!-- Payment Details -->
                    <div class="payment-details">
                        <h5 class="mb-3"><i class="fas fa-receipt me-2"></i>Payment Details</h5>
                        
                        <div class="detail-row">
                            <span>Transaction ID:</span>
                            <strong><?php echo htmlspecialchars($payment['transaction_id']); ?></strong>
                        </div>
                        
                        <div class="detail-row">
                            <span>Payment Type:</span>
                            <strong><?php echo ucfirst(str_replace('_', ' ', $payment['payment_type'])); ?></strong>
                        </div>
                        
                        <div class="detail-row">
                            <span>Amount Paid:</span>
                            <strong class="text-success">Rs. <?php echo number_format($payment['amount'], 2); ?></strong>
                        </div>
                        
                        <div class="detail-row">
                            <span>Payment Method:</span>
                            <strong><?php echo ucfirst($payment['payment_method']); ?></strong>
                        </div>
                        
                        <div class="detail-row">
                            <span>Payment Date:</span>
                            <strong><?php echo date('M j, Y g:i A', strtotime($payment['payment_date'])); ?></strong>
                        </div>
                        
                        <div class="detail-row">
                            <span>Project:</span>
                            <strong><?php echo htmlspecialchars($payment['title']); ?></strong>
                        </div>
                        
                        <div class="detail-row">
                            <span>Contractor:</span>
                            <strong><?php echo htmlspecialchars($payment['business_name']); ?></strong>
                        </div>
                        
                        <div class="detail-row">
                            <span>Remaining Amount:</span>
                            <strong>Rs. <?php echo number_format($payment['quoted_amount'] - $payment['amount'], 2); ?></strong>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="mt-4">
                        <a href="quotes.php" class="btn btn-primary-custom btn-action">
                            <i class="fas fa-list me-2"></i>View My Quotes
                        </a>
                        <a href="dashboard.php" class="btn btn-outline-primary btn-action">
                            <i class="fas fa-home me-2"></i>Dashboard
                        </a>
                        <button type="button" class="btn btn-success btn-action" data-bs-toggle="modal" data-bs-target="#reviewModal">
                            <i class="fas fa-star me-2"></i>Rate & Review Contractor
                        </button>
                    </div>
                </div>
                
                <!-- Next Steps -->
                <div class="success-card">
                    <div class="next-steps">
                        <h5 class="mb-3"><i class="fas fa-tasks me-2"></i>What Happens Next?</h5>
                        
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div>
                                <strong>Contractor Notification</strong><br>
                                <small class="text-muted">Your contractor has been notified about the payment and will contact you soon.</small>
                            </div>
                        </div>
                        
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div>
                                <strong>Project Planning</strong><br>
                                <small class="text-muted">The contractor will schedule a meeting to discuss project timeline and details.</small>
                            </div>
                        </div>
                        
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div>
                                <strong>Work Commencement</strong><br>
                                <small class="text-muted">Construction work will begin as per the agreed timeline.</small>
                            </div>
                        </div>
                        
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div>
                                <strong>Progress Updates</strong><br>
                                <small class="text-muted">You'll receive regular updates on project progress through our platform.</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Info -->
                <div class="text-center">
                    <p class="text-muted">
                        <i class="fas fa-info-circle me-2"></i>
                        If you have any questions, please contact us at 
                        <a href="mailto:<EMAIL>"><EMAIL></a> or 
                        <a href="tel:+94112345678">+94 11 234 5678</a>
                    </p>
                </div>
            </div>
        </div>
        <!-- Success Content End -->

      
          
        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

    <?php
    // Include the review form component
    require_once 'review_form_component.php';

    // Prepare data for the review form
    $contractor_data = [
        'contractor_id' => $payment['contractor_id'],
        'business_name' => $payment['business_name']
    ];

    $payment_data = [
        'payment_id' => $payment['id'],
        'quote_response_id' => $payment['quote_response_id']
    ];

    // Render the review form
    renderReviewForm($contractor_data, $payment_data, 'reviewModal');
    ?>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
    
    <script>
        // Auto-hide spinner after page load
        window.addEventListener('load', function() {
            setTimeout(function() {
                document.getElementById('spinner').classList.remove('show');
            }, 500);
        });

        // Star rating functionality
        document.addEventListener('DOMContentLoaded', function() {
            const ratingInputs = document.querySelectorAll('input[name="rating"]');
            const ratingText = document.getElementById('ratingText');

            const ratingTexts = {
                '1': 'Poor - Very unsatisfied',
                '2': 'Fair - Somewhat unsatisfied',
                '3': 'Good - Satisfied',
                '4': 'Very Good - Very satisfied',
                '5': 'Excellent - Extremely satisfied'
            };

            ratingInputs.forEach(input => {
                input.addEventListener('change', function() {
                    ratingText.textContent = ratingTexts[this.value];
                    ratingText.className = 'text-warning fw-bold';
                });
            });
        });
    </script>
</body>
</html>
