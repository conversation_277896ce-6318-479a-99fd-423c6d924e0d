<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

// Get customer profile
try {
    $stmt = $pdo->prepare("SELECT cp.*, u.email FROM customer_profiles cp JOIN users u ON cp.user_id = u.id WHERE cp.user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $customer = $stmt->fetch();
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: ../login.php');
    exit();
}

// Get unread notifications count
$unread_count = 0;
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$_SESSION['user_id']]);
    $unread_count = $stmt->fetchColumn();
} catch (PDOException $e) {
    // Ignore error, keep count as 0
}

// Get quote requests with responses
try {
    $stmt = $pdo->prepare("
        SELECT qr.*, sc.name_en as service_name,
               (SELECT COUNT(*) FROM quote_responses qres WHERE qres.quote_request_id = qr.id) as response_count,
               (SELECT COUNT(*) FROM quote_responses qres WHERE qres.quote_request_id = qr.id AND qres.status = 'pending') as pending_responses
        FROM quote_requests qr 
        JOIN service_categories sc ON qr.service_category_id = sc.id 
        WHERE qr.customer_id = ? 
        ORDER BY qr.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $quote_requests = $stmt->fetchAll();
} catch (PDOException $e) {
    $quote_requests = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>My Quotes - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, my quotes" name="keywords">
    <meta content="My Quotes - Brick & Click" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <style>
        .quotes-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .quotes-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
            z-index: 1;
        }

        .quotes-header .container {
            position: relative;
            z-index: 2;
        }

        .quote-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: all 0.4s ease;
            border: none;
            position: relative;
        }

        .quote-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quote-card:hover::before {
            left: 100%;
        }

        .quote-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 60px rgba(0,0,0,0.15);
        }
        
        .quote-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .quote-body {
            padding: 1.5rem;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .status-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .status-open {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-closed {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-cancelled {
            background: linear-gradient(135deg, #f1f3f4, #e9ecef);
            color: #5f6368;
            border: 1px solid #e9ecef;
        }
        
        .response-indicator {
            background: #fff3cd;
            color: #856404;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            font-size: 0.9rem;
            margin-top: 1rem;
        }
        
        .response-indicator.has-responses {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .service-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 10px 25px rgba(253, 126, 20, 0.3);
            transition: all 0.3s ease;
            animation: float 3s ease-in-out infinite;
        }

        .service-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 35px rgba(253, 126, 20, 0.4);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-3px); }
        }
        
        .quote-meta {
            display: flex;
            gap: 2rem;
            margin-top: 1rem;
        }
        
        .quote-meta-item {
            display: flex;
            align-items: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .quote-meta-item i {
            margin-right: 0.5rem;
            color: #fd7e14;
        }
        
        .empty-state {
            text-align: center;
            padding: 5rem 3rem;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 25px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .empty-state i {
            font-size: 4rem;
            color: #fd7e14;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .btn-view-responses {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-view-responses::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-view-responses:hover::before {
            left: 100%;
        }

        .btn-view-responses:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .stats-row {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 25px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow: 0 20px 50px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .stat-item {
            text-align: center;
            position: relative;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            animation: countUp 2s ease-out;
        }

        @keyframes countUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stat-label {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            margin: 0 auto 1rem;
            box-shadow: 0 5px 15px rgba(253, 126, 20, 0.3);
        }
    </style>
</head>

<body>
    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
        <a href="../index.php" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="../img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="contractors.php" class="nav-item nav-link">Find Contractors</a>
                <a href="quotes.php" class="nav-item nav-link active">My Quotes</a>
                <a href="cost_estimator.php" class="nav-item nav-link">Cost Estimator</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
            </div>
            <div class="dropdown">
                <a href="#" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($customer['first_name']); ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="notifications.php"><i class="fas fa-bell me-2"></i>Notifications <?php if ($unread_count > 0): ?><span class="badge bg-danger"><?php echo $unread_count; ?></span><?php endif; ?></a></li>
                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Quotes Header Start -->
    <div class="quotes-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 mb-3">My Quote Requests</h1>
                    <p class="fs-5 mb-0">Manage your quote requests and view contractor responses</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <a href="request_quote.php" class="btn btn-light btn-lg">
                        <i class="fas fa-plus me-2"></i>New Quote Request
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Quotes Header End -->

    <!-- Quotes Content Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>
            
            <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <!-- Stats Row -->
            <div class="stats-row">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="stat-number"><?php echo count($quote_requests); ?></div>
                            <div class="stat-label">Total Requests</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-number">
                                <?php echo count(array_filter($quote_requests, function($q) { return $q['status'] === 'open'; })); ?>
                            </div>
                            <div class="stat-label">Active Requests</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-reply"></i>
                            </div>
                            <div class="stat-number">
                                <?php echo array_sum(array_column($quote_requests, 'response_count')); ?>
                            </div>
                            <div class="stat-label">Total Responses</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-hourglass-half"></i>
                            </div>
                            <div class="stat-number">
                                <?php echo array_sum(array_column($quote_requests, 'pending_responses')); ?>
                            </div>
                            <div class="stat-label">Pending Reviews</div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (empty($quote_requests)): ?>
            <!-- Empty State -->
            <div class="empty-state">
                <i class="fas fa-file-alt fa-4x text-muted mb-4"></i>
                <h3>No Quote Requests Yet</h3>
                <p class="text-muted mb-4">Start by requesting quotes from verified contractors for your construction project.</p>
                <a href="request_quote.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>Request Your First Quote
                </a>
            </div>
            <?php else: ?>
            
            <!-- Quote Requests List -->
            <?php foreach ($quote_requests as $quote): ?>
            <div class="quote-card">
                <div class="quote-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="service-icon me-3">
                                    <i class="fas fa-tools"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1"><?php echo htmlspecialchars($quote['title']); ?></h5>
                                    <p class="text-muted mb-0"><?php echo htmlspecialchars($quote['service_name']); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <span class="status-badge status-<?php echo $quote['status']; ?>">
                                <?php echo ucfirst($quote['status']); ?>
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="quote-body">
                    <p class="mb-3"><?php echo htmlspecialchars(substr($quote['description'], 0, 200)); ?>...</p>
                    
                    <div class="quote-meta">
                        <div class="quote-meta-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <?php echo htmlspecialchars($quote['location']); ?>, <?php echo htmlspecialchars($quote['district']); ?>
                        </div>
                        <div class="quote-meta-item">
                            <i class="fas fa-calendar"></i>
                            <?php echo date('M j, Y', strtotime($quote['created_at'])); ?>
                        </div>
                        <?php if ($quote['estimated_budget']): ?>
                        <div class="quote-meta-item">
                            <i class="fas fa-dollar-sign"></i>
                            LKR <?php echo number_format($quote['estimated_budget']); ?>
                        </div>
                        <?php endif; ?>
                        <?php if ($quote['project_timeline']): ?>
                        <div class="quote-meta-item">
                            <i class="fas fa-clock"></i>
                            <?php echo htmlspecialchars($quote['project_timeline']); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($quote['response_count'] > 0): ?>
                    <div class="response-indicator has-responses">
                        <i class="fas fa-reply me-2"></i>
                        <?php echo $quote['response_count']; ?> contractor response(s) received
                        <?php if ($quote['pending_responses'] > 0): ?>
                        • <?php echo $quote['pending_responses']; ?> pending review
                        <?php endif; ?>
                    </div>
                    <div class="mt-3">
                        <a href="quote_responses.php?id=<?php echo $quote['id']; ?>" class="btn btn-view-responses">
                            <i class="fas fa-eye me-2"></i>View Responses
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="response-indicator">
                        <i class="fas fa-hourglass-half me-2"></i>
                        Waiting for contractor responses...
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
            
            <?php endif; ?>
        </div>
    </div>
    <!-- Quotes Content End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer mt-5 pt-5 px-0">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Brick & Click</h3>
                    <p class="mb-2 text-light">Sri Lanka's leading construction contractor marketplace</p>
                    <p class="mb-2 text-light"><i class="fa fa-phone-alt text-primary me-3"></i>+94 77 123 4567</p>
                    <p class="mb-2 text-light"><i class="fa fa-envelope text-primary me-3"></i><EMAIL></p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Quick Links</h3>
                    <a class="btn btn-link text-light" href="contractors.php">Find Contractors</a>
                    <a class="btn btn-link text-light" href="quotes.php">My Quotes</a>
                    <a class="btn btn-link text-light" href="favorites.php">Favorites</a>
                    <a class="btn btn-link text-light" href="profile.php">Profile</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Services</h3>
                    <a class="btn btn-link text-light" href="#">House Construction</a>
                    <a class="btn btn-link text-light" href="#">Renovation</a>
                    <a class="btn btn-link text-light" href="#">Roofing</a>
                    <a class="btn btn-link text-light" href="#">Electrical</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Support</h3>
                    <a class="btn btn-link text-light" href="#">Help Center</a>
                    <a class="btn btn-link text-light" href="#">Contact Support</a>
                    <a class="btn btn-link text-light" href="#">Terms of Service</a>
                    <a class="btn btn-link text-light" href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="container-fluid copyright">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        <span class="text-light">&copy; <a href="#" class="text-primary">Brick & Click</a>, All Right Reserved.</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
</body>
</html>
