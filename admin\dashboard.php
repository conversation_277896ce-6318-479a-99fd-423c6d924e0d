<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

// Get dashboard statistics
try {
    // Total counts
    $stmt = $pdo->query("SELECT COUNT(*) as total_customers FROM users WHERE user_type = 'customer'");
    $total_customers = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total_contractors FROM users WHERE user_type = 'contractor'");
    $total_contractors = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as pending_contractors FROM users WHERE user_type = 'contractor' AND status = 'pending'");
    $pending_contractors = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total_quotes FROM quote_requests");
    $total_quotes = $stmt->fetchColumn();
    
    // Check which review field structure we're using
    $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'status'");
    $has_status_field = $stmt->fetch();

    if ($has_status_field) {
        $stmt = $pdo->query("SELECT COUNT(*) as total_reviews FROM reviews WHERE (status IS NULL OR status != 'deleted')");
    } else {
        $stmt = $pdo->query("SELECT COUNT(*) as total_reviews FROM reviews WHERE (is_approved IS NULL OR is_approved != 0)");
    }
    $total_reviews = $stmt->fetchColumn();
    
    // Recent activity
    $stmt = $pdo->query("SELECT COUNT(*) as new_registrations FROM users WHERE DATE(created_at) = CURDATE()");
    $new_registrations = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as new_quotes FROM quote_requests WHERE DATE(created_at) = CURDATE()");
    $new_quotes = $stmt->fetchColumn();
    
    // Top rated contractors
    $stmt = $pdo->prepare("
        SELECT cp.business_name, cp.average_rating, cp.total_reviews, u.email
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' AND cp.total_reviews > 0
        ORDER BY cp.average_rating DESC, cp.total_reviews DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $top_contractors = $stmt->fetchAll();
    
    // Service category statistics
    $stmt = $pdo->prepare("
        SELECT sc.name_en, COUNT(qr.id) as quote_count
        FROM service_categories sc
        LEFT JOIN quote_requests qr ON sc.id = qr.service_category_id
        WHERE sc.is_active = 1
        GROUP BY sc.id, sc.name_en
        ORDER BY quote_count DESC
        LIMIT 8
    ");
    $stmt->execute();
    $service_stats = $stmt->fetchAll();
    
    // Monthly registration trends (last 6 months)
    $stmt = $pdo->prepare("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(CASE WHEN user_type = 'customer' THEN 1 END) as customers,
            COUNT(CASE WHEN user_type = 'contractor' THEN 1 END) as contractors
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
    ");
    $stmt->execute();
    $monthly_trends = $stmt->fetchAll();
    
    // Recent activities
    $stmt = $pdo->prepare("
        SELECT 'registration' as type, u.email, u.user_type, u.created_at as activity_date
        FROM users u
        WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        UNION ALL
        SELECT 'quote' as type, CONCAT(cp.first_name, ' ', cp.last_name) as email, 'quote_request' as user_type, qr.created_at as activity_date
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        WHERE qr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY activity_date DESC
        LIMIT 10
    ");
    $stmt->execute();
    $recent_activities = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'Database error: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Admin Dashboard - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF8C00;
            --accent-yellow: #FFD700;
            --light-gray: #F8F9FA;
            --medium-gray: #6C757D;
            --success-green: #28A745;
            --warning-orange: #FFC107;
            --info-blue: #17A2B8;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f5f6fa;
            color: var(--primary-dark);
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-header h3 {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }
        
        .sidebar-header p {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 1rem 1.5rem;
            border-radius: 12px;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 15px rgba(197, 23, 46, 0.3);
        }
        
        .nav-link i {
            width: 20px;
            margin-right: 1rem;
            font-size: 1.1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .top-bar {
            background: white;
            border-radius: 15px;
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-actions {
            margin-left: 2rem;
        }
        
        .welcome-text h2 {
            color: var(--primary-dark);
            font-weight: 700;
            margin: 0;
        }
        
        .welcome-text p {
            color: var(--medium-gray);
            margin: 0;
        }
        
        .admin-actions {
            display: flex;
            gap: 1rem;
        }
        
        .btn-admin {
            background: linear-gradient(135deg, var(--primary-red), #e41e3f);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }
        
        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(197, 23, 46, 0.3);
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        
        .stat-card.customers::before {
            background: linear-gradient(90deg, var(--info-blue), #20c997);
        }
        
        .stat-card.contractors::before {
            background: linear-gradient(90deg, var(--primary-red), var(--accent-orange));
        }
        
        .stat-card.quotes::before {
            background: linear-gradient(90deg, var(--accent-orange), var(--accent-yellow));
        }
        
        .stat-card.reviews::before {
            background: linear-gradient(90deg, var(--success-green), #20c997);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .stat-card.customers .stat-icon {
            background: linear-gradient(135deg, var(--info-blue), #20c997);
        }
        
        .stat-card.contractors .stat-icon {
            background: linear-gradient(135deg, var(--primary-red), var(--accent-orange));
        }
        
        .stat-card.quotes .stat-icon {
            background: linear-gradient(135deg, var(--accent-orange), var(--accent-yellow));
        }
        
        .stat-card.reviews .stat-icon {
            background: linear-gradient(135deg, var(--success-green), #20c997);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-dark);
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: var(--medium-gray);
            font-weight: 500;
            margin-bottom: 1rem;
        }
        
        .stat-change {
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .stat-change.positive {
            color: var(--success-green);
        }
        
        .stat-change.negative {
            color: var(--primary-red);
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
            height: 450px;
        }

        .chart-canvas {
            position: relative;
            height: 300px !important;
            width: 100% !important;
        }
        
        .chart-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .chart-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-dark);
            margin: 0;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        
        .table {
            margin: 0;
        }
        
        .table th {
            border: none;
            background: var(--light-gray);
            color: var(--primary-dark);
            font-weight: 600;
            padding: 1rem;
        }
        
        .table td {
            border: none;
            padding: 1rem;
            vertical-align: middle;
        }
        
        .rating-stars {
            color: var(--accent-yellow);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1rem;
            color: white;
        }
        
        .activity-icon.registration {
            background: linear-gradient(135deg, var(--info-blue), #20c997);
        }
        
        .activity-icon.quote {
            background: linear-gradient(135deg, var(--accent-orange), var(--accent-yellow));
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 600;
            color: var(--primary-dark);
            margin: 0;
        }
        
        .activity-time {
            color: var(--medium-gray);
            font-size: 0.9rem;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-shield-alt me-2"></i>Admin Panel</h3>
            <p>Brick & Click Management</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="contractors.php" class="nav-link">
                    <i class="fas fa-hard-hat"></i>Contractors
                </a>
            </div>
            <div class="nav-item">
                <a href="customers.php" class="nav-link">
                    <i class="fas fa-users"></i>Customers
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews
                </a>
            </div>
            <div class="nav-item">
                <a href="payments.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payments
                </a>
            </div>
            <div class="nav-item">
                <a href="reports.php" class="nav-link">
                    <i class="fas fa-chart-bar"></i>Reports
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="welcome-text">
                <h2>Welcome back, Admin!</h2>
                <p>Here's what's happening with your platform today</p>
            </div>
            <div class="admin-actions">
                <a href="contractors.php?status=pending" class="btn btn-admin">
                    <i class="fas fa-clock me-2"></i>Pending Approvals (<?php echo $pending_contractors; ?>)
                </a>
            </div>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card customers">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?php echo number_format($total_customers); ?></div>
                <div class="stat-label">Total Customers</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up me-1"></i>+<?php echo $new_registrations; ?> today
                </div>
            </div>

            <div class="stat-card contractors">
                <div class="stat-icon">
                    <i class="fas fa-hard-hat"></i>
                </div>
                <div class="stat-number"><?php echo number_format($total_contractors); ?></div>
                <div class="stat-label">Total Contractors</div>
                <div class="stat-change <?php echo $pending_contractors > 0 ? 'negative' : 'positive'; ?>">
                    <i class="fas fa-clock me-1"></i><?php echo $pending_contractors; ?> pending
                </div>
            </div>

            <div class="stat-card quotes">
                <div class="stat-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="stat-number"><?php echo number_format($total_quotes); ?></div>
                <div class="stat-label">Quote Requests</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up me-1"></i>+<?php echo $new_quotes; ?> today
                </div>
            </div>

            <div class="stat-card reviews">
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-number"><?php echo number_format($total_reviews); ?></div>
                <div class="stat-label">Total Reviews</div>
                <div class="stat-change positive">
                    <i class="fas fa-check me-1"></i>Active
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row">
            <div class="col-lg-8">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3 class="chart-title">Registration Trends</h3>
                        <small class="text-muted">Last 6 months</small>
                    </div>
                    <canvas id="registrationChart" style="max-height: 300px;"></canvas>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3 class="chart-title">Service Categories</h3>
                        <small class="text-muted">Quote requests by category</small>
                    </div>
                    <canvas id="serviceChart" style="max-height: 300px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Tables Row -->
        <div class="row">
            <div class="col-lg-8">
                <div class="table-container">
                    <h3 class="chart-title mb-3">Top Rated Contractors</h3>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Business Name</th>
                                    <th>Rating</th>
                                    <th>Reviews</th>
                                    <th>Email</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($top_contractors as $contractor): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($contractor['business_name']); ?></strong>
                                    </td>
                                    <td>
                                        <div class="rating-stars">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <i class="fas fa-star<?php echo $i <= $contractor['average_rating'] ? '' : '-o'; ?>"></i>
                                            <?php endfor; ?>
                                            <span class="ms-1"><?php echo number_format($contractor['average_rating'], 1); ?></span>
                                        </div>
                                    </td>
                                    <td><?php echo $contractor['total_reviews']; ?></td>
                                    <td><?php echo htmlspecialchars($contractor['email']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="table-container">
                    <h3 class="chart-title mb-3">Recent Activity</h3>
                    <div class="activity-list">
                        <?php foreach ($recent_activities as $activity): ?>
                        <div class="activity-item">
                            <div class="activity-icon <?php echo $activity['type']; ?>">
                                <i class="fas fa-<?php echo $activity['type'] === 'registration' ? 'user-plus' : 'file-invoice'; ?>"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">
                                    <?php if ($activity['type'] === 'registration'): ?>
                                        New <?php echo ucfirst($activity['user_type']); ?> Registration
                                    <?php else: ?>
                                        New Quote Request
                                    <?php endif; ?>
                                </div>
                                <div class="activity-time">
                                    <?php echo htmlspecialchars($activity['email']); ?> •
                                    <?php echo date('M j, g:i A', strtotime($activity['activity_date'])); ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart.js Scripts -->
    <script>
        // Registration Trends Chart
        const registrationCtx = document.getElementById('registrationChart').getContext('2d');
        const registrationChart = new Chart(registrationCtx, {
            type: 'line',
            data: {
                labels: [<?php echo "'" . implode("','", array_column($monthly_trends, 'month')) . "'"; ?>],
                datasets: [{
                    label: 'Customers',
                    data: [<?php echo implode(',', array_column($monthly_trends, 'customers')); ?>],
                    borderColor: '#17A2B8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Contractors',
                    data: [<?php echo implode(',', array_column($monthly_trends, 'contractors')); ?>],
                    borderColor: '#C5172E',
                    backgroundColor: 'rgba(197, 23, 46, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                }
            }
        });

        // Service Categories Chart
        const serviceCtx = document.getElementById('serviceChart').getContext('2d');
        const serviceChart = new Chart(serviceCtx, {
            type: 'doughnut',
            data: {
                labels: [<?php echo "'" . implode("','", array_column($service_stats, 'name_en')) . "'"; ?>],
                datasets: [{
                    data: [<?php echo implode(',', array_column($service_stats, 'quote_count')); ?>],
                    backgroundColor: [
                        '#C5172E',
                        '#FF8C00',
                        '#FFD700',
                        '#28A745',
                        '#17A2B8',
                        '#6F42C1',
                        '#E83E8C',
                        '#FD7E14'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
