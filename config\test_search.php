<?php
require_once 'database.php';

echo "<h2>🔍 Testing Search Functionality</h2>";

try {
    // Test 1: Search for House Construction (category 1)
    echo "<h3>Test 1: Search for House Construction (Category ID: 1)</h3>";
    
    $category = 1;
    $stmt = $pdo->prepare("
        SELECT cp.business_name, cp.service_types, cp.service_areas, u.status
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        AND JSON_CONTAINS(cp.service_types, ?)
        LIMIT 10
    ");
    $stmt->execute([json_encode($category)]);
    $results = $stmt->fetchAll();
    
    echo "<p><strong>Found " . count($results) . " contractors for House Construction</strong></p>";
    
    if (!empty($results)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Business Name</th><th>Service Types</th><th>Service Areas</th></tr>";
        
        foreach ($results as $contractor) {
            $services = json_decode($contractor['service_types'], true);
            $areas = json_decode($contractor['service_areas'], true);
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
            echo "<td>" . (is_array($services) ? implode(', ', $services) : 'None') . "</td>";
            echo "<td>" . (is_array($areas) ? implode(', ', $areas) : 'None') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test 2: Search for Vavuniya location
    echo "<h3>Test 2: Search for Vavuniya Location</h3>";
    
    $location = 'Vavuniya';
    $stmt = $pdo->prepare("
        SELECT cp.business_name, cp.service_types, cp.service_areas, u.status
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        AND JSON_CONTAINS(cp.service_areas, ?)
        LIMIT 10
    ");
    $stmt->execute([json_encode($location)]);
    $results = $stmt->fetchAll();
    
    echo "<p><strong>Found " . count($results) . " contractors in Vavuniya</strong></p>";
    
    if (!empty($results)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Business Name</th><th>Service Types</th><th>Service Areas</th></tr>";
        
        foreach ($results as $contractor) {
            $services = json_decode($contractor['service_types'], true);
            $areas = json_decode($contractor['service_areas'], true);
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
            echo "<td>" . (is_array($services) ? implode(', ', $services) : 'None') . "</td>";
            echo "<td>" . (is_array($areas) ? implode(', ', $areas) : 'None') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test 3: Combined search (House Construction + Vavuniya)
    echo "<h3>Test 3: Combined Search (House Construction + Vavuniya)</h3>";
    
    $stmt = $pdo->prepare("
        SELECT cp.business_name, cp.service_types, cp.service_areas, u.status
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        AND JSON_CONTAINS(cp.service_types, ?)
        AND JSON_CONTAINS(cp.service_areas, ?)
        LIMIT 10
    ");
    $stmt->execute([json_encode(1), json_encode('Vavuniya')]);
    $results = $stmt->fetchAll();
    
    echo "<p><strong>Found " . count($results) . " contractors for House Construction in Vavuniya</strong></p>";
    
    if (!empty($results)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Business Name</th><th>Service Types</th><th>Service Areas</th></tr>";
        
        foreach ($results as $contractor) {
            $services = json_decode($contractor['service_types'], true);
            $areas = json_decode($contractor['service_areas'], true);
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
            echo "<td>" . (is_array($services) ? implode(', ', $services) : 'None') . "</td>";
            echo "<td>" . (is_array($areas) ? implode(', ', $areas) : 'None') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test 4: Check service category mapping
    echo "<h3>Test 4: Service Category Mapping</h3>";
    
    $stmt = $pdo->prepare("SELECT id, name_en FROM service_categories WHERE is_active = 1 ORDER BY id");
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Service Name</th></tr>";
    
    foreach ($categories as $cat) {
        echo "<tr>";
        echo "<td>" . $cat['id'] . "</td>";
        echo "<td>" . htmlspecialchars($cat['name_en']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
