<?php
require_once 'config/database.php';

echo "<h2>Debug General Quote Request Issue</h2>";

try {
    // Check if specific_contractor_id column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        echo "<p style='color: red;'>❌ Missing specific_contractor_id column in quote_requests table</p>";
        echo "<p>Running migration...</p>";
        
        $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        $pdo->exec("ALTER TABLE quote_requests ADD INDEX idx_specific_contractor_id (specific_contractor_id)");
        $pdo->exec("ALTER TABLE quote_requests ADD FOREIGN KEY (specific_contractor_id) REFERENCES users(id) ON DELETE SET NULL");
        
        echo "<p style='color: green;'>✅ Added specific_contractor_id column</p>";
    } else {
        echo "<p style='color: green;'>✅ specific_contractor_id column exists</p>";
    }
    
    // Check contractors data
    echo "<h3>Contractor Data Analysis</h3>";
    $stmt = $pdo->query("
        SELECT u.id, u.status, cp.business_name, cp.service_areas, cp.service_types 
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' 
        LIMIT 5
    ");
    $contractors = $stmt->fetchAll();
    
    echo "<p>Found " . count($contractors) . " contractors</p>";
    
    foreach ($contractors as $contractor) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo "<strong>ID:</strong> " . $contractor['id'] . "<br>";
        echo "<strong>Status:</strong> " . $contractor['status'] . "<br>";
        echo "<strong>Business:</strong> " . htmlspecialchars($contractor['business_name']) . "<br>";
        echo "<strong>Service Areas:</strong> " . htmlspecialchars($contractor['service_areas']) . "<br>";
        echo "<strong>Service Types:</strong> " . htmlspecialchars($contractor['service_types']) . "<br>";
        
        // Decode and display
        $areas = json_decode($contractor['service_areas'], true);
        $types = json_decode($contractor['service_types'], true);
        
        echo "<strong>Decoded Areas:</strong> ";
        if ($areas) {
            echo implode(', ', $areas);
        } else {
            echo "NULL or invalid JSON";
        }
        echo "<br>";
        
        echo "<strong>Decoded Types:</strong> ";
        if ($types) {
            echo implode(', ', $types);
        } else {
            echo "NULL or invalid JSON";
        }
        echo "<br>";
        echo "</div>";
    }
    
    // Test the exact query used in process_quote_request.php
    echo "<h3>Testing Quote Request Query</h3>";
    
    $test_district = 'Colombo';
    $test_service_id = 1; // House Construction
    
    echo "<p>Testing with District: '$test_district' and Service ID: $test_service_id</p>";
    
    $stmt = $pdo->prepare("
        SELECT u.id, cp.business_name, cp.contact_person, cp.service_areas, cp.service_types
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.status = 'approved'
        AND JSON_CONTAINS(cp.service_areas, ?)
        AND JSON_CONTAINS(cp.service_types, ?)
    ");
    
    $district_json = json_encode($test_district);
    $service_json = json_encode($test_service_id);
    
    echo "<p>District JSON: $district_json</p>";
    echo "<p>Service JSON: $service_json</p>";
    
    $stmt->execute([$district_json, $service_json]);
    $matching_contractors = $stmt->fetchAll();
    
    echo "<p><strong>Found " . count($matching_contractors) . " matching contractors</strong></p>";
    
    foreach ($matching_contractors as $contractor) {
        echo "<div style='background: #e8f5e8; padding: 10px; margin: 5px 0;'>";
        echo "<strong>Match:</strong> " . htmlspecialchars($contractor['business_name']) . " (ID: " . $contractor['id'] . ")<br>";
        echo "<strong>Areas:</strong> " . htmlspecialchars($contractor['service_areas']) . "<br>";
        echo "<strong>Types:</strong> " . htmlspecialchars($contractor['service_types']) . "<br>";
        echo "</div>";
    }
    
    // Test alternative query approaches
    echo "<h3>Alternative Query Tests</h3>";
    
    // Test 1: Using LIKE instead of JSON_CONTAINS
    echo "<h4>Test 1: Using LIKE for areas</h4>";
    $stmt = $pdo->prepare("
        SELECT u.id, cp.business_name, cp.service_areas, cp.service_types
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.status = 'approved'
        AND cp.service_areas LIKE ?
        AND JSON_CONTAINS(cp.service_types, ?)
    ");
    
    $stmt->execute(["%\"$test_district\"%", $service_json]);
    $like_results = $stmt->fetchAll();
    echo "<p>LIKE query found: " . count($like_results) . " contractors</p>";
    
    // Test 2: Check if any contractors have the required service type
    echo "<h4>Test 2: Contractors with service type $test_service_id</h4>";
    $stmt = $pdo->prepare("
        SELECT u.id, cp.business_name, cp.service_types
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.status = 'approved'
        AND JSON_CONTAINS(cp.service_types, ?)
    ");
    
    $stmt->execute([$service_json]);
    $service_results = $stmt->fetchAll();
    echo "<p>Contractors with service type: " . count($service_results) . "</p>";
    
    // Test 3: Check if any contractors have the required area
    echo "<h4>Test 3: Contractors with area $test_district</h4>";
    $stmt = $pdo->prepare("
        SELECT u.id, cp.business_name, cp.service_areas
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.status = 'approved'
        AND JSON_CONTAINS(cp.service_areas, ?)
    ");
    
    $stmt->execute([$district_json]);
    $area_results = $stmt->fetchAll();
    echo "<p>Contractors with area: " . count($area_results) . "</p>";
    
    // Check recent quote requests
    echo "<h3>Recent Quote Requests</h3>";
    $stmt = $pdo->query("
        SELECT id, customer_id, service_category_id, title, district, specific_contractor_id, created_at
        FROM quote_requests 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $recent_quotes = $stmt->fetchAll();
    
    foreach ($recent_quotes as $quote) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
        echo "<strong>Quote ID:</strong> " . $quote['id'] . "<br>";
        echo "<strong>Service Category:</strong> " . $quote['service_category_id'] . "<br>";
        echo "<strong>District:</strong> " . $quote['district'] . "<br>";
        echo "<strong>Specific Contractor:</strong> " . ($quote['specific_contractor_id'] ?: 'General') . "<br>";
        echo "<strong>Created:</strong> " . $quote['created_at'] . "<br>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database Error: " . $e->getMessage() . "</p>";
}
?>
