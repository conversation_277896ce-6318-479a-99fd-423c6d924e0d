<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔧 Fix Contractor Profile</h2>";

// Get service categories to find plumbing
$stmt = $pdo->query("SELECT * FROM service_categories ORDER BY id");
$categories = $stmt->fetchAll();

echo "<h3>📋 Available Service Categories:</h3>";
foreach ($categories as $category) {
    echo "<p><strong>ID {$category['id']}:</strong> {$category['name_en']}</p>";
}

// Find plumbing service ID
$plumbing_id = null;
foreach ($categories as $category) {
    if (stripos($category['name_en'], 'plumb') !== false || stripos($category['name_en'], 'sanit') !== false) {
        $plumbing_id = $category['id'];
        echo "<p>🔧 <strong>Found plumbing service:</strong> ID {$plumbing_id} - {$category['name_en']}</p>";
        break;
    }
}

if (!$plumbing_id) {
    echo "<p>❌ No plumbing service category found! Let's check what services exist:</p>";
    foreach ($categories as $category) {
        echo "<p>- ID {$category['id']}: {$category['name_en']}</p>";
    }
    
    // Let's assume plumbing might be under a different name or we need to use a general service
    echo "<p>🔧 Let's use 'Electrical Work' (ID 6) as a test or check if there's a general construction service</p>";
    $plumbing_id = 6; // Assuming this exists
}

// Get the contractor
$stmt = $pdo->prepare("
    SELECT u.id, u.email, cp.*
    FROM users u
    LEFT JOIN contractor_profiles cp ON u.id = cp.user_id
    WHERE u.email = ?
");
$stmt->execute(['<EMAIL>']);
$contractor = $stmt->fetch();

if (!$contractor) {
    echo "<p>❌ Contractor not found!</p>";
    exit;
}

echo "<h3>👷 Current Contractor Profile:</h3>";
echo "<p><strong>ID:</strong> {$contractor['id']}</p>";
echo "<p><strong>Business:</strong> {$contractor['business_name']}</p>";
echo "<p><strong>Current Services:</strong> {$contractor['service_types']}</p>";
echo "<p><strong>Current Areas:</strong> {$contractor['service_areas']}</p>";

$current_services = json_decode($contractor['service_types'], true) ?: [];
$current_areas = json_decode($contractor['service_areas'], true) ?: [];

echo "<p><strong>Parsed Services:</strong> [" . implode(', ', $current_services) . "]</p>";
echo "<p><strong>Parsed Areas:</strong> [" . implode(', ', $current_areas) . "]</p>";

// Add plumbing service if not present
if (!in_array($plumbing_id, $current_services) && !in_array((string)$plumbing_id, $current_services)) {
    $current_services[] = $plumbing_id;
    echo "<p>✅ Adding plumbing service ID {$plumbing_id}</p>";
} else {
    echo "<p>✅ Contractor already has plumbing service</p>";
}

// Add Kalutara area if not present
if (!in_array('Kalutara', $current_areas)) {
    $current_areas[] = 'Kalutara';
    echo "<p>✅ Adding Kalutara area</p>";
} else {
    echo "<p>✅ Contractor already serves Kalutara</p>";
}

// Update the contractor profile
$new_services = json_encode($current_services);
$new_areas = json_encode($current_areas);

echo "<h3>🔄 Updating Profile:</h3>";
echo "<p><strong>New Services:</strong> {$new_services}</p>";
echo "<p><strong>New Areas:</strong> {$new_areas}</p>";

try {
    $stmt = $pdo->prepare("
        UPDATE contractor_profiles 
        SET service_types = ?, service_areas = ?
        WHERE user_id = ?
    ");
    $stmt->execute([$new_services, $new_areas, $contractor['id']]);
    
    echo "<p>✅ <strong>Profile updated successfully!</strong></p>";
    
    // Verify the update
    $stmt = $pdo->prepare("SELECT service_types, service_areas FROM contractor_profiles WHERE user_id = ?");
    $stmt->execute([$contractor['id']]);
    $updated = $stmt->fetch();
    
    echo "<h3>✅ Verification:</h3>";
    echo "<p><strong>Updated Services:</strong> {$updated['service_types']}</p>";
    echo "<p><strong>Updated Areas:</strong> {$updated['service_areas']}</p>";
    
} catch (PDOException $e) {
    echo "<p>❌ <strong>Error updating profile:</strong> " . $e->getMessage() . "</p>";
}

// Test with latest quote again
echo "<h3>🧪 Test with Latest Quote (After Update):</h3>";
$stmt = $pdo->query("
    SELECT qr.*, sc.name_en as service_name
    FROM quote_requests qr
    LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
    ORDER BY qr.created_at DESC
    LIMIT 1
");
$latest_quote = $stmt->fetch();

if ($latest_quote) {
    echo "<p><strong>Latest Quote:</strong> {$latest_quote['title']}</p>";
    echo "<p><strong>Service:</strong> {$latest_quote['service_name']} (ID: {$latest_quote['service_category_id']})</p>";
    echo "<p><strong>District:</strong> {$latest_quote['district']}</p>";
    
    $has_service = in_array((int)$latest_quote['service_category_id'], $current_services) ||
                  in_array((string)$latest_quote['service_category_id'], $current_services);
    $has_area = in_array($latest_quote['district'], $current_areas);
    
    echo "<p><strong>Service Match:</strong> " . ($has_service ? '✅ YES' : '❌ NO') . "</p>";
    echo "<p><strong>Area Match:</strong> " . ($has_area ? '✅ YES' : '❌ NO') . "</p>";
    echo "<p><strong>Should See Quote:</strong> " . ($has_service && $has_area ? '✅ YES' : '❌ NO') . "</p>";
    
    if ($has_service && $has_area) {
        echo "<p>🎉 <strong>SUCCESS!</strong> The contractor should now see this quote!</p>";
        echo "<p>🔄 <strong>Next step:</strong> The contractor should refresh their dashboard to see the quote.</p>";
    } else {
        if (!$has_service) {
            echo "<p>🔧 <strong>Service Issue:</strong> Need to add service ID {$latest_quote['service_category_id']} to contractor profile.</p>";
        }
        if (!$has_area) {
            echo "<p>🗺️ <strong>Area Issue:</strong> Need to add {$latest_quote['district']} to contractor areas.</p>";
        }
    }
} else {
    echo "<p>❌ No quotes found</p>";
}
?>
