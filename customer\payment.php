<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

$quote_response_id = (int)($_GET['quote_response_id'] ?? 0);

if (!$quote_response_id) {
    $_SESSION['error'] = 'Invalid quote response.';
    header('Location: quotes.php');
    exit();
}

// Get quote response details
try {
    $stmt = $pdo->prepare("
        SELECT qres.*, qr.title, qr.description, qr.location, qr.district,
               cp.business_name, cp.contact_person, cp.phone, cp.business_address,
               cust.first_name, cust.last_name
        FROM quote_responses qres
        JOIN quote_requests qr ON qres.quote_request_id = qr.id
        JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
        JOIN customer_profiles cust ON qr.customer_id = cust.user_id
        WHERE qres.id = ? AND qr.customer_id = ?
    ");
    $stmt->execute([$quote_response_id, $_SESSION['user_id']]);
    $quote_response = $stmt->fetch();
    
    if (!$quote_response) {
        $_SESSION['error'] = 'Quote response not found or not accessible.';
        header('Location: quotes.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: quotes.php');
    exit();
}

// Check if down payment already exists
try {
    $stmt = $pdo->prepare("
        SELECT * FROM project_payments 
        WHERE quote_response_id = ? AND payment_type = 'down_payment'
    ");
    $stmt->execute([$quote_response_id]);
    $existing_payment = $stmt->fetch();
    
    if ($existing_payment) {
        if ($existing_payment['payment_status'] === 'completed') {
            $_SESSION['info'] = 'Down payment has already been completed for this project.';
            header('Location: quotes.php');
            exit();
        }
    }
} catch (PDOException $e) {
    // Continue if error checking existing payment
}

// Calculate down payment (typically 20-30% of total amount)
$down_payment_percentage = 25; // 25% down payment
$total_amount = $quote_response['quoted_amount'];
$down_payment_amount = ($total_amount * $down_payment_percentage) / 100;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Payment - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600&family=Inter:wght@700;800&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }

        .payment-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .payment-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            padding: 30px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .project-summary {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .amount-breakdown {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 2px solid #007bff;
        }

        .payment-method {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }

        .payment-method:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.1);
        }

        .payment-method.selected {
            border-color: #007bff;
            background: #f8f9ff;
        }

        .payment-method input[type="radio"] {
            margin-right: 12px;
        }

        .payment-method-icon {
            font-size: 20px;
            margin-right: 12px;
            color: #6c757d;
        }

        .btn-pay {
            background: #007bff;
            border: none;
            padding: 15px 40px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            color: white;
            transition: all 0.2s ease;
        }

        .btn-pay:hover {
            background: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
            color: white;
        }

        .security-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .card-title {
            color: #343a40;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1.25rem;
        }

        .amount-highlight {
            font-size: 1.5rem;
            font-weight: 700;
            color: #007bff;
        }

        .contractor-info {
            background: #fff3cd;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #ffc107;
        }

        .header-clean {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 40px 0;
        }

        .text-primary-custom {
            color: #007bff !important;
        }

        .badge-custom {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
        }
    </style>
</head>

<body>
    <div class="container-xxl bg-white p-0">
        <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <!-- Spinner End -->

        <!-- Navbar Start -->
        <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
            <a href="../index.php" class="navbar-brand ms-4 ms-lg-0">
                <h1 class="text-primary m-0"><img class="me-3" src="../img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
            </a>
            <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarCollapse">
                <div class="navbar-nav ms-auto p-4 p-lg-0">
                    <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                    <a href="contractors.php" class="nav-item nav-link">Find Contractors</a>
                    <a href="quotes.php" class="nav-item nav-link">My Quotes</a>
                    <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                    <a href="profile.php" class="nav-item nav-link">Profile</a>
                    <div class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($quote_response['first_name']); ?>
                        </a>
                        <div class="dropdown-menu rounded-0 m-0">
                            <a href="profile.php" class="dropdown-item">My Profile</a>
                            <a href="../logout.php" class="dropdown-item">Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
        <!-- Navbar End -->

        <!-- Header Start -->
        <div class="container-fluid header-clean">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center">
                        <h1 class="mb-3">Secure Payment</h1>
                        <p class="text-muted mb-4">Complete your down payment to start your construction project</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="badge bg-success me-3">
                                <i class="fas fa-check me-1"></i>Quote Accepted
                            </span>
                            <i class="fas fa-arrow-right text-muted me-3"></i>
                            <span class="badge bg-primary me-3">
                                <i class="fas fa-credit-card me-1"></i>Payment
                            </span>
                            <i class="fas fa-arrow-right text-muted me-3"></i>
                            <span class="badge bg-secondary">
                                <i class="fas fa-hammer me-1"></i>Project Start
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Header End -->

        <!-- Payment Content Start -->
        <div class="container-xxl py-5">
            <div class="payment-container">
                
                <!-- Project Summary -->
                <div class="payment-card">
                    <h3 class="card-title">Project Summary</h3>
                    <div class="project-summary">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="text-primary-custom mb-2"><?php echo htmlspecialchars($quote_response['title']); ?></h5>
                                <p class="text-muted mb-3"><?php echo htmlspecialchars($quote_response['description']); ?></p>
                                <p class="mb-0">
                                    <i class="fas fa-map-marker-alt me-2 text-muted"></i>
                                    <?php echo htmlspecialchars($quote_response['location']); ?>, <?php echo htmlspecialchars($quote_response['district']); ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <div class="contractor-info">
                                    <h6 class="mb-2">Contractor Details</h6>
                                    <p class="mb-1"><strong><?php echo htmlspecialchars($quote_response['business_name']); ?></strong></p>
                                    <p class="mb-1 text-muted"><?php echo htmlspecialchars($quote_response['contact_person']); ?></p>
                                    <p class="mb-1 text-muted">
                                        <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($quote_response['phone']); ?>
                                    </p>
                                    <p class="mb-0 text-muted small"><?php echo htmlspecialchars($quote_response['business_address']); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Amount Breakdown -->
                <div class="payment-card">
                    <h3 class="card-title">Payment Breakdown</h3>
                    <div class="amount-breakdown">
                        <div class="d-flex justify-content-between mb-3">
                            <span>Total Project Amount:</span>
                            <strong>Rs. <?php echo number_format($total_amount, 2); ?></strong>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span>Down Payment (<?php echo $down_payment_percentage; ?>%):</span>
                            <strong class="text-primary-custom">Rs. <?php echo number_format($down_payment_amount, 2); ?></strong>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <span>Remaining Amount:</span>
                            <span class="text-muted">Rs. <?php echo number_format($total_amount - $down_payment_amount, 2); ?></span>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                The remaining amount will be paid in milestones as the project progresses.
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <div class="payment-card">
                    <h3 class="card-title">Payment Method</h3>

                    <form action="process_payment.php" method="POST" id="paymentForm">
                        <input type="hidden" name="quote_response_id" value="<?php echo $quote_response_id; ?>">
                        <input type="hidden" name="amount" value="<?php echo $down_payment_amount; ?>">
                        <input type="hidden" name="payment_type" value="down_payment">

                        <!-- Payment Methods -->
                        <div class="payment-method" onclick="selectPaymentMethod('card')">
                            <input type="radio" name="payment_method" value="card" id="card" checked>
                            <label for="card" class="mb-0 d-flex align-items-center">
                                <div class="payment-method-icon">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold">Credit/Debit Card</div>
                                    <small class="text-muted">Visa, MasterCard, American Express</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge-custom">Instant</span>
                                </div>
                            </label>
                        </div>

                        <div class="payment-method" onclick="selectPaymentMethod('bank')">
                            <input type="radio" name="payment_method" value="bank" id="bank">
                            <label for="bank" class="mb-0 d-flex align-items-center">
                                <div class="payment-method-icon">
                                    <i class="fas fa-university"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold">Bank Transfer</div>
                                    <small class="text-muted">Direct bank transfer</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge-custom">1-2 Hours</span>
                                </div>
                            </label>
                        </div>

                        <div class="payment-method" onclick="selectPaymentMethod('mobile')">
                            <input type="radio" name="payment_method" value="mobile" id="mobile">
                            <label for="mobile" class="mb-0 d-flex align-items-center">
                                <div class="payment-method-icon">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold">Mobile Payment</div>
                                    <small class="text-muted">eZ Cash, mCash, Dialog Pay</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge-custom">Instant</span>
                                </div>
                            </label>
                        </div>
                        
                        <!-- Payment Button -->
                        <div class="text-center mt-4">
                            <div class="mb-3">
                                <span class="text-muted">Amount to Pay: </span>
                                <span class="amount-highlight">Rs. <?php echo number_format($down_payment_amount, 2); ?></span>
                            </div>

                            <button type="submit" class="btn btn-pay">
                                <i class="fas fa-lock me-2"></i>
                                Pay Now
                            </button>

                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Secure payment gateway
                                </small>
                            </div>
                        </div>

                        <!-- Security Info -->
                        <div class="security-info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-shield-alt text-success me-2"></i>
                                <small>
                                    <strong>Secure Payment:</strong> Your payment information is encrypted and secure.
                                    We use industry-standard security measures to protect your data.
                                </small>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- Back Button -->
                <div class="text-center">
                    <a href="quote_responses.php?id=<?php echo $quote_response['quote_request_id']; ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Quote
                    </a>
                </div>
            </div>
        </div>
        <!-- Payment Content End -->



        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
    
    <script>
        function selectPaymentMethod(method) {
            // Remove selected class from all payment methods
            document.querySelectorAll('.payment-method').forEach(el => {
                el.classList.remove('selected');
            });
            
            // Add selected class to clicked method
            event.currentTarget.classList.add('selected');
            
            // Check the radio button
            document.getElementById(method).checked = true;
        }
        
        // Initialize first payment method as selected
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.payment-method').classList.add('selected');
        });
    </script>
</body>
</html>
