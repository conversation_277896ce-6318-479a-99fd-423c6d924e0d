<?php
session_start();
require_once 'config/database.php';

echo "<h2>Testing Quote Responses Fix</h2>";

try {
    // Get test customer
    $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' AND status = 'approved' LIMIT 1");
    $customer_id = $stmt->fetchColumn();
    
    if (!$customer_id) {
        echo "<p style='color: red;'>No test customer found. Please run fix_database_issues.php first.</p>";
        exit;
    }
    
    // Set session for testing
    $_SESSION['user_id'] = $customer_id;
    $_SESSION['user_type'] = 'customer';
    
    // Get test quote request
    $stmt = $pdo->prepare("SELECT id FROM quote_requests WHERE customer_id = ? LIMIT 1");
    $stmt->execute([$customer_id]);
    $quote_id = $stmt->fetchColumn();
    
    if (!$quote_id) {
        echo "<p style='color: red;'>No test quote request found. Please run fix_database_issues.php first.</p>";
        exit;
    }
    
    echo "<h3>Testing Quote Responses Query</h3>";
    echo "<p>Customer ID: $customer_id</p>";
    echo "<p>Quote ID: $quote_id</p>";
    
    // Test the exact query from quote_responses.php
    echo "<h4>1. Checking Reviews Table Structure</h4>";
    
    $reviews_has_payment_id = false;
    try {
        $check_stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'payment_id'");
        $reviews_has_payment_id = $check_stmt->fetch() !== false;
        echo "<p>Reviews table has payment_id: " . ($reviews_has_payment_id ? "YES" : "NO") . "</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Reviews table check error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h4>2. Testing Quote Responses Query</h4>";
    
    if ($reviews_has_payment_id) {
        echo "<p>Using payment_id join...</p>";
        $stmt = $pdo->prepare("
            SELECT qres.*, 
                   COALESCE(cp.business_name, 'Unknown Contractor') as business_name, 
                   COALESCE(cp.contact_person, 'N/A') as contact_person, 
                   COALESCE(cp.phone, 'N/A') as phone, 
                   cp.profile_image,
                   COALESCE(cp.average_rating, 0) as average_rating, 
                   COALESCE(cp.total_reviews, 0) as total_reviews, 
                   COALESCE(cp.cida_grade, 'N/A') as cida_grade,
                   pp.payment_status, pp.amount as paid_amount, pp.payment_date, pp.id as payment_id,
                   r.id as review_id, r.rating as review_rating, r.review_text
            FROM quote_responses qres
            LEFT JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
            LEFT JOIN project_payments pp ON qres.id = pp.quote_response_id AND pp.payment_type = 'down_payment'
            LEFT JOIN reviews r ON pp.id = r.payment_id AND r.customer_id = ?
            WHERE qres.quote_request_id = ?
            ORDER BY qres.created_at DESC
        ");
        $stmt->execute([$customer_id, $quote_id]);
    } else {
        $check_stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'quote_request_id'");
        $reviews_has_quote_request_id = $check_stmt->fetch() !== false;
        echo "<p>Reviews table has quote_request_id: " . ($reviews_has_quote_request_id ? "YES" : "NO") . "</p>";
        
        if ($reviews_has_quote_request_id) {
            echo "<p>Using quote_request_id join...</p>";
            $stmt = $pdo->prepare("
                SELECT qres.*, 
                       COALESCE(cp.business_name, 'Unknown Contractor') as business_name, 
                       COALESCE(cp.contact_person, 'N/A') as contact_person, 
                       COALESCE(cp.phone, 'N/A') as phone, 
                       cp.profile_image,
                       COALESCE(cp.average_rating, 0) as average_rating, 
                       COALESCE(cp.total_reviews, 0) as total_reviews, 
                       COALESCE(cp.cida_grade, 'N/A') as cida_grade,
                       pp.payment_status, pp.amount as paid_amount, pp.payment_date, pp.id as payment_id,
                       r.id as review_id, r.rating as review_rating, r.review_text
                FROM quote_responses qres
                LEFT JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
                LEFT JOIN project_payments pp ON qres.id = pp.quote_response_id AND pp.payment_type = 'down_payment'
                LEFT JOIN reviews r ON qres.quote_request_id = r.quote_request_id AND r.customer_id = ? AND r.contractor_id = qres.contractor_id
                WHERE qres.quote_request_id = ?
                ORDER BY qres.created_at DESC
            ");
            $stmt->execute([$customer_id, $quote_id]);
        } else {
            echo "<p>Skipping reviews join...</p>";
            $stmt = $pdo->prepare("
                SELECT qres.*, 
                       COALESCE(cp.business_name, 'Unknown Contractor') as business_name, 
                       COALESCE(cp.contact_person, 'N/A') as contact_person, 
                       COALESCE(cp.phone, 'N/A') as phone, 
                       cp.profile_image,
                       COALESCE(cp.average_rating, 0) as average_rating, 
                       COALESCE(cp.total_reviews, 0) as total_reviews, 
                       COALESCE(cp.cida_grade, 'N/A') as cida_grade,
                       pp.payment_status, pp.amount as paid_amount, pp.payment_date, pp.id as payment_id,
                       NULL as review_id, NULL as review_rating, NULL as review_text
                FROM quote_responses qres
                LEFT JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
                LEFT JOIN project_payments pp ON qres.id = pp.quote_response_id AND pp.payment_type = 'down_payment'
                WHERE qres.quote_request_id = ?
                ORDER BY qres.created_at DESC
            ");
            $stmt->execute([$quote_id]);
        }
    }
    
    $responses = $stmt->fetchAll();
    
    echo "<h4>3. Query Results</h4>";
    echo "<p style='color: green;'>✓ Query executed successfully!</p>";
    echo "<p>Found " . count($responses) . " quote responses</p>";
    
    if (!empty($responses)) {
        echo "<h4>4. Response Details</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Business Name</th><th>Amount</th><th>Status</th><th>Payment Status</th></tr>";
        
        foreach ($responses as $response) {
            echo "<tr>";
            echo "<td>" . $response['id'] . "</td>";
            echo "<td>" . htmlspecialchars($response['business_name']) . "</td>";
            echo "<td>Rs. " . number_format($response['quoted_amount'], 2) . "</td>";
            echo "<td>" . $response['status'] . "</td>";
            echo "<td>" . ($response['payment_status'] ?? 'No payment') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h4>5. Test the Actual Page</h4>";
        echo "<p><a href='customer/quote_responses.php?id=$quote_id' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Open Quote Responses Page</a></p>";
    } else {
        echo "<p style='color: orange;'>No quote responses found. This might be expected if no contractors have responded yet.</p>";
        echo "<p><a href='fix_database_issues.php' style='background: #ffc107; color: #212529; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Create Test Data</a></p>";
    }
    
    echo "<h4>6. Database Status Summary</h4>";
    echo "<ul>";
    echo "<li>Customer ID: $customer_id</li>";
    echo "<li>Quote Request ID: $quote_id</li>";
    echo "<li>Quote Responses Found: " . count($responses) . "</li>";
    echo "<li>Reviews Table Payment ID: " . ($reviews_has_payment_id ? "Available" : "Missing") . "</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
    echo "<p>SQL State: " . $e->getCode() . "</p>";
}
?>
