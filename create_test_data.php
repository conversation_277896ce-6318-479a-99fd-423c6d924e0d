<?php
require_once 'config/database.php';

echo "<h2>Creating Test Data</h2>";

try {
    // Check if we have users
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'customer'");
    $customer_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'contractor'");
    $contractor_count = $stmt->fetchColumn();
    
    echo "<p>Customers: $customer_count, Contractors: $contractor_count</p>";
    
    if ($customer_count == 0 || $contractor_count == 0) {
        echo "<p style='color: red;'>Need at least 1 customer and 1 contractor to create test data.</p>";
        
        // Create test users if they don't exist
        if ($customer_count == 0) {
            echo "<p>Creating test customer...</p>";
            $pdo->exec("INSERT INTO users (email, password, user_type, status) VALUES ('<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 'customer', 'approved')");
            $customer_id = $pdo->lastInsertId();
            
            // Create customer profile
            $pdo->prepare("INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district, address) VALUES (?, 'Test', 'Customer', '0771234567', 'Colombo', 'Test Address')")->execute([$customer_id]);
        } else {
            $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' LIMIT 1");
            $customer_id = $stmt->fetchColumn();
        }
        
        if ($contractor_count == 0) {
            echo "<p>Creating test contractor...</p>";
            $pdo->exec("INSERT INTO users (email, password, user_type, status) VALUES ('<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 'contractor', 'approved')");
            $contractor_id = $pdo->lastInsertId();
            
            // Create contractor profile
            $pdo->prepare("INSERT INTO contractor_profiles (user_id, business_name, contact_person, phone, district, address, cida_grade, business_registration) VALUES (?, 'Test Construction', 'Test Contractor', '0771234568', 'Colombo', 'Test Address', 'A', 'REG123')")->execute([$contractor_id]);
        } else {
            $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'contractor' LIMIT 1");
            $contractor_id = $stmt->fetchColumn();
        }
    } else {
        $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' LIMIT 1");
        $customer_id = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'contractor' LIMIT 1");
        $contractor_id = $stmt->fetchColumn();
    }
    
    // Check if we have service categories
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM service_categories");
    $service_count = $stmt->fetchColumn();
    
    if ($service_count == 0) {
        echo "<p>Creating service categories...</p>";
        $services = [
            ['House Construction', 'නිවාස ඉදිකිරීම', 'வீட்டு கட்டுமானம்'],
            ['Building Renovation', 'ගොඩනැගිලි අලුත්වැඩියා', 'கட்டிட புனரமைப்பு'],
            ['Electrical Work', 'විදුලි වැඩ', 'மின் வேலை'],
            ['Plumbing', 'ජල නල වැඩ', 'குழாய் வேலை']
        ];
        
        foreach ($services as $service) {
            $pdo->prepare("INSERT INTO service_categories (name_en, name_si, name_ta) VALUES (?, ?, ?)")->execute($service);
        }
    }
    
    // Get first service category
    $stmt = $pdo->query("SELECT id FROM service_categories LIMIT 1");
    $service_id = $stmt->fetchColumn();
    
    // Check if we have quote requests
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM quote_requests");
    $quote_count = $stmt->fetchColumn();
    
    if ($quote_count == 0) {
        echo "<p>Creating test quote request...</p>";
        $pdo->prepare("
            INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, status) 
            VALUES (?, ?, 'Test House Construction', 'Need to build a 3-bedroom house', 'Colombo', 'Colombo', 5000000, '6 months', 'open')
        ")->execute([$customer_id, $service_id]);
        $quote_request_id = $pdo->lastInsertId();
    } else {
        $stmt = $pdo->query("SELECT id FROM quote_requests LIMIT 1");
        $quote_request_id = $stmt->fetchColumn();
    }
    
    // Check if we have quote responses
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM quote_responses WHERE quote_request_id = ?");
    $stmt->execute([$quote_request_id]);
    $response_count = $stmt->fetchColumn();
    
    if ($response_count == 0) {
        echo "<p>Creating test quote response...</p>";
        $pdo->prepare("
            INSERT INTO quote_responses (quote_request_id, contractor_id, quoted_amount, estimated_timeline, description, status) 
            VALUES (?, ?, 4800000, '5-6 months', 'We can complete your house construction with high quality materials and experienced workers.', 'pending')
        ")->execute([$quote_request_id, $contractor_id]);
        echo "<p style='color: green;'>Test quote response created!</p>";
    } else {
        echo "<p>Quote responses already exist: $response_count</p>";
    }
    
    echo "<h3>Current Data Summary:</h3>";
    echo "<p>Quote Request ID: $quote_request_id</p>";
    echo "<p>Customer ID: $customer_id</p>";
    echo "<p>Contractor ID: $contractor_id</p>";
    echo "<p>Service Category ID: $service_id</p>";
    
    echo "<p><a href='customer/quote_responses.php?id=$quote_request_id' target='_blank'>Test Quote Responses Page</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
