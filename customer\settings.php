<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

$success_message = '';
$error_message = '';

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    $errors = [];
    
    if (empty($current_password)) {
        $errors[] = 'Current password is required.';
    }
    
    if (empty($new_password)) {
        $errors[] = 'New password is required.';
    } elseif (strlen($new_password) < 6) {
        $errors[] = 'New password must be at least 6 characters long.';
    }
    
    if ($new_password !== $confirm_password) {
        $errors[] = 'New passwords do not match.';
    }
    
    if (empty($errors)) {
        try {
            // Verify current password
            $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch();
            
            if (!password_verify($current_password, $user['password'])) {
                $error_message = 'Current password is incorrect.';
            } else {
                // Update password
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$hashed_password, $_SESSION['user_id']]);
                
                $success_message = 'Password updated successfully!';
            }
        } catch (PDOException $e) {
            $error_message = 'Database error. Please try again.';
        }
    } else {
        $error_message = implode('<br>', $errors);
    }
}

// Handle notification settings
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_notifications'])) {
    $quote_notifications = isset($_POST['quote_notifications']) ? 1 : 0;
    $message_notifications = isset($_POST['message_notifications']) ? 1 : 0;
    $update_notifications = isset($_POST['update_notifications_check']) ? 1 : 0;
    $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
    $sms_notifications = isset($_POST['sms_notifications']) ? 1 : 0;
    
    try {
        // Check if notification preferences exist
        $stmt = $pdo->prepare("SELECT id FROM customer_notification_preferences WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $exists = $stmt->fetch();
        
        if ($exists) {
            // Update existing preferences
            $stmt = $pdo->prepare("
                UPDATE customer_notification_preferences 
                SET quote_notifications = ?, message_notifications = ?, update_notifications = ?, 
                    email_notifications = ?, sms_notifications = ?, updated_at = NOW()
                WHERE user_id = ?
            ");
            $stmt->execute([
                $quote_notifications, $message_notifications, $update_notifications,
                $email_notifications, $sms_notifications, $_SESSION['user_id']
            ]);
        } else {
            // Insert new preferences
            $stmt = $pdo->prepare("
                INSERT INTO customer_notification_preferences 
                (user_id, quote_notifications, message_notifications, update_notifications, 
                 email_notifications, sms_notifications, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([
                $_SESSION['user_id'], $quote_notifications, $message_notifications, 
                $update_notifications, $email_notifications, $sms_notifications
            ]);
        }
        
        $success_message = 'Notification preferences updated successfully!';
    } catch (PDOException $e) {
        $error_message = 'Error updating notification preferences. Please try again.';
    }
}

// Get current notification preferences
$notification_prefs = [
    'quote_notifications' => 1,
    'message_notifications' => 1,
    'update_notifications' => 1,
    'email_notifications' => 1,
    'sms_notifications' => 0
];

try {
    $stmt = $pdo->prepare("SELECT * FROM customer_notification_preferences WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $prefs = $stmt->fetch();
    
    if ($prefs) {
        $notification_prefs = [
            'quote_notifications' => $prefs['quote_notifications'],
            'message_notifications' => $prefs['message_notifications'],
            'update_notifications' => $prefs['update_notifications'],
            'email_notifications' => $prefs['email_notifications'],
            'sms_notifications' => $prefs['sms_notifications']
        ];
    }
} catch (PDOException $e) {
    // Use default preferences if error
}

// Get customer info
try {
    $stmt = $pdo->prepare("
        SELECT u.email, u.created_at, cp.first_name, cp.last_name 
        FROM users u 
        LEFT JOIN customer_profiles cp ON u.id = cp.user_id 
        WHERE u.id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $customer = $stmt->fetch();
} catch (PDOException $e) {
    $customer = ['first_name' => 'User', 'last_name' => '', 'email' => ''];
}

// Get unread notifications count
$unread_count = 0;
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$_SESSION['user_id']]);
    $unread_count = $stmt->fetchColumn();
} catch (PDOException $e) {
    // Ignore error, keep count as 0
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Settings - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
    <link href="../lib/lightbox/css/lightbox.min.css" rel="stylesheet">
    
    <!-- Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        .settings-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .settings-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
            border: none;
        }
        
        .settings-header {
            text-align: center;
            margin-bottom: 2rem;
            color: white;
        }
        
        .section-title {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-update {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-update:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .notification-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        
        .required {
            color: #dc3545;
        }

        .form-control.is-invalid {
            border-color: #dc3545;
        }

        .form-control.is-valid {
            border-color: #28a745;
        }

        .password-strength {
            margin-top: 0.25rem;
            font-size: 0.875rem;
        }

        .btn-update:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .notification-item:hover {
            background: #e9ecef;
            transition: background-color 0.3s ease;
        }
    </style>
</head>

<body>
    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
        <a href="../index.php" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="../img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="contractors.php" class="nav-item nav-link">Find Contractors</a>
                <a href="quotes.php" class="nav-item nav-link">My Quotes</a>
                <a href="cost_estimator.php" class="nav-item nav-link">Cost Estimator</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
            </div>
            <div class="dropdown">
                <a href="#" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($customer['first_name'] ?: 'User'); ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="notifications.php"><i class="fas fa-bell me-2"></i>Notifications <?php if ($unread_count > 0): ?><span class="badge bg-danger"><?php echo $unread_count; ?></span><?php endif; ?></a></li>
                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Settings Container Start -->
    <div class="settings-container">
        <div class="container">
            <div class="settings-header">
                <h1><i class="fas fa-cog me-3"></i>Account Settings</h1>
                <p class="mb-0">Manage your account security and notification preferences</p>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Account Security -->
                <div class="col-lg-6">
                    <div class="settings-card">
                        <h3 class="section-title">
                            <i class="fas fa-shield-alt text-primary me-2"></i>Account Security
                        </h3>
                        
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">Current Password <span class="required">*</span></label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="new_password" class="form-label">New Password <span class="required">*</span></label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <div class="form-text">Password must be at least 6 characters long.</div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="confirm_password" class="form-label">Confirm New Password <span class="required">*</span></label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                            
                            <button type="submit" name="change_password" class="btn btn-update">
                                <i class="fas fa-key me-2"></i>Update Password
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Notification Settings -->
                <div class="col-lg-6">
                    <div class="settings-card">
                        <h3 class="section-title">
                            <i class="fas fa-bell text-warning me-2"></i>Notification Preferences
                        </h3>
                        
                        <form method="POST" action="">
                            <div class="notification-item">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="quote_notifications" name="quote_notifications" 
                                           <?php echo $notification_prefs['quote_notifications'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="quote_notifications">
                                        <strong>Quote Notifications</strong><br>
                                        <small class="text-muted">Receive notifications when contractors respond to your quote requests</small>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="notification-item">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="message_notifications" name="message_notifications"
                                           <?php echo $notification_prefs['message_notifications'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="message_notifications">
                                        <strong>Message Notifications</strong><br>
                                        <small class="text-muted">Get notified when you receive new messages from contractors</small>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="notification-item">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="update_notifications_check" name="update_notifications_check"
                                           <?php echo $notification_prefs['update_notifications'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="update_notifications_check">
                                        <strong>Platform Updates</strong><br>
                                        <small class="text-muted">Stay informed about new features and platform updates</small>
                                    </label>
                                </div>
                            </div>
                            
                            <hr class="my-4">
                            
                            <h5 class="mb-3">Delivery Methods</h5>
                            
                            <div class="notification-item">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications"
                                           <?php echo $notification_prefs['email_notifications'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="email_notifications">
                                        <strong>Email Notifications</strong><br>
                                        <small class="text-muted">Receive notifications via email at <?php echo htmlspecialchars($customer['email']); ?></small>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="notification-item">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="sms_notifications" name="sms_notifications"
                                           <?php echo $notification_prefs['sms_notifications'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="sms_notifications">
                                        <strong>SMS Notifications</strong><br>
                                        <small class="text-muted">Receive important notifications via SMS (charges may apply)</small>
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" name="update_notifications" class="btn btn-update mt-3">
                                <i class="fas fa-bell me-2"></i>Update Preferences
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Back to Profile -->
            <div class="text-center mt-4">
                <a href="profile.php" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>Back to Profile
                </a>
            </div>
        </div>
    </div>
    <!-- Settings Container End -->

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>
    <script src="../lib/lightbox/js/lightbox.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>

    <!-- Settings Page JavaScript -->
    <script>
        // Password validation
        document.addEventListener('DOMContentLoaded', function() {
            const passwordForm = document.querySelector('form[method="POST"]');
            const newPassword = document.getElementById('new_password');
            const confirmPassword = document.getElementById('confirm_password');

            if (passwordForm && newPassword && confirmPassword) {
                // Real-time password confirmation validation
                confirmPassword.addEventListener('input', function() {
                    if (newPassword.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity('Passwords do not match');
                        confirmPassword.classList.add('is-invalid');
                    } else {
                        confirmPassword.setCustomValidity('');
                        confirmPassword.classList.remove('is-invalid');
                    }
                });

                // Password strength indicator
                newPassword.addEventListener('input', function() {
                    const password = newPassword.value;
                    const strengthIndicator = document.getElementById('password-strength');

                    if (!strengthIndicator) {
                        const indicator = document.createElement('div');
                        indicator.id = 'password-strength';
                        indicator.className = 'form-text';
                        newPassword.parentNode.appendChild(indicator);
                    }

                    const strength = getPasswordStrength(password);
                    const indicator = document.getElementById('password-strength');

                    if (password.length === 0) {
                        indicator.textContent = '';
                        return;
                    }

                    switch (strength) {
                        case 'weak':
                            indicator.textContent = 'Password strength: Weak';
                            indicator.className = 'form-text text-danger';
                            break;
                        case 'medium':
                            indicator.textContent = 'Password strength: Medium';
                            indicator.className = 'form-text text-warning';
                            break;
                        case 'strong':
                            indicator.textContent = 'Password strength: Strong';
                            indicator.className = 'form-text text-success';
                            break;
                    }
                });
            }

            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 500);
                }, 5000);
            });
        });

        function getPasswordStrength(password) {
            let score = 0;

            if (password.length >= 8) score++;
            if (password.match(/[a-z]/)) score++;
            if (password.match(/[A-Z]/)) score++;
            if (password.match(/[0-9]/)) score++;
            if (password.match(/[^a-zA-Z0-9]/)) score++;

            if (score < 3) return 'weak';
            if (score < 5) return 'medium';
            return 'strong';
        }
    </script>
</body>
</html>
