<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>e-Shift System Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background-color: white;
            color: black;
        }
        
        .architecture-container {
            width: 1000px;
            margin: 0 auto;
            position: relative;
        }
        
        .layer {
            margin-bottom: 100px;
            position: relative;
            border: 2px solid #333;
            border-radius: 10px;
            padding: 30px;
            background-color: white;
        }
        
        .layer-title {
            font-weight: bold;
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            background-color: white;
            padding: 0 10px;
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .components-row {
            display: flex;
            justify-content: space-around;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .component {
            border: 2px solid #333;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            background-color: white;
            width: 160px;
            height: 90px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }
        
        .component-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .component-title {
            font-weight: bold;
            font-size: 12px;
            line-height: 1.3;
            margin-bottom: 4px;
        }
        
        .component-subtitle {
            font-size: 10px;
            color: #666;
            font-style: italic;
        }
        
        .database-component {
            border-radius: 50px;
            width: 300px;
            height: 120px;
            margin: 0 auto;
        }
        
        .arrow {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: -50px;
            z-index: 10;
        }
        
        .arrow-line {
            width: 2px;
            height: 40px;
            background-color: #333;
            margin: 0 auto;
        }
        
        .arrow-head {
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 12px solid #333;
            margin: 0 auto;
        }
        
        .business-logic-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }
        
        .module {
            border: 1px solid #666;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            background-color: #f9f9f9;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .module-title {
            font-weight: bold;
            font-size: 11px;
            margin-bottom: 4px;
        }
        
        .module-desc {
            font-size: 9px;
            color: #555;
            line-height: 1.2;
        }
        
        .database-tables {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .table-group {
            border: 1px solid #999;
            border-radius: 6px;
            padding: 10px;
            background-color: #f5f5f5;
            width: 140px;
            text-align: center;
        }
        
        .table-title {
            font-weight: bold;
            font-size: 10px;
            margin-bottom: 5px;
        }
        
        .table-items {
            font-size: 8px;
            color: #666;
            line-height: 1.3;
        }
    </style>
</head>
<body>
    <div class="architecture-container">
        <h1 style="text-align: center; margin-bottom: 40px;">e-Shift System Architecture</h1>
        
        <!-- Presentation Layer -->
        <div class="layer">
            <div class="layer-title">Presentation Layer (Client Layer)</div>
            
            <div class="components-row">
                <div class="component">
                    <div class="component-icon">🖥️</div>
                    <div class="component-title">Admin Interface</div>
                    <div class="component-subtitle">Windows Forms GUI</div>
                </div>
                
                <div class="component">
                    <div class="component-icon">👤</div>
                    <div class="component-title">Customer Interface</div>
                    <div class="component-subtitle">User Dashboard</div>
                </div>
                
                <div class="component">
                    <div class="component-icon">📊</div>
                    <div class="component-title">Reports Interface</div>
                    <div class="component-subtitle">Data Visualization</div>
                </div>
                
                <div class="component">
                    <div class="component-icon">💳</div>
                    <div class="component-title">Payment Interface</div>
                    <div class="component-subtitle">Financial Management</div>
                </div>
            </div>
            
            <div class="arrow">
                <div class="arrow-line"></div>
                <div class="arrow-head"></div>
            </div>
        </div>
        
        <!-- Business Logic Layer -->
        <div class="layer">
            <div class="layer-title">Business Logic Layer</div>
            
            <div class="business-logic-grid">
                <div class="module">
                    <div class="module-title">Customer Module</div>
                    <div class="module-desc">Registration, profile updates, customer lookups</div>
                </div>
                
                <div class="module">
                    <div class="module-title">Job Management Module</div>
                    <div class="module-desc">Job creation, location assignment, status tracking</div>
                </div>
                
                <div class="module">
                    <div class="module-title">Load Management Module</div>
                    <div class="module-desc">Load creation, tracking, transport unit association</div>
                </div>
                
                <div class="module">
                    <div class="module-title">Transport Unit Module</div>
                    <div class="module-desc">Lorries, drivers, assistants, containers management</div>
                </div>
                
                <div class="module">
                    <div class="module-title">Authentication Module</div>
                    <div class="module-desc">Login validation, user roles, access control</div>
                </div>
                
                <div class="module">
                    <div class="module-title">Payment Management Module</div>
                    <div class="module-desc">Payment recording, history tracking, financial transparency</div>
                </div>
            </div>
            
            <div class="arrow">
                <div class="arrow-line"></div>
                <div class="arrow-head"></div>
            </div>
        </div>
        
        <!-- Database Layer -->
        <div class="layer">
            <div class="layer-title">Database Layer</div>
            
            <div class="components-row">
                <div class="component database-component">
                    <div class="component-icon">🗄️</div>
                    <div class="component-title">Microsoft SQL Server Database</div>
                    <div class="component-subtitle">Persistent Data Storage with Referential Integrity</div>
                </div>
            </div>
            
            <div class="database-tables">
                <div class="table-group">
                    <div class="table-title">Core Tables</div>
                    <div class="table-items">• Customers<br/>• Jobs<br/>• Loads<br/>• Login Credentials</div>
                </div>
                
                <div class="table-group">
                    <div class="table-title">Transport Tables</div>
                    <div class="table-items">• Lorries<br/>• Drivers<br/>• Assistants<br/>• Containers</div>
                </div>
                
                <div class="table-group">
                    <div class="table-title">Payment Tables</div>
                    <div class="table-items">• Payment Records<br/>• Payment Types<br/>• Job References<br/>• Financial History</div>
                </div>
                
                <div class="table-group">
                    <div class="table-title">System Tables</div>
                    <div class="table-items">• Reports<br/>• User Roles<br/>• System Logs<br/>• Configuration</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
