<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

// Handle contractor approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['approve_contractor'])) {
        $contractor_id = (int)$_POST['contractor_id'];
        try {
            $stmt = $pdo->prepare("UPDATE users SET status = 'approved' WHERE id = ? AND user_type = 'contractor'");
            $result = $stmt->execute([$contractor_id]);
            $affected_rows = $stmt->rowCount();

            if ($affected_rows > 0) {
                $_SESSION['success'] = 'Contractor approved successfully!';
            } else {
                $_SESSION['error'] = 'No contractor found with ID: ' . $contractor_id;
            }
        } catch (PDOException $e) {
            $_SESSION['error'] = 'Error approving contractor: ' . $e->getMessage();
        }
    } elseif (isset($_POST['reject_contractor'])) {
        $contractor_id = (int)$_POST['contractor_id'];
        try {
            $stmt = $pdo->prepare("UPDATE users SET status = 'rejected' WHERE id = ? AND user_type = 'contractor'");
            $result = $stmt->execute([$contractor_id]);
            $affected_rows = $stmt->rowCount();

            if ($affected_rows > 0) {
                $_SESSION['success'] = 'Contractor rejected successfully!';
            } else {
                $_SESSION['error'] = 'No contractor found with ID: ' . $contractor_id;
            }
        } catch (PDOException $e) {
            $_SESSION['error'] = 'Error rejecting contractor: ' . $e->getMessage();
        }
    } elseif (isset($_POST['suspend_contractor'])) {
        $contractor_id = (int)$_POST['contractor_id'];
        try {
            $stmt = $pdo->prepare("UPDATE users SET status = 'suspended' WHERE id = ? AND user_type = 'contractor'");
            $result = $stmt->execute([$contractor_id]);
            $affected_rows = $stmt->rowCount();

            if ($affected_rows > 0) {
                $_SESSION['success'] = 'Contractor suspended successfully!';
            } else {
                $_SESSION['error'] = 'No contractor found with ID: ' . $contractor_id;
            }
        } catch (PDOException $e) {
            $_SESSION['error'] = 'Error suspending contractor: ' . $e->getMessage();
        }
    } elseif (isset($_POST['unsuspend_contractor'])) {
        $contractor_id = (int)$_POST['contractor_id'];
        try {
            $stmt = $pdo->prepare("UPDATE users SET status = 'approved' WHERE id = ? AND user_type = 'contractor'");
            $result = $stmt->execute([$contractor_id]);
            $affected_rows = $stmt->rowCount();

            if ($affected_rows > 0) {
                $_SESSION['success'] = 'Contractor unsuspended successfully!';
            } else {
                $_SESSION['error'] = 'No contractor found with ID: ' . $contractor_id;
            }
        } catch (PDOException $e) {
            $_SESSION['error'] = 'Error unsuspending contractor: ' . $e->getMessage();
        }
    }
    header('Location: contractors.php');
    exit();
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : 'all';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Build query
$where_conditions = ["u.user_type = 'contractor'"];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "u.status = ?";
    $params[] = $status_filter;
}

if (!empty($search)) {
    $where_conditions[] = "(cp.business_name LIKE ? OR cp.contact_person LIKE ? OR u.email LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

$where_clause = implode(' AND ', $where_conditions);

// Get contractors
try {
    // Check which review field structure we're using
    $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'status'");
    $has_status = $stmt->fetch();

    if ($has_status) {
        $review_condition = "(r.status IS NULL OR r.status != 'deleted')";
    } else {
        $review_condition = "(r.is_approved IS NULL OR r.is_approved != 0)";
    }

    $stmt = $pdo->prepare("
        SELECT u.id as user_id, u.email, u.status, u.created_at, u.user_type,
               cp.id as profile_id, cp.business_name, cp.contact_person, cp.phone, cp.business_address,
               cp.website, cp.cida_registration, cp.cida_grade, cp.business_description,
               cp.service_areas, cp.service_types,
               COALESCE((SELECT COUNT(*) FROM reviews r WHERE r.contractor_id = u.id AND $review_condition), 0) as review_count,
               COALESCE((SELECT AVG(rating) FROM reviews r WHERE r.contractor_id = u.id AND $review_condition), 0) as average_rating,
               COALESCE((SELECT COUNT(*) FROM quote_responses qr WHERE qr.contractor_id = u.id), 0) as quote_count
        FROM users u
        LEFT JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE $where_clause
        ORDER BY u.created_at DESC
    ");
    $stmt->execute($params);
    $contractors = $stmt->fetchAll();
} catch (PDOException $e) {
    $contractors = [];
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
}

// Get status counts
try {
    $stmt = $pdo->query("
        SELECT 
            status,
            COUNT(*) as count
        FROM users 
        WHERE user_type = 'contractor'
        GROUP BY status
    ");
    $status_counts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
} catch (PDOException $e) {
    $status_counts = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Contractors Management - Admin Panel</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF8C00;
            --accent-yellow: #FFD700;
            --light-gray: #F8F9FA;
            --medium-gray: #6C757D;
            --success-green: #28A745;
            --warning-orange: #FFC107;
            --info-blue: #17A2B8;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f5f6fa;
            color: var(--primary-dark);
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-header h3 {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }
        
        .sidebar-header p {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 15px rgba(197, 23, 46, 0.3);
        }
        
        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #2563eb, #0891b2);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .page-title {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 2rem;
        }

        .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            font-size: 1.1rem;
        }
        
        .filters-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .status-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .status-tab {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .status-tab.all {
            background: linear-gradient(135deg, var(--medium-gray), #5a6268);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .status-tab.pending {
            background: linear-gradient(135deg, var(--warning-orange), #e0a800);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
        }

        .status-tab.approved {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
        }

        .status-tab.rejected {
            background: linear-gradient(135deg, var(--primary-red), #bd2130);
            color: white;
            box-shadow: 0 4px 15px rgba(197, 23, 46, 0.4);
        }

        .status-tab.suspended {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }
        
        .status-tab.active {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
        }

        .status-tab:hover {
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        
        .contractor-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .contractor-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .contractor-header {
            display: flex;
            justify-content: between;
            align-items: start;
            margin-bottom: 1.5rem;
        }
        
        .contractor-info h5 {
            color: var(--primary-dark);
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .contractor-info p {
            color: var(--medium-gray);
            margin: 0;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .status-badge.pending {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
        }
        
        .status-badge.approved {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }
        
        .status-badge.rejected {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
        
        .status-badge.suspended {
            background: linear-gradient(135deg, #e2e3e5, #d6d8db);
            color: #383d41;
        }
        
        .contractor-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            color: var(--medium-gray);
        }
        
        .detail-item i {
            width: 20px;
            margin-right: 0.5rem;
            color: var(--accent-orange);
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .btn-action {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-approve {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            color: white;
        }
        
        .btn-reject {
            background: linear-gradient(135deg, var(--primary-red), #bd2130);
            color: white;
        }
        
        .btn-suspend {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
        }
        
        .btn-view {
            background: linear-gradient(135deg, var(--info-blue), #138496);
            color: white;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .rating-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 4px;
        }

        .rating-stars {
            display: inline-flex;
            align-items: center;
            gap: 1px;
        }

        .rating-stars i {
            color: #ffc107;
            font-size: 0.9rem;
            line-height: 1;
        }

        .rating-stars i.fa-star-o {
            color: #e9ecef;
        }

        .rating-text {
            font-size: 0.875rem;
            color: #6c757d;
            font-weight: 500;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .empty-state i {
            font-size: 4rem;
            color: var(--medium-gray);
            margin-bottom: 1rem;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-shield-alt me-2"></i>Admin Panel</h3>
            <p>Brick & Click Management</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="contractors.php" class="nav-link active">
                    <i class="fas fa-hard-hat"></i>Contractors
                </a>
            </div>
            <div class="nav-item">
                <a href="customers.php" class="nav-link">
                    <i class="fas fa-users"></i>Customers
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews
                </a>
            </div>
            <div class="nav-item">
                <a href="payments.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payments
                </a>
            </div>
            <div class="nav-item">
                <a href="reports.php" class="nav-link">
                    <i class="fas fa-chart-bar"></i>Reports
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Contractors Management</h1>
            <p class="page-subtitle">Manage contractor registrations, approvals, and profiles</p>
        </div>

        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Filters -->
        <div class="filters-card">
            <div class="status-tabs">
                <a href="contractors.php" class="status-tab all <?php echo $status_filter === 'all' ? 'active' : ''; ?>">
                    All Contractors (<?php echo !empty($status_counts) ? array_sum($status_counts) : 0; ?>)
                </a>
                <a href="contractors.php?status=pending" class="status-tab pending <?php echo $status_filter === 'pending' ? 'active' : ''; ?>">
                    Pending (<?php echo $status_counts['pending'] ?? 0; ?>)
                </a>
                <a href="contractors.php?status=approved" class="status-tab approved <?php echo $status_filter === 'approved' ? 'active' : ''; ?>">
                    Approved (<?php echo $status_counts['approved'] ?? 0; ?>)
                </a>
                <a href="contractors.php?status=rejected" class="status-tab rejected <?php echo $status_filter === 'rejected' ? 'active' : ''; ?>">
                    Rejected (<?php echo $status_counts['rejected'] ?? 0; ?>)
                </a>
                <a href="contractors.php?status=suspended" class="status-tab suspended <?php echo $status_filter === 'suspended' ? 'active' : ''; ?>">
                    Suspended (<?php echo $status_counts['suspended'] ?? 0; ?>)
                </a>
            </div>

            <form method="GET" class="row g-3">
                <input type="hidden" name="status" value="<?php echo htmlspecialchars($status_filter); ?>">
                <div class="col-md-8">
                    <input type="text" class="form-control" name="search" placeholder="Search by business name, contact person, or email..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                </div>
            </form>
        </div>

        <!-- Contractors List -->
        <?php if (empty($contractors)): ?>
            <div class="empty-state">
                <i class="fas fa-hard-hat"></i>
                <h3>No contractors found</h3>
                <p class="text-muted">No contractors match your current filters.</p>
            </div>
        <?php else: ?>
            <?php foreach ($contractors as $contractor): ?>
                <div class="contractor-card">
                    <div class="contractor-header">
                        <div class="contractor-info">
                            <h5><?php echo htmlspecialchars($contractor['business_name'] ?? 'N/A'); ?></h5>
                            <p><?php echo htmlspecialchars($contractor['contact_person'] ?? 'N/A'); ?></p>
                            <small class="text-muted"><?php echo htmlspecialchars($contractor['email']); ?></small>
                        </div>
                        <span class="status-badge <?php echo $contractor['status']; ?>">
                            <?php echo ucfirst($contractor['status']); ?>
                        </span>
                    </div>

                    <div class="contractor-details">
                        <div class="detail-item">
                            <i class="fas fa-phone"></i>
                            <span><?php echo htmlspecialchars($contractor['phone'] ?? 'N/A'); ?></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-certificate"></i>
                            <span>CIDA Grade: <?php echo htmlspecialchars($contractor['cida_grade'] ?? 'N/A'); ?></span>
                        </div>
                        
                        <div class="detail-item">
                            <i class="fas fa-file-invoice"></i>
                            <span><?php echo $contractor['quote_count']; ?> quotes sent</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-calendar"></i>
                            <span>Joined <?php echo date('M j, Y', strtotime($contractor['created_at'])); ?></span>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <?php if ($contractor['status'] === 'pending'): ?>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="contractor_id" value="<?php echo $contractor['user_id']; ?>">
                                <button type="submit" name="approve_contractor" class="btn-action btn-approve" onclick="return confirm('Are you sure you want to approve this contractor?')">
                                    <i class="fas fa-check me-1"></i>Approve
                                </button>
                            </form>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="contractor_id" value="<?php echo $contractor['user_id']; ?>">
                                <button type="submit" name="reject_contractor" class="btn-action btn-reject" onclick="return confirm('Are you sure you want to reject this contractor?')">
                                    <i class="fas fa-times me-1"></i>Reject
                                </button>
                            </form>
                        <?php elseif ($contractor['status'] === 'approved'): ?>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="contractor_id" value="<?php echo $contractor['user_id']; ?>">
                                <button type="submit" name="suspend_contractor" class="btn-action btn-suspend" onclick="return confirm('Are you sure you want to suspend this contractor?')">
                                    <i class="fas fa-ban me-1"></i>Suspend
                                </button>
                            </form>
                        <?php elseif ($contractor['status'] === 'suspended'): ?>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="contractor_id" value="<?php echo $contractor['user_id']; ?>">
                                <button type="submit" name="unsuspend_contractor" class="btn-action btn-approve" onclick="return confirm('Are you sure you want to unsuspend this contractor?')">
                                    <i class="fas fa-check me-1"></i>Unsuspend
                                </button>
                            </form>
                        <?php endif; ?>

                        <button type="button" class="btn-action btn-view" onclick="viewContractorDetails(<?php echo $contractor['user_id']; ?>)">
                            <i class="fas fa-eye me-1"></i>View Details
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function viewContractorDetails(contractorId) {
            // This would open a modal or redirect to a detailed view
            window.open('contractor_details.php?id=' + contractorId, '_blank');
        }
    </script>
</body>
</html>
