<?php
session_start();
require_once 'config/database.php';

echo "<h2>🎯 Direct Dashboard Test</h2>";

// Set up contractor session
$stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND user_type = 'contractor'");
$stmt->execute(['<EMAIL>']);
$contractor_user = $stmt->fetch();

if (!$contractor_user) {
    echo "<p>❌ Contractor not found!</p>";
    exit;
}

// Set session
$_SESSION['user_id'] = $contractor_user['id'];
$_SESSION['user_type'] = 'contractor';
$_SESSION['email'] = $contractor_user['email'];

echo "<p>✅ Session set for contractor ID: {$contractor_user['id']}</p>";

// Test the exact same logic as dashboard.php
$contractor_id = $_SESSION['user_id'];

// Get contractor profile
$stmt = $pdo->prepare("SELECT * FROM contractor_profiles WHERE user_id = ?");
$stmt->execute([$contractor_id]);
$contractor = $stmt->fetch();

if (!$contractor) {
    echo "<p>❌ No contractor profile found!</p>";
    exit;
}

echo "<p>✅ Contractor profile found: {$contractor['business_name']}</p>";

// Test pending quotes count (exact same query as dashboard)
$stmt = $pdo->prepare("
    SELECT qr.*
    FROM quote_requests qr
    WHERE qr.status = 'open'
    AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
    AND qr.id NOT IN (
        SELECT COALESCE(quote_request_id, 0)
        FROM quote_responses
        WHERE contractor_id = ?
    )
");
$stmt->execute([$contractor_id, $contractor_id]);
$all_pending_quotes = $stmt->fetchAll();

echo "<p><strong>Raw pending quotes from DB:</strong> " . count($all_pending_quotes) . "</p>";

// Apply filtering logic (exact same as dashboard)
$pending_quotes = [];
$contractor_services = json_decode($contractor['service_types'], true) ?: [];
$contractor_areas = json_decode($contractor['service_areas'], true) ?: [];

foreach ($all_pending_quotes as $quote) {
    if ($quote['specific_contractor_id'] == $contractor_id) {
        $pending_quotes[] = $quote;
    } elseif ($quote['specific_contractor_id'] === null) {
        $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                      in_array((string)$quote['service_category_id'], $contractor_services);
        $has_area = in_array($quote['district'], $contractor_areas);

        if ($has_service && $has_area) {
            $pending_quotes[] = $quote;
        }
    }
}

echo "<p><strong>Filtered pending quotes:</strong> " . count($pending_quotes) . "</p>";

// Test recent quotes (exact same query as dashboard)
$stmt = $pdo->prepare("
    SELECT qr.*, cp.first_name, cp.last_name, cp.district,
           (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
    FROM quote_requests qr
    JOIN customer_profiles cp ON qr.customer_id = cp.user_id
    WHERE qr.status = 'open'
    AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
    ORDER BY qr.created_at DESC
    LIMIT 10
");
$stmt->execute([$contractor_id, $contractor_id]);
$all_recent_quotes = $stmt->fetchAll();

echo "<p><strong>Raw recent quotes from DB:</strong> " . count($all_recent_quotes) . "</p>";

// Apply filtering logic (exact same as dashboard)
$recent_quotes = [];
foreach ($all_recent_quotes as $quote) {
    if ($quote['specific_contractor_id'] == $contractor_id) {
        $recent_quotes[] = $quote;
        continue;
    }

    if ($quote['specific_contractor_id'] === null) {
        $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                      in_array((string)$quote['service_category_id'], $contractor_services);
        $has_area = in_array($quote['district'], $contractor_areas);

        if ($has_service && $has_area) {
            $recent_quotes[] = $quote;
        }
    }

    if (count($recent_quotes) >= 5) {
        break;
    }
}

echo "<p><strong>Filtered recent quotes:</strong> " . count($recent_quotes) . "</p>";

// Display the results exactly as dashboard would
echo "<h3>📊 Dashboard Variables</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>";
echo "<p><strong>\$pending_quotes count:</strong> " . count($pending_quotes) . "</p>";
echo "<p><strong>\$recent_quotes count:</strong> " . count($recent_quotes) . "</p>";
echo "<p><strong>empty(\$recent_quotes):</strong> " . (empty($recent_quotes) ? 'true' : 'false') . "</p>";
echo "</div>";

if (!empty($recent_quotes)) {
    echo "<h4>Recent Quotes Details:</h4>";
    foreach ($recent_quotes as $quote) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
        echo "<p><strong>ID:</strong> {$quote['id']}</p>";
        echo "<p><strong>Title:</strong> " . htmlspecialchars($quote['title']) . "</p>";
        echo "<p><strong>Customer:</strong> {$quote['first_name']} {$quote['last_name']}</p>";
        echo "<p><strong>District:</strong> {$quote['district']}</p>";
        echo "<p><strong>Service ID:</strong> {$quote['service_category_id']}</p>";
        echo "</div>";
    }
}

// Test what the dashboard should display
echo "<h3>🎯 Dashboard Display Test</h3>";

if (empty($recent_quotes)) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb;'>";
    echo "<p><strong>Dashboard will show:</strong> 'No recent quote requests' message</p>";
    echo "<p>This means the empty state will be displayed.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb;'>";
    echo "<p><strong>Dashboard will show:</strong> " . count($recent_quotes) . " recent quotes</p>";
    echo "<p>The quotes should be displayed in the dashboard.</p>";
    echo "</div>";
}

echo "<h3>🔗 Test Dashboard Access</h3>";
echo "<p><strong>Current Session:</strong></p>";
echo "<ul>";
echo "<li>User ID: " . ($_SESSION['user_id'] ?? 'not set') . "</li>";
echo "<li>User Type: " . ($_SESSION['user_type'] ?? 'not set') . "</li>";
echo "<li>Email: " . ($_SESSION['email'] ?? 'not set') . "</li>";
echo "</ul>";

echo "<p><a href='contractor/dashboard.php' target='_blank' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>🚀 OPEN CONTRACTOR DASHBOARD</a></p>";

echo "<p><strong>If the dashboard still shows 'No recent quote requests', there might be a session issue or the contractor needs to log in properly through the login page.</strong></p>";

echo "<h3>🔐 Alternative: Use Login Page</h3>";
echo "<p><a href='contractor/login.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Login as Contractor</a></p>";
echo "<p><strong>Login credentials:</strong></p>";
echo "<p>Email: <EMAIL></p>";
echo "<p>Password: password123</p>";
?>
