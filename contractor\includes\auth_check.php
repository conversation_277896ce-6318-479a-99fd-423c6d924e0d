<?php
// Contractor authentication and authorization check
// Include this file at the top of all contractor pages

if (!isset($_SESSION)) {
    session_start();
}

require_once '../config/database.php';

// Check if user is logged in and is a contractor
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'contractor') {
    header('Location: ../login.php');
    exit();
}

// Get contractor profile and check status
try {
    $stmt = $pdo->prepare("
        SELECT cp.*, u.email, u.status 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE cp.user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        // Redirect to profile setup if profile doesn't exist
        header('Location: setup_profile.php');
        exit();
    }
    
    // Check if contractor is approved
    if ($contractor['status'] !== 'approved') {
        $status_messages = [
            'pending' => 'Your account is pending approval. Please wait for admin verification.',
            'rejected' => 'Your account has been rejected. Please contact support for more information.',
            'suspended' => 'Your account has been suspended. Please contact support.'
        ];
        
        $_SESSION['error'] = $status_messages[$contractor['status']] ?? 'Your account is not active.';
        header('Location: ../login.php');
        exit();
    }
    
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: ../login.php');
    exit();
}

// Make contractor data available to the including page
$GLOBALS['contractor'] = $contractor;
?>
