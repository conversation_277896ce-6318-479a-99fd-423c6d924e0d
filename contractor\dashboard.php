<?php
// Include authentication and authorization check
require_once 'includes/auth_check.php';

// Contractor data is now available in $contractor variable

// Get dashboard statistics
$total_quotes = $pending_quotes = $total_reviews = $total_projects = 0;
$recent_quotes = $recent_reviews = [];

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        throw new Exception("User not logged in");
    }

    $contractor_id = $_SESSION['user_id'];

    // Ensure specific_contractor_id column exists
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
        $column_exists = $stmt->fetch();

        if (!$column_exists) {
            $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        }
    } catch (PDOException $e) {
        // Column might already exist, continue
    }

    // Total quote requests - count quotes the contractor has responded to
    $stmt = $pdo->prepare("
        SELECT COUNT(DISTINCT quote_request_id) as total_quotes
        FROM quote_responses
        WHERE contractor_id = ?
    ");
    $stmt->execute([$contractor_id]);
    $total_quotes = $stmt->fetchColumn() ?: 0;

    // Get all pending quotes for this contractor
    $stmt = $pdo->prepare("
        SELECT qr.*
        FROM quote_requests qr
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.id NOT IN (
            SELECT COALESCE(quote_request_id, 0)
            FROM quote_responses
            WHERE contractor_id = ?
        )
    ");
    $stmt->execute([$contractor_id, $contractor_id]);
    $all_pending_quotes = $stmt->fetchAll();

    // Filter quotes in PHP to handle JSON logic properly
    $pending_quotes = 0;
    foreach ($all_pending_quotes as $quote) {
        // Always count direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $pending_quotes++;
            continue;
        }

        // For general quotes (no specific contractor), check service and area match
        if ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($contractor['service_types'], true) ?: [];
            $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];

            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);

            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);

            if ($has_service && $has_area) {
                $pending_quotes++;
            }
        }
    }

    // Total reviews - count approved reviews
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total_reviews
            FROM reviews
            WHERE contractor_id = ? AND status = 'approved'
        ");
        $stmt->execute([$contractor_id]);
        $total_reviews = $stmt->fetchColumn() ?: 0;
    } catch (PDOException $e) {
        // If reviews table doesn't exist, set to 0
        $total_reviews = 0;
    }

    // Total portfolio projects
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total_projects
        FROM contractor_portfolios
        WHERE contractor_id = ?
    ");
    $stmt->execute([$contractor_id]);
    $total_projects = $stmt->fetchColumn() ?: 0;

    // Recent quote requests - show open quotes for this contractor only
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, u.email, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               CASE WHEN qr.specific_contractor_id = ? THEN 'direct' ELSE 'general' END as quote_type
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        JOIN users u ON qr.customer_id = u.id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.id NOT IN (
            SELECT COALESCE(quote_request_id, 0)
            FROM quote_responses
            WHERE contractor_id = ?
        )
        ORDER BY qr.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id, $contractor_id]);
    $all_recent_quotes = $stmt->fetchAll();

    // Filter quotes in PHP to handle JSON logic properly
    $recent_quotes = [];
    foreach ($all_recent_quotes as $quote) {
        // Always show direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $recent_quotes[] = $quote;
            continue;
        }

        // For general quotes (no specific contractor), check service and area match
        if ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($contractor['service_types'], true) ?: [];
            $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];

            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);

            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);

            if ($has_service && $has_area) {
                $recent_quotes[] = $quote;
            }
        }

        // Limit to 5 quotes for dashboard
        if (count($recent_quotes) >= 5) {
            break;
        }
    }

    // Recent reviews - get approved reviews with customer information
    try {
        $stmt = $pdo->prepare("
            SELECT r.*, cp.first_name, cp.last_name
            FROM reviews r
            JOIN customer_profiles cp ON r.customer_id = cp.user_id
            WHERE r.contractor_id = ? AND r.status = 'approved'
            ORDER BY r.created_at DESC
            LIMIT 3
        ");
        $stmt->execute([$contractor_id]);
        $recent_reviews = $stmt->fetchAll();
    } catch (PDOException $e) {
        // If reviews table doesn't exist, set to empty array
        $recent_reviews = [];
    }

} catch (Exception $e) {
    // Log error for debugging
    error_log("Contractor Dashboard Error: " . $e->getMessage());
    $total_quotes = $pending_quotes = $total_reviews = $total_projects = 0;
    $recent_quotes = $recent_reviews = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Contractor Dashboard - BrickClick</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet"> 
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF6B35;
            --accent-yellow: #F7931E;
            --success-green: #28a745;
            --info-blue: #17a2b8;
            --warning-orange: #ffc107;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --dark-gray: #343a40;
        }

        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e9ecef 100%);
            font-family: 'Open Sans', sans-serif;
            line-height: 1.6;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            z-index: 1000;
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            flex-shrink: 0;
        }

        .sidebar-header h3 {
            color: var(--accent-orange);
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }

        .sidebar-header p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }

        .sidebar-nav {
            flex: 1;
            padding: 0.5rem 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .nav-item {
            margin: 0.15rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,107,53,0.1);
            color: var(--accent-orange);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--accent-orange), var(--primary-red));
            color: white;
            box-shadow: 0 3px 10px rgba(255,107,53,0.3);
        }

        .nav-link i {
            margin-right: 0.8rem;
            width: 18px;
            text-align: center;
            font-size: 1rem;
        }

        .main-content {
            margin-left: 280px;
            padding: 2.5rem;
            min-height: 100vh;
        }

        .top-bar {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            margin-bottom: 2.5rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .welcome-text h2 {
            color: var(--primary-dark);
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            font-size: 1.8rem;
        }

        .welcome-text p {
            color: var(--medium-gray);
            margin: 0;
            font-size: 1.05rem;
            font-weight: 500;
        }

        .status-badge {
            padding: 0.7rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .status-badge.approved {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-badge.pending {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-badge.rejected {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-orange), var(--primary-red));
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.6rem;
            color: white;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .stat-icon.quotes {
            background: linear-gradient(135deg, var(--info-blue), #138496);
        }

        .stat-icon.pending {
            background: linear-gradient(135deg, var(--warning-orange), #e0a800);
        }

        .stat-icon.reviews {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
        }

        .stat-icon.projects {
            background: linear-gradient(135deg, var(--accent-orange), #e55a2b);
        }

        .stat-number {
            font-size: 2.8rem;
            font-weight: 700;
            color: var(--primary-dark);
            margin: 0 0 0.5rem 0;
            line-height: 1;
        }

        .stat-label {
            color: var(--medium-gray);
            font-weight: 600;
            margin: 0;
            font-size: 1rem;
        }

        .content-section {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            margin-bottom: 2.5rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .section-title {
            color: var(--primary-dark);
            font-weight: 700;
            margin-bottom: 2rem;
            font-size: 1.4rem;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-orange), var(--primary-red));
            border-radius: 2px;
        }

        .quote-item {
            padding: 2rem;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .quote-item:hover {
            border-color: var(--accent-orange);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
            background: white;
        }

        .quote-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1.5rem;
        }

        .quote-title {
            color: var(--primary-dark);
            font-weight: 600;
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
            line-height: 1.4;
        }

        .quote-customer {
            color: var(--medium-gray);
            font-size: 0.95rem;
            margin: 0;
            line-height: 1.5;
        }

        .quote-budget {
            color: var(--accent-orange);
            font-weight: 600;
            font-size: 1.05rem;
        }

        .btn-respond {
            background: linear-gradient(135deg, var(--accent-orange), #e55a2b);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 0.95rem;
            text-decoration: none;
        }

        .btn-respond:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            color: white;
        }

        .review-item {
            padding: 2rem;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            margin-bottom: 1.5rem;
            background: #fafbfc;
            transition: all 0.3s ease;
        }

        .review-item:hover {
            background: white;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }

        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .review-customer {
            color: var(--primary-dark);
            font-weight: 600;
            margin: 0;
            font-size: 1.05rem;
        }

        .rating-stars {
            color: var(--accent-yellow);
            font-size: 1.1rem;
        }

        .review-text {
            color: var(--medium-gray);
            margin: 0 0 1rem 0;
            line-height: 1.7;
            font-size: 0.95rem;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--medium-gray);
        }

        .empty-state i {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            opacity: 0.4;
            color: var(--accent-orange);
        }

        .empty-state h4 {
            color: var(--primary-dark);
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .empty-state p {
            font-size: 0.95rem;
            line-height: 1.6;
            max-width: 400px;
            margin: 0 auto;
        }

        .btn-outline-primary {
            padding: 0.8rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-outline-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>BrickClick</h3>
            <p>Contractor Portal</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice-dollar"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="portfolio.php" class="nav-link">
                    <i class="fas fa-images"></i>Portfolio
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews & Ratings
                </a>
            </div>
            <div class="nav-item">
                <a href="profile.php" class="nav-link">
                    <i class="fas fa-user-edit"></i>Profile & Services
                </a>
            </div>
            <div class="nav-item">
                <a href="payment_history.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payment History
                </a>
            </div>
            <div class="nav-item">
                <a href="notifications.php" class="nav-link">
                    <i class="fas fa-bell"></i>Notifications
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="welcome-text">
                <h2>Welcome back, <?php echo htmlspecialchars($contractor['contact_person']); ?>!</h2>
                <p><?php echo htmlspecialchars($contractor['business_name']); ?></p>
            </div>
            <div>
                <span class="status-badge <?php echo $contractor['status']; ?>">
                    <?php echo ucfirst($contractor['status']); ?>
                </span>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon quotes">
                    <i class="fas fa-file-invoice-dollar"></i>
                </div>
                <h3 class="stat-number"><?php echo $total_quotes ?: 0; ?></h3>
                <p class="stat-label">Total Quotes</p>
            </div>

            <div class="stat-card">
                <div class="stat-icon pending">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="stat-number"><?php echo $pending_quotes ?: 0; ?></h3>
                <p class="stat-label">Pending Quotes</p>
            </div>

            <div class="stat-card">
                <div class="stat-icon reviews">
                    <i class="fas fa-star"></i>
                </div>
                <h3 class="stat-number"><?php echo $total_reviews ?: 0; ?></h3>
                <p class="stat-label">Total Reviews</p>
            </div>

            <div class="stat-card">
                <div class="stat-icon projects">
                    <i class="fas fa-images"></i>
                </div>
                <h3 class="stat-number"><?php echo $total_projects ?: 0; ?></h3>
                <p class="stat-label">Portfolio Projects</p>
            </div>
        </div>

        <!-- Recent Quote Requests -->
        <div class="content-section">
            <h3 class="section-title">Recent Quote Requests</h3>

            <?php if (empty($recent_quotes)): ?>
                <div class="empty-state">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <h4>No recent quote requests</h4>
                    <p>New quote requests will appear here when customers request quotes for your services.</p>
                </div>
            <?php else: ?>
                <?php foreach ($recent_quotes as $quote): ?>
                    <div class="quote-item">
                        <div class="quote-header">
                            <div class="flex-grow-1">
                                <h5 class="quote-title">
                                    <?php echo htmlspecialchars($quote['service_category'] ?? $quote['title'] ?? 'Quote Request'); ?>
                                    <?php if ($quote['quote_type'] === 'direct'): ?>
                                        <span class="badge bg-primary ms-2">
                                            <i class="fas fa-star me-1"></i>Direct Request
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-info ms-2">
                                            <i class="fas fa-broadcast-tower me-1"></i>General Request
                                        </span>
                                    <?php endif; ?>
                                </h5>
                                <div class="quote-customer mt-2">
                                    <div class="d-flex align-items-center mb-1">
                                        <i class="fas fa-user me-2"></i>
                                        <span><?php echo htmlspecialchars($quote['first_name'] . ' ' . $quote['last_name']); ?></span>
                                    </div>
                                    <div class="d-flex align-items-center mb-1">
                                        <i class="fas fa-map-marker-alt me-2"></i>
                                        <span><?php echo htmlspecialchars($quote['district']); ?></span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-envelope me-2"></i>
                                        <span><?php echo htmlspecialchars($quote['email']); ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-end ms-3">
                                <div class="quote-budget mb-2">
                                    Budget: Rs. <?php echo number_format($quote['estimated_budget']); ?>
                                </div>
                                <small class="text-muted">
                                    <?php echo date('M j, Y', strtotime($quote['created_at'])); ?>
                                </small>
                            </div>
                        </div>

                        <div class="quote-description mb-4">
                            <p class="text-muted mb-0" style="line-height: 1.7;">
                                <?php echo htmlspecialchars(substr($quote['description'], 0, 150)) . (strlen($quote['description']) > 150 ? '...' : ''); ?>
                            </p>
                        </div>

                        <div class="quote-actions">
                            <?php if ($quote['has_responded'] == 0): ?>
                                <a href="respond_quote.php?id=<?php echo $quote['id']; ?>" class="btn btn-respond">
                                    <i class="fas fa-reply me-2"></i>Respond to Quote
                                </a>
                            <?php else: ?>
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>Responded
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>

                <div class="text-center mt-4">
                    <a href="quotes.php" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-2"></i>View All Quote Requests
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Recent Reviews -->
        <div class="content-section">
            <h3 class="section-title">Recent Reviews</h3>

            <?php if (empty($recent_reviews)): ?>
                <div class="empty-state">
                    <i class="fas fa-star"></i>
                    <h4>No reviews yet</h4>
                    <p>Customer reviews will appear here after you complete projects.</p>
                </div>
            <?php else: ?>
                <?php foreach ($recent_reviews as $review): ?>
                    <div class="review-item">
                        <div class="review-header">
                            <h6 class="review-customer">
                                <?php echo htmlspecialchars($review['first_name'] . ' ' . $review['last_name']); ?>
                            </h6>
                            <div class="rating-stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star<?php echo $i <= $review['rating'] ? '' : '-o'; ?>"></i>
                                <?php endfor; ?>
                            </div>
                        </div>

                        <?php
                        // Handle different possible column names for review text
                        $review_text = '';
                        if (isset($review['review_text'])) {
                            $review_text = $review['review_text'];
                        } elseif (isset($review['comment'])) {
                            $review_text = $review['comment'];
                        } elseif (isset($review['text'])) {
                            $review_text = $review['text'];
                        }
                        ?>
                        <?php if ($review_text): ?>
                            <div class="review-content mb-3">
                                <p class="review-text"><?php echo htmlspecialchars($review_text); ?></p>
                            </div>
                        <?php endif; ?>

                        <div class="review-meta">
                            <small class="text-muted">
                                <i class="fas fa-calendar-alt me-1"></i>
                                <?php echo date('M j, Y', strtotime($review['created_at'])); ?>
                            </small>
                        </div>
                    </div>
                <?php endforeach; ?>

                <div class="text-center mt-4">
                    <a href="reviews.php" class="btn btn-outline-primary">
                        <i class="fas fa-star me-2"></i>View All Reviews
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
</body>
</html>
