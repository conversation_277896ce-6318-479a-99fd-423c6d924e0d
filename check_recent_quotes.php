<?php
session_start();
require_once 'config/database.php';

echo "<h2>📋 Check Recent Quote Requests</h2>";

// Get all recent quote requests
$stmt = $pdo->query("
    SELECT qr.*, sc.name_en as service_name, cp.first_name, cp.last_name
    FROM quote_requests qr
    LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
    LEFT JOIN customer_profiles cp ON qr.customer_id = cp.user_id
    ORDER BY qr.created_at DESC
    LIMIT 10
");
$quotes = $stmt->fetchAll();

echo "<h3>📋 Recent Quote Requests:</h3>";

if (count($quotes) == 0) {
    echo "<p>❌ No quote requests found!</p>";
    exit;
}

foreach ($quotes as $quote) {
    echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 15px 0;'>";
    echo "<h4>Quote ID: {$quote['id']}</h4>";
    echo "<p><strong>Title:</strong> " . htmlspecialchars($quote['title']) . "</p>";
    echo "<p><strong>Customer:</strong> {$quote['first_name']} {$quote['last_name']}</p>";
    echo "<p><strong>Service:</strong> {$quote['service_name']} (ID: {$quote['service_category_id']})</p>";
    echo "<p><strong>District:</strong> {$quote['district']}</p>";
    echo "<p><strong>Status:</strong> {$quote['status']}</p>";
    echo "<p><strong>Specific Contractor:</strong> " . ($quote['specific_contractor_id'] ?: 'NULL (General Quote)') . "</p>";
    echo "<p><strong>Created:</strong> {$quote['created_at']}</p>";
    echo "</div>";
}

// Check if there's a plumbing quote for Kalutara
echo "<h3>🔧 Looking for Plumbing Quote in Kalutara:</h3>";

$stmt = $pdo->prepare("
    SELECT qr.*, sc.name_en as service_name
    FROM quote_requests qr
    LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
    WHERE qr.district = 'Kalutara'
    AND (sc.name_en LIKE '%plumb%' OR sc.name_en LIKE '%sanit%' OR qr.title LIKE '%plumb%')
    ORDER BY qr.created_at DESC
    LIMIT 5
");
$stmt->execute();
$plumbing_quotes = $stmt->fetchAll();

if (count($plumbing_quotes) > 0) {
    echo "<p>✅ Found plumbing quotes in Kalutara:</p>";
    foreach ($plumbing_quotes as $quote) {
        echo "<p>- ID {$quote['id']}: {$quote['title']} - {$quote['service_name']} - Status: {$quote['status']}</p>";
    }
} else {
    echo "<p>❌ No plumbing quotes found in Kalutara</p>";
    
    // Check for any quotes in Kalutara
    $stmt = $pdo->prepare("
        SELECT qr.*, sc.name_en as service_name
        FROM quote_requests qr
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE qr.district = 'Kalutara'
        ORDER BY qr.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $kalutara_quotes = $stmt->fetchAll();
    
    if (count($kalutara_quotes) > 0) {
        echo "<p>📍 Found other quotes in Kalutara:</p>";
        foreach ($kalutara_quotes as $quote) {
            echo "<p>- ID {$quote['id']}: {$quote['title']} - {$quote['service_name']} - Status: {$quote['status']}</p>";
        }
    } else {
        echo "<p>❌ No quotes found in Kalutara at all</p>";
    }
}

// Check what <NAME_EMAIL> should see
echo "<h3>👷 What <EMAIL> Should See:</h3>";

$stmt = $pdo->prepare("
    SELECT u.id, cp.service_types, cp.service_areas
    FROM users u
    JOIN contractor_profiles cp ON u.id = cp.user_id
    WHERE u.email = ?
");
$stmt->execute(['<EMAIL>']);
$contractor = $stmt->fetch();

if ($contractor) {
    $services = json_decode($contractor['service_types'], true) ?: [];
    $areas = json_decode($contractor['service_areas'], true) ?: [];
    
    echo "<p><strong>Contractor Services:</strong> [" . implode(', ', $services) . "]</p>";
    echo "<p><strong>Contractor Areas:</strong> [" . implode(', ', $areas) . "]</p>";
    
    $matching_quotes = [];
    foreach ($quotes as $quote) {
        if ($quote['status'] !== 'open') continue;
        
        if ($quote['specific_contractor_id'] == $contractor['id']) {
            $matching_quotes[] = $quote;
            echo "<p>✅ <strong>Direct Quote:</strong> {$quote['title']} (ID: {$quote['id']})</p>";
        } elseif ($quote['specific_contractor_id'] === null) {
            $has_service = in_array((int)$quote['service_category_id'], $services) ||
                          in_array((string)$quote['service_category_id'], $services);
            $has_area = in_array($quote['district'], $areas);
            
            if ($has_service && $has_area) {
                $matching_quotes[] = $quote;
                echo "<p>✅ <strong>General Match:</strong> {$quote['title']} (ID: {$quote['id']}) - Service: {$quote['service_name']}, District: {$quote['district']}</p>";
            } else {
                echo "<p>❌ <strong>No Match:</strong> {$quote['title']} (ID: {$quote['id']}) - Service: " . ($has_service ? 'YES' : 'NO') . ", Area: " . ($has_area ? 'YES' : 'NO') . "</p>";
            }
        }
    }
    
    echo "<p><strong>Total quotes contractor should see:</strong> " . count($matching_quotes) . "</p>";
    
    if (count($matching_quotes) == 0) {
        echo "<p>❌ <strong>This explains why the dashboard is empty!</strong></p>";
        echo "<p>🔧 <strong>Solutions:</strong></p>";
        echo "<ul>";
        echo "<li>1. Make sure the contractor has the right service types in their profile</li>";
        echo "<li>2. Make sure the contractor serves the right areas</li>";
        echo "<li>3. Check if there are any open quote requests at all</li>";
        echo "</ul>";
    }
} else {
    echo "<p>❌ Contractor not found!</p>";
}

// Check if there are any responses that might be hiding quotes
echo "<h3>📝 Check Quote Responses:</h3>";
if ($contractor) {
    $stmt = $pdo->prepare("
        SELECT qr.quote_request_id, qreq.title, qr.status, qr.created_at
        FROM quote_responses qr
        JOIN quote_requests qreq ON qr.quote_request_id = qreq.id
        WHERE qr.contractor_id = ?
        ORDER BY qr.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$contractor['id']]);
    $responses = $stmt->fetchAll();
    
    if (count($responses) > 0) {
        echo "<p>📝 Recent responses by this contractor:</p>";
        foreach ($responses as $response) {
            echo "<p>- Quote {$response['quote_request_id']}: {$response['title']} - Status: {$response['status']}</p>";
        }
    } else {
        echo "<p>📝 No responses found - contractor hasn't responded to any quotes yet</p>";
    }
}
?>
