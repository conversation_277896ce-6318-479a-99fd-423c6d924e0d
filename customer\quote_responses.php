<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

$quote_id = (int)($_GET['id'] ?? 0);

if (!$quote_id) {
    $_SESSION['error'] = 'Invalid quote request.';
    header('Location: quotes.php');
    exit();
}

// Get quote request details
try {
    $stmt = $pdo->prepare("
        SELECT qr.*, sc.name_en as service_name
        FROM quote_requests qr 
        JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE qr.id = ? AND qr.customer_id = ?
    ");
    $stmt->execute([$quote_id, $_SESSION['user_id']]);
    $quote = $stmt->fetch();
    
    if (!$quote) {
        $_SESSION['error'] = 'Quote request not found.';
        header('Location: quotes.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    error_log("Quote request fetch error: " . $e->getMessage());
    header('Location: quotes.php');
    exit();
}

// Get quote responses
try {
    // First, check if there are any responses at all
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM quote_responses WHERE quote_request_id = ?");
    $stmt->execute([$quote_id]);
    $response_count = $stmt->fetch()['count'];

    // First check if reviews table has payment_id column
    $reviews_has_payment_id = false;
    try {
        $check_stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'payment_id'");
        $reviews_has_payment_id = $check_stmt->fetch() !== false;
    } catch (PDOException $e) {
        // Reviews table might not exist, continue without it
    }

    if ($reviews_has_payment_id) {
        // Use the payment_id join if the column exists
        $stmt = $pdo->prepare("
            SELECT qres.*,
                   COALESCE(cp.business_name, 'Unknown Contractor') as business_name,
                   COALESCE(cp.contact_person, 'N/A') as contact_person,
                   COALESCE(cp.phone, 'N/A') as phone,
                   cp.profile_image,
                   COALESCE(cp.average_rating, 0) as average_rating,
                   COALESCE(cp.total_reviews, 0) as total_reviews,
                   COALESCE(cp.cida_grade, 'N/A') as cida_grade,
                   pp.payment_status, pp.amount as paid_amount, pp.payment_date, pp.id as payment_id,
                   r.id as review_id, r.rating as review_rating, r.review_text
            FROM quote_responses qres
            LEFT JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
            LEFT JOIN project_payments pp ON qres.id = pp.quote_response_id AND pp.payment_type = 'down_payment'
            LEFT JOIN reviews r ON pp.id = r.payment_id AND r.customer_id = ?
            WHERE qres.quote_request_id = ?
            ORDER BY qres.created_at DESC
        ");
    } else {
        // Use quote_request_id join or skip reviews entirely
        $check_stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'quote_request_id'");
        $reviews_has_quote_request_id = $check_stmt->fetch() !== false;

        if ($reviews_has_quote_request_id) {
            $stmt = $pdo->prepare("
                SELECT qres.*,
                       COALESCE(cp.business_name, 'Unknown Contractor') as business_name,
                       COALESCE(cp.contact_person, 'N/A') as contact_person,
                       COALESCE(cp.phone, 'N/A') as phone,
                       cp.profile_image,
                       COALESCE(cp.average_rating, 0) as average_rating,
                       COALESCE(cp.total_reviews, 0) as total_reviews,
                       COALESCE(cp.cida_grade, 'N/A') as cida_grade,
                       pp.payment_status, pp.amount as paid_amount, pp.payment_date, pp.id as payment_id,
                       r.id as review_id, r.rating as review_rating, r.review_text
                FROM quote_responses qres
                LEFT JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
                LEFT JOIN project_payments pp ON qres.id = pp.quote_response_id AND pp.payment_type = 'down_payment'
                LEFT JOIN reviews r ON qres.quote_request_id = r.quote_request_id AND r.customer_id = ? AND r.contractor_id = qres.contractor_id
                WHERE qres.quote_request_id = ?
                ORDER BY qres.created_at DESC
            ");
        } else {
            // Skip reviews join entirely
            $stmt = $pdo->prepare("
                SELECT qres.*,
                       COALESCE(cp.business_name, 'Unknown Contractor') as business_name,
                       COALESCE(cp.contact_person, 'N/A') as contact_person,
                       COALESCE(cp.phone, 'N/A') as phone,
                       cp.profile_image,
                       COALESCE(cp.average_rating, 0) as average_rating,
                       COALESCE(cp.total_reviews, 0) as total_reviews,
                       COALESCE(cp.cida_grade, 'N/A') as cida_grade,
                       pp.payment_status, pp.amount as paid_amount, pp.payment_date, pp.id as payment_id,
                       NULL as review_id, NULL as review_rating, NULL as review_text
                FROM quote_responses qres
                LEFT JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
                LEFT JOIN project_payments pp ON qres.id = pp.quote_response_id AND pp.payment_type = 'down_payment'
                WHERE qres.quote_request_id = ?
                ORDER BY qres.created_at DESC
            ");
        }
    }

    // Execute with appropriate parameters based on query type
    if ($reviews_has_payment_id || (isset($reviews_has_quote_request_id) && $reviews_has_quote_request_id)) {
        $stmt->execute([$_SESSION['user_id'], $quote_id]);
    } else {
        $stmt->execute([$quote_id]);
    }
    $responses = $stmt->fetchAll();
} catch (PDOException $e) {
    $responses = [];
    $response_count = 0;
    // Log error for debugging
    error_log("Quote responses error: " . $e->getMessage());
    // Store error for display
    $quote_responses_error = $e->getMessage();
}

// Handle response actions (accept/reject)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $response_id = (int)$_POST['response_id'];
    $action = $_POST['action'];
    
    if (in_array($action, ['accepted', 'rejected'])) {
        try {
            $stmt = $pdo->prepare("
                UPDATE quote_responses 
                SET status = ?, updated_at = NOW() 
                WHERE id = ? AND quote_request_id = ?
            ");
            $stmt->execute([$action, $response_id, $quote_id]);
            
            // If accepted, reject all other responses for this quote
            if ($action === 'accepted') {
                $stmt = $pdo->prepare("
                    UPDATE quote_responses
                    SET status = 'rejected', updated_at = NOW()
                    WHERE quote_request_id = ? AND id != ?
                ");
                $stmt->execute([$quote_id, $response_id]);

                // Close the quote request
                $stmt = $pdo->prepare("UPDATE quote_requests SET status = 'closed' WHERE id = ?");
                $stmt->execute([$quote_id]);

                // Redirect to payment page for down payment
                $_SESSION['success'] = 'Quote accepted successfully! Please proceed with the down payment.';
                header("Location: payment.php?quote_response_id=$response_id");
                exit();
            }
            
            $_SESSION['success'] = 'Response ' . $action . ' successfully!';
            header('Location: quote_responses.php?id=' . $quote_id);
            exit();
            
        } catch (PDOException $e) {
            $_SESSION['error'] = 'Failed to update response.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Quote Responses - <?php echo htmlspecialchars($quote['title']); ?> - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, quote responses" name="keywords">
    <meta content="Quote Responses - Brick & Click" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF6B35;
            --accent-yellow: #F7931E;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --success-green: #28a745;
            --warning-yellow: #ffc107;
            --danger-red: #dc3545;
        }

        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-family: 'Open Sans', sans-serif;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 4rem 0 2rem;
            margin-bottom: 3rem;
        }

        .quote-details-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            border: 1px solid #e9ecef;
        }

        .response-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .response-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-orange), var(--accent-yellow));
        }

        .response-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.12);
        }

        .contractor-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .contractor-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--accent-orange), var(--accent-yellow));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            overflow: hidden;
        }

        .contractor-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .quote-amount {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-red);
            margin-bottom: 0.5rem;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-pending {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-accepted {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-rejected {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .btn-accept {
            background: linear-gradient(135deg, var(--success-green), #20c997);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-accept:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .btn-reject {
            background: linear-gradient(135deg, var(--danger-red), #e74c3c);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-reject:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(220, 53, 69, 0.3);
            color: white;
        }

        .rating-stars {
            color: #ffc107;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--medium-gray);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>

<body>
    <!-- Page Header Start -->
    <div class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h1 class="display-6 mb-3">Quote Responses</h1>
                    <p class="lead mb-0">Review and manage contractor responses for your project</p>
                </div>
            </div>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Main Content Start -->
    <div class="container">
        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Quote Details -->
        <div class="quote-details-card">
            <div class="row">
                <div class="col-md-8">
                    <h3 class="mb-3"><?php echo htmlspecialchars($quote['title']); ?></h3>
                    <p class="text-muted mb-2">
                        <i class="fas fa-tag me-2"></i><?php echo htmlspecialchars($quote['service_name']); ?>
                    </p>
                    <p class="text-muted mb-2">
                        <i class="fas fa-map-marker-alt me-2"></i><?php echo htmlspecialchars($quote['location'] . ', ' . $quote['district']); ?>
                    </p>
                    <p class="text-muted mb-3">
                        <i class="fas fa-calendar me-2"></i>Timeline: <?php echo htmlspecialchars($quote['project_timeline']); ?>
                    </p>
                    <p><?php echo nl2br(htmlspecialchars($quote['description'])); ?></p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="mb-2">
                        <span class="status-badge status-<?php echo $quote['status']; ?>">
                            <?php echo ucfirst($quote['status']); ?>
                        </span>
                    </div>
                    <?php if ($quote['estimated_budget']): ?>
                        <p class="text-muted mb-0">
                            <strong>Budget:</strong> Rs. <?php echo number_format($quote['estimated_budget'], 2); ?>
                        </p>
                    <?php endif; ?>
                    <small class="text-muted">
                        Posted: <?php echo date('M j, Y', strtotime($quote['created_at'])); ?>
                    </small>
                </div>
            </div>
        </div>

        <!-- Responses -->
        <h4 class="mb-4">Contractor Responses (<?php echo count($responses); ?>)</h4>


        <?php if (isset($quote_responses_error)): ?>
        <div class="alert alert-danger">
            <strong>Database Error:</strong> <?php echo htmlspecialchars($quote_responses_error); ?>
        </div>
        <?php endif; ?>
        
        <?php if (empty($responses)): ?>
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <h5>No responses yet</h5>
                <p>Contractors will respond to your quote request soon. You'll be notified when responses are received.</p>
                <a href="quotes.php" class="btn btn-primary mt-3">
                    <i class="fas fa-arrow-left me-2"></i>Back to My Quotes
                </a>
            </div>
        <?php else: ?>
            <?php foreach ($responses as $response): ?>
                <div class="response-card">
                    <div class="contractor-info">
                        <div class="contractor-avatar">
                            <i class="fas fa-hard-hat"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1"><?php echo htmlspecialchars($response['business_name']); ?></h5>
                            <p class="text-muted mb-1"><?php echo htmlspecialchars($response['contact_person']); ?></p>
                            <div class="d-flex align-items-center gap-3">
                                <div class="rating-stars">
                                    <?php
                                    $rating = $response['average_rating'];
                                    for ($i = 1; $i <= 5; $i++) {
                                        if ($i <= $rating) {
                                            echo '<i class="fas fa-star"></i>';
                                        } elseif ($i - 0.5 <= $rating) {
                                            echo '<i class="fas fa-star-half-alt"></i>';
                                        } else {
                                            echo '<i class="far fa-star"></i>';
                                        }
                                    }
                                    ?>
                                    <span class="ms-1"><?php echo number_format($rating, 1); ?></span>
                                </div>
                                <small class="text-muted">
                                    CIDA <?php echo htmlspecialchars($response['cida_grade']); ?>
                                </small>
                                <small class="text-muted">
                                    <?php echo $response['total_reviews']; ?> reviews
                                </small>
                            </div>
                        </div>
                        <div class="text-end">
                            <div class="quote-amount">Rs. <?php echo number_format($response['quoted_amount'], 2); ?></div>
                            <span class="status-badge status-<?php echo $response['status']; ?>">
                                <?php echo ucfirst($response['status']); ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Timeline</h6>
                            <p class="text-muted"><?php echo htmlspecialchars($response['estimated_timeline']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Contact</h6>
                            <p class="text-muted"><?php echo htmlspecialchars($response['phone']); ?></p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Quote Description</h6>
                        <p><?php echo nl2br(htmlspecialchars($response['description'])); ?></p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Terms & Conditions</h6>
                        <p><?php echo nl2br(htmlspecialchars($response['terms_conditions'])); ?></p>
                    </div>
                    
                    <?php if ($response['status'] === 'pending' && $quote['status'] === 'open'): ?>
                        <div class="d-flex gap-2 mt-3">
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="response_id" value="<?php echo $response['id']; ?>">
                                <input type="hidden" name="action" value="accepted">
                                <button type="submit" class="btn btn-accept" onclick="return confirm('Accept this quote? This will reject all other responses.')">
                                    <i class="fas fa-check me-2"></i>Accept Quote
                                </button>
                            </form>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="response_id" value="<?php echo $response['id']; ?>">
                                <input type="hidden" name="action" value="rejected">
                                <button type="submit" class="btn btn-reject" onclick="return confirm('Reject this quote?')">
                                    <i class="fas fa-times me-2"></i>Reject
                                </button>
                            </form>
                        </div>
                    <?php elseif ($response['status'] === 'accepted'): ?>
                        <div class="mt-3">
                            <?php if ($response['payment_status'] === 'completed'): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <strong>Payment Completed!</strong><br>
                                    Down payment of Rs. <?php echo number_format($response['paid_amount'], 2); ?> paid on
                                    <?php echo date('M j, Y', strtotime($response['payment_date'])); ?>
                                </div>

                                <!-- Rating/Review Section -->
                                <?php if ($response['payment_status'] === 'completed'): ?>
                                    <?php if ($response['review_id']): ?>
                                        <div class="alert alert-info">
                                            <i class="fas fa-star me-2"></i>
                                            <strong>Review Submitted!</strong><br>
                                            You rated this contractor <?php echo $response['review_rating']; ?>/5 stars
                                            <?php if ($response['review_text']): ?>
                                                <br><em>"<?php echo htmlspecialchars(substr($response['review_text'], 0, 100)); ?>..."</em>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="mt-3">
                                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#reviewModal<?php echo $response['id']; ?>">
                                                <i class="fas fa-star me-2"></i>Rate & Review Contractor
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Payment Pending</strong><br>
                                    Please complete your down payment to start the project.
                                </div>
                                <a href="payment.php?quote_response_id=<?php echo $response['id']; ?>" class="btn btn-primary">
                                    <i class="fas fa-credit-card me-2"></i>Make Payment
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            Submitted: <?php echo date('M j, Y \a\t g:i A', strtotime($response['created_at'])); ?>
                        </small>
                    </div>
                </div>
            <?php endforeach; ?>
            
            <div class="text-center mt-4">
                <a href="quotes.php" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to My Quotes
                </a>
            </div>
        <?php endif; ?>
    </div>
    <!-- Main Content End -->

    <!-- Review Modals -->
    <?php
    // Include the review form component
    require_once 'review_form_component.php';

    foreach ($responses as $response):
        if ($response['payment_status'] === 'completed' && !$response['review_id']):
            // Prepare data for the review form
            $contractor_data = [
                'contractor_id' => $response['contractor_id'],
                'business_name' => $response['business_name']
            ];

            $payment_data = [
                'payment_id' => $response['payment_id'],
                'quote_response_id' => $response['id']
            ];

            // Render the review form with unique modal ID
            renderReviewForm($contractor_data, $payment_data, 'reviewModal' . $response['id']);
        endif;
    endforeach;
    ?>



    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>

    <!-- Rating System JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle star rating clicks
            document.querySelectorAll('.rating-input').forEach(function(ratingContainer) {
                const stars = ratingContainer.querySelectorAll('.fa-star');
                const targetInput = ratingContainer.dataset.target;

                stars.forEach(function(star, index) {
                    star.addEventListener('click', function() {
                        const rating = parseInt(star.dataset.value);
                        ratingContainer.dataset.rating = rating;

                        // Update visual stars
                        stars.forEach(function(s, i) {
                            if (i < rating) {
                                s.classList.remove('far');
                                s.classList.add('fas');
                                s.style.color = '#ffc107';
                            } else {
                                s.classList.remove('fas');
                                s.classList.add('far');
                                s.style.color = '#dee2e6';
                            }
                        });

                        // Update hidden input
                        if (targetInput) {
                            document.getElementById(targetInput).value = rating;
                        } else {
                            // This is the main rating
                            const modalId = ratingContainer.closest('.modal').id.replace('reviewModal', '');
                            document.getElementById('rating' + modalId).value = rating;
                        }
                    });

                    // Hover effects
                    star.addEventListener('mouseenter', function() {
                        const hoverRating = parseInt(star.dataset.value);
                        stars.forEach(function(s, i) {
                            if (i < hoverRating) {
                                s.style.color = '#ffc107';
                            } else {
                                s.style.color = '#dee2e6';
                            }
                        });
                    });
                });

                // Reset on mouse leave
                ratingContainer.addEventListener('mouseleave', function() {
                    const currentRating = parseInt(ratingContainer.dataset.rating);
                    stars.forEach(function(s, i) {
                        if (i < currentRating) {
                            s.style.color = '#ffc107';
                        } else {
                            s.style.color = '#dee2e6';
                        }
                    });
                });
            });

            // Form validation
            document.querySelectorAll('form[id^="reviewForm"]').forEach(function(form) {
                form.addEventListener('submit', function(e) {
                    const modalId = form.id.replace('reviewForm', '');
                    const rating = document.getElementById('rating' + modalId).value;

                    if (!rating || rating < 1) {
                        e.preventDefault();
                        alert('Please provide an overall rating before submitting your review.');
                        return false;
                    }
                });
            });
        });
    </script>

    <style>
        .rating-input {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .rating-input .fa-star {
            color: #dee2e6;
            cursor: pointer;
            transition: color 0.2s;
            margin-right: 0.25rem;
        }

        .rating-input .fa-star:hover {
            color: #ffc107;
        }

        .modal-body {
            max-height: 70vh;
            overflow-y: auto;
        }
    </style>
</body>
</html>
