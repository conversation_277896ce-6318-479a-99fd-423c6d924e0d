<?php
require_once 'config/database.php';

echo "<h2>🔧 Fix Contractor Quotes Issue</h2>";

try {
    $issues_found = [];
    $fixes_applied = [];
    
    // Step 1: Check and fix database structure
    echo "<h3>Step 1: Database Structure</h3>";
    
    $stmt = $pdo->query("DESCRIBE quote_requests");
    $columns = $stmt->fetchAll();
    
    $has_specific_contractor_id = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'specific_contractor_id') {
            $has_specific_contractor_id = true;
            break;
        }
    }
    
    if (!$has_specific_contractor_id) {
        $issues_found[] = "Missing specific_contractor_id column";
        echo "<p style='color: red;'>❌ Missing specific_contractor_id column</p>";
        
        try {
            $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL");
            $fixes_applied[] = "Added specific_contractor_id column";
            echo "<p style='color: green;'>✅ Added specific_contractor_id column</p>";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ Column might already exist: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ specific_contractor_id column exists</p>";
    }
    
    // Step 2: Check and create service categories
    echo "<h3>Step 2: Service Categories</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM service_categories");
    $category_count = $stmt->fetchColumn();
    
    if ($category_count == 0) {
        $issues_found[] = "No service categories";
        echo "<p style='color: red;'>❌ No service categories found</p>";
        
        $categories = [
            ['House Construction', 'නිවාස ඉදිකිරීම්', 'வீட்டு கட்டுமானம்'],
            ['Building Renovation', 'ගොඩනැගිලි අලුත්වැඩියා', 'கட்டிட புதுப்பித்தல்'],
            ['Commercial Construction', 'වාණිජ ඉදිකිරීම්', 'வணிக கட்டுமானம්'],
            ['Interior Design & Finishing', 'අභ්‍යන්තර සැලසුම්', 'உள்துறை வடிவமைப்பு'],
            ['Roofing & Waterproofing', 'වහල සහ ජල ආරක්ෂණ', 'கூரை மற்றும் நீர்ப்புகாமை'],
            ['Electrical Work', 'විදුලි වැඩ', 'மின் வேலை'],
            ['Plumbing & Sanitation', 'ජල නල සහ සනීපාරක්ෂක', 'குழாய் மற்றும் சுகாதாரம்'],
            ['Landscaping & Gardening', 'භූමි අලංකරණ', 'இயற்கை அலங்காரம்']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO service_categories (name_en, name_si, name_ta) VALUES (?, ?, ?)");
        foreach ($categories as $category) {
            $stmt->execute($category);
        }
        
        $fixes_applied[] = "Created " . count($categories) . " service categories";
        echo "<p style='color: green;'>✅ Created " . count($categories) . " service categories</p>";
    } else {
        echo "<p style='color: green;'>✅ Found $category_count service categories</p>";
    }
    
    // Step 3: Check and create test contractor
    echo "<h3>Step 3: Test Contractor</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'contractor' AND status = 'approved'");
    $contractor_count = $stmt->fetchColumn();
    
    if ($contractor_count == 0) {
        $issues_found[] = "No approved contractors";
        echo "<p style='color: red;'>❌ No approved contractors found</p>";
        
        // Check if test contractor already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
        $stmt->execute();
        $existing_contractor = $stmt->fetch();
        
        if ($existing_contractor) {
            // Update existing contractor to approved
            $stmt = $pdo->prepare("UPDATE users SET status = 'approved' WHERE id = ?");
            $stmt->execute([$existing_contractor['id']]);
            $contractor_id = $existing_contractor['id'];
            echo "<p style='color: green;'>✅ Updated existing test contractor to approved</p>";
        } else {
            // Create new test contractor
            $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, 'contractor', 'approved')");
            $stmt->execute(['<EMAIL>', password_hash('password', PASSWORD_DEFAULT)]);
            $contractor_id = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ Created new test contractor</p>";
        }
        
        // Check if contractor profile exists
        $stmt = $pdo->prepare("SELECT id FROM contractor_profiles WHERE user_id = ?");
        $stmt->execute([$contractor_id]);
        $profile_exists = $stmt->fetch();
        
        if (!$profile_exists) {
            // Create contractor profile
            $stmt = $pdo->prepare("
                INSERT INTO contractor_profiles (
                    user_id, business_name, contact_person, phone, business_address,
                    service_areas, service_types, cida_registration, cida_grade,
                    business_description, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([
                $contractor_id,
                'Test Construction Company',
                'Test Contractor',
                '+94 77 123 4567',
                'Test Address, Colombo',
                json_encode(['Colombo', 'Gampaha', 'Kalutara']),
                json_encode([1, 2, 3]), // Service category IDs
                'CIDA/TEST/2024/001',
                'C5',
                'Test contractor for debugging purposes'
            ]);
            echo "<p style='color: green;'>✅ Created contractor profile</p>";
        }
        
        $fixes_applied[] = "Created/updated test contractor";
        echo "<p><strong>Test Contractor Login:</strong> <EMAIL> / password</p>";
    } else {
        echo "<p style='color: green;'>✅ Found $contractor_count approved contractors</p>";
    }
    
    // Step 4: Check and create test customer and quote request
    echo "<h3>Step 4: Test Quote Request</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM quote_requests WHERE status = 'open'");
    $quote_count = $stmt->fetchColumn();
    
    if ($quote_count == 0) {
        $issues_found[] = "No open quote requests";
        echo "<p style='color: red;'>❌ No open quote requests found</p>";
        
        // Check if test customer exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
        $stmt->execute();
        $existing_customer = $stmt->fetch();
        
        if ($existing_customer) {
            $customer_id = $existing_customer['id'];
            echo "<p style='color: green;'>✅ Using existing test customer</p>";
        } else {
            // Create test customer
            $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, 'customer', 'approved')");
            $stmt->execute(['<EMAIL>', password_hash('password', PASSWORD_DEFAULT)]);
            $customer_id = $pdo->lastInsertId();
            
            // Create customer profile
            $stmt = $pdo->prepare("
                INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district, address) 
                VALUES (?, 'Test', 'Customer', '+94 77 123 4567', 'Colombo', 'Test Address, Colombo')
            ");
            $stmt->execute([$customer_id]);
            echo "<p style='color: green;'>✅ Created test customer</p>";
        }
        
        // Create test quote request
        $stmt = $pdo->prepare("
            INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, status, specific_contractor_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'open', NULL)
        ");
        $stmt->execute([
            $customer_id,
            1, // House Construction
            'Test General Quote Request - ' . date('Y-m-d H:i:s'),
            'This is a test general quote request to verify contractors can see quotes. Looking for house construction services in Colombo area.',
            'Test Location, Colombo',
            'Colombo',
            5000000,
            '6 months'
        ]);
        $quote_id = $pdo->lastInsertId();
        
        $fixes_applied[] = "Created test quote request";
        echo "<p style='color: green;'>✅ Created test quote request (ID: $quote_id)</p>";
        echo "<p><strong>Test Customer Login:</strong> <EMAIL> / password</p>";
    } else {
        echo "<p style='color: green;'>✅ Found $quote_count open quote requests</p>";
    }
    
    // Step 5: Test the contractor query
    echo "<h3>Step 5: Test Contractor Query</h3>";
    
    $stmt = $pdo->query("
        SELECT u.id, cp.business_name, cp.service_areas, cp.service_types
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved' 
        LIMIT 1
    ");
    $test_contractor = $stmt->fetch();
    
    if ($test_contractor) {
        echo "<p>Testing with contractor: " . htmlspecialchars($test_contractor['business_name']) . "</p>";
        
        // Test the exact query from contractor/quotes.php
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
                   (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
                   CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
                   cp_contractor.service_types, cp_contractor.service_areas
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
            JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
            WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
            AND qr.status = 'open'
        ");
        
        $contractor_id = $test_contractor['id'];
        $stmt->execute([$contractor_id, $contractor_id, $contractor_id, $contractor_id]);
        $quotes = $stmt->fetchAll();
        
        echo "<p>SQL query found: " . count($quotes) . " quotes</p>";
        
        if (count($quotes) > 0) {
            // Test PHP filtering
            $filtered_quotes = [];
            foreach ($quotes as $quote) {
                if ($quote['specific_contractor_id'] == $contractor_id) {
                    $filtered_quotes[] = $quote;
                } elseif ($quote['specific_contractor_id'] === null) {
                    $contractor_services = json_decode($quote['service_types'], true) ?: [];
                    $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
                    
                    $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                                  in_array((string)$quote['service_category_id'], $contractor_services);
                    $has_area = in_array($quote['district'], $contractor_areas);
                    
                    if ($has_service && $has_area) {
                        $filtered_quotes[] = $quote;
                    }
                }
            }
            
            echo "<p>After PHP filtering: " . count($filtered_quotes) . " quotes</p>";
            
            if (count($filtered_quotes) > 0) {
                echo "<p style='color: green;'>✅ SUCCESS! Contractor should see quotes</p>";
                foreach ($filtered_quotes as $quote) {
                    echo "<div style='background: #e8f5e8; padding: 10px; margin: 5px 0;'>";
                    echo "<strong>" . htmlspecialchars($quote['title']) . "</strong><br>";
                    echo "Service: " . htmlspecialchars($quote['service_category'] ?? 'Unknown') . "<br>";
                    echo "District: " . $quote['district'] . "<br>";
                    echo "Type: " . $quote['quote_type'] . "<br>";
                    echo "</div>";
                }
            } else {
                echo "<p style='color: red;'>❌ No quotes after filtering - service/area mismatch</p>";
                $issues_found[] = "Service/area mismatch in contractor profile";
            }
        } else {
            echo "<p style='color: red;'>❌ SQL query returned no quotes</p>";
            $issues_found[] = "SQL query issue or missing JOINs";
        }
    }
    
    // Step 6: Summary and next steps
    echo "<h3>✅ Fix Complete</h3>";
    
    if (empty($issues_found)) {
        echo "<p style='color: green;'>🎉 No issues found! The system should be working correctly.</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Issues found and fixed:</p>";
        echo "<ul>";
        foreach ($issues_found as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
    }
    
    if (!empty($fixes_applied)) {
        echo "<p style='color: green;'>✅ Fixes applied:</p>";
        echo "<ul>";
        foreach ($fixes_applied as $fix) {
            echo "<li>$fix</li>";
        }
        echo "</ul>";
    }
    
    echo "<h4>Next Steps:</h4>";
    echo "<ol>";
    echo "<li><a href='contractor/login.php' target='_blank'>Login as contractor</a> (<EMAIL> / password)</li>";
    echo "<li><a href='contractor/quotes.php' target='_blank'>Check contractor quotes page</a></li>";
    echo "<li><a href='customer/login.php' target='_blank'>Login as customer</a> (<EMAIL> / password)</li>";
    echo "<li><a href='customer/request_quote.php' target='_blank'>Create more quote requests</a></li>";
    echo "</ol>";
    
    echo "<p><strong>If contractors still can't see quotes:</strong></p>";
    echo "<ul>";
    echo "<li>Clear browser cache and cookies</li>";
    echo "<li>Check browser console for JavaScript errors</li>";
    echo "<li>Verify contractor is logged in with correct credentials</li>";
    echo "<li>Check that contractor status is 'approved'</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
