<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔄 Test Full Contractor Flow</h2>";

try {
    // Step 1: Get or create test contractor
    echo "<h3>Step 1: Contractor Setup</h3>";
    
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $contractor_user = $stmt->fetch();
    
    if (!$contractor_user) {
        echo "<p style='color: red;'>❌ Test contractor not found. Creating...</p>";
        
        // Create test contractor
        $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, 'contractor', 'approved')");
        $stmt->execute(['<EMAIL>', password_hash('password', PASSWORD_DEFAULT)]);
        $contractor_id = $pdo->lastInsertId();
        
        // Create contractor profile
        $stmt = $pdo->prepare("
            INSERT INTO contractor_profiles (
                user_id, business_name, contact_person, phone, business_address,
                service_areas, service_types, cida_registration, cida_grade,
                business_description, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        $stmt->execute([
            $contractor_id,
            'Test Construction Company',
            'Test Contractor',
            '+94 77 123 4567',
            'Test Address, Colombo',
            json_encode(['Colombo', 'Gampaha', 'Kalutara']),
            json_encode([1, 2, 3]), // Service category IDs
            'CIDA/TEST/2024/001',
            'C5',
            'Test contractor for debugging purposes'
        ]);
        
        echo "<p style='color: green;'>✅ Created test contractor</p>";
    } else {
        $contractor_id = $contractor_user['id'];
        echo "<p style='color: green;'>✅ Using existing test contractor (ID: $contractor_id)</p>";
    }
    
    // Step 2: Simulate contractor login
    echo "<h3>Step 2: Simulate Contractor Login</h3>";
    
    $_SESSION['user_id'] = $contractor_id;
    $_SESSION['user_type'] = 'contractor';
    $_SESSION['email'] = '<EMAIL>';
    
    echo "<p style='color: green;'>✅ Simulated contractor login</p>";
    
    // Step 3: Test auth_check logic
    echo "<h3>Step 3: Test Authentication Logic</h3>";
    
    $stmt = $pdo->prepare("
        SELECT cp.*, u.email, u.status 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE cp.user_id = ?
    ");
    $stmt->execute([$contractor_id]);
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p style='color: red;'>❌ Contractor profile not found!</p>";
        exit;
    }
    
    if ($contractor['status'] !== 'approved') {
        echo "<p style='color: red;'>❌ Contractor not approved! Status: " . $contractor['status'] . "</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Contractor authenticated and approved</p>";
    echo "<p><strong>Business:</strong> " . htmlspecialchars($contractor['business_name']) . "</p>";
    echo "<p><strong>Service Areas:</strong> " . htmlspecialchars($contractor['service_areas']) . "</p>";
    echo "<p><strong>Service Types:</strong> " . htmlspecialchars($contractor['service_types']) . "</p>";
    
    // Step 4: Create test quote request if none exist
    echo "<h3>Step 4: Ensure Test Quote Requests Exist</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM quote_requests WHERE status = 'open'");
    $quote_count = $stmt->fetchColumn();
    
    if ($quote_count == 0) {
        echo "<p style='color: red;'>❌ No open quote requests found. Creating test quote...</p>";
        
        // Get or create test customer
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
        $stmt->execute();
        $customer_user = $stmt->fetch();
        
        if (!$customer_user) {
            $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, 'customer', 'approved')");
            $stmt->execute(['<EMAIL>', password_hash('password', PASSWORD_DEFAULT)]);
            $customer_id = $pdo->lastInsertId();
            
            $stmt = $pdo->prepare("
                INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district, address) 
                VALUES (?, 'Test', 'Customer', '+94 77 123 4567', 'Colombo', 'Test Address, Colombo')
            ");
            $stmt->execute([$customer_id]);
        } else {
            $customer_id = $customer_user['id'];
        }
        
        // Create test quote request
        $stmt = $pdo->prepare("
            INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, status, specific_contractor_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'open', NULL)
        ");
        $stmt->execute([
            $customer_id,
            1, // House Construction
            'Test Quote Request - ' . date('Y-m-d H:i:s'),
            'This is a test quote request for house construction in Colombo area.',
            'Test Location, Colombo',
            'Colombo',
            5000000,
            '6 months'
        ]);
        
        echo "<p style='color: green;'>✅ Created test quote request</p>";
    } else {
        echo "<p style='color: green;'>✅ Found $quote_count open quote requests</p>";
    }
    
    // Step 5: Test dashboard query (EXACT COPY)
    echo "<h3>Step 5: Test Dashboard Query</h3>";
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.id NOT IN (
            SELECT COALESCE(quote_request_id, 0)
            FROM quote_responses
            WHERE contractor_id = ?
        )
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
    $all_pending_quotes = $stmt->fetchAll();
    
    echo "<p>SQL returned: " . count($all_pending_quotes) . " quotes</p>";
    
    // Apply PHP filtering (EXACT COPY)
    $pending_quotes = 0;
    foreach ($all_pending_quotes as $quote) {
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $pending_quotes++;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];

            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);

            if ($has_service && $has_area) {
                $pending_quotes++;
            }
        }
    }
    
    echo "<p><strong>Dashboard pending quotes:</strong> $pending_quotes</p>";
    
    // Step 6: Test quotes page query (EXACT COPY)
    echo "<h3>Step 6: Test Quotes Page Query</h3>";
    
    // Default filter is "all"
    $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
    $params = [$contractor_id];
    $where_conditions[] = "(qr.status IN ('open', 'completed', 'cancelled') OR qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?))";
    $params[] = $contractor_id;
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
               cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");
    
    $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
    $stmt->execute($query_params);
    $all_quotes = $stmt->fetchAll();
    
    echo "<p>SQL returned: " . count($all_quotes) . " quotes</p>";
    
    // Apply PHP filtering (EXACT COPY)
    $quotes = [];
    foreach ($all_quotes as $quote) {
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $quotes[] = $quote;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];

            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);

            if ($has_service && $has_area) {
                $quotes[] = $quote;
            }
        }
    }
    
    echo "<p><strong>Quotes page total quotes:</strong> " . count($quotes) . "</p>";
    
    // Step 7: Final verification
    echo "<h3>✅ Final Verification</h3>";
    
    if ($pending_quotes > 0 && count($quotes) > 0) {
        echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 SUCCESS! The fix is working!</p>";
        echo "<p>Dashboard shows: $pending_quotes pending quotes</p>";
        echo "<p>Quotes page shows: " . count($quotes) . " total quotes</p>";
        
        echo "<h4>Test the actual pages:</h4>";
        echo "<ol>";
        echo "<li><a href='contractor/login.php' target='_blank'>Login as contractor</a> (<EMAIL> / password)</li>";
        echo "<li><a href='contractor/dashboard.php' target='_blank'>Check dashboard</a> - should show $pending_quotes pending quotes</li>";
        echo "<li><a href='contractor/quotes.php' target='_blank'>Check quotes page</a> - should show " . count($quotes) . " quotes</li>";
        echo "</ol>";
        
        // Show sample quotes
        echo "<h4>Sample quotes that should be visible:</h4>";
        foreach (array_slice($quotes, 0, 3) as $quote) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; background: #f9f9f9;'>";
            echo "<strong>" . htmlspecialchars($quote['title']) . "</strong><br>";
            echo "Service: " . htmlspecialchars($quote['service_category'] ?? 'Unknown') . "<br>";
            echo "District: " . $quote['district'] . "<br>";
            echo "Budget: Rs. " . number_format($quote['estimated_budget']) . "<br>";
            echo "</div>";
        }
        
    } else {
        echo "<p style='color: red; font-size: 18px; font-weight: bold;'>❌ Still not working!</p>";
        echo "<p>Dashboard shows: $pending_quotes pending quotes</p>";
        echo "<p>Quotes page shows: " . count($quotes) . " total quotes</p>";
        
        echo "<h4>Debugging info:</h4>";
        echo "<p><strong>Contractor service areas:</strong> " . htmlspecialchars($contractor['service_areas']) . "</p>";
        echo "<p><strong>Contractor service types:</strong> " . htmlspecialchars($contractor['service_types']) . "</p>";
        
        // Show why quotes are being filtered out
        foreach ($all_quotes as $quote) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
            echo "<strong>Quote:</strong> " . htmlspecialchars($quote['title']) . "<br>";
            echo "<strong>Service Category ID:</strong> " . $quote['service_category_id'] . "<br>";
            echo "<strong>District:</strong> " . $quote['district'] . "<br>";
            
            if ($quote['specific_contractor_id'] === null) {
                $contractor_services = json_decode($quote['service_types'], true) ?: [];
                $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
                
                $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                              in_array((string)$quote['service_category_id'], $contractor_services);
                $has_area = in_array($quote['district'], $contractor_areas);
                
                echo "<strong>Service match:</strong> " . ($has_service ? '✅' : '❌') . "<br>";
                echo "<strong>Area match:</strong> " . ($has_area ? '✅' : '❌') . "<br>";
            }
            echo "</div>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
