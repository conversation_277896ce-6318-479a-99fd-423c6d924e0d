-- Migration: Add specific_contractor_id to quote_requests table
-- This allows tracking when a quote is requested from a specific contractor

ALTER TABLE quote_requests 
ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline,
ADD INDEX idx_specific_contractor_id (specific_contractor_id),
ADD FOREIGN KEY (specific_contractor_id) REFERENCES users(id) ON DELETE SET NULL;

-- Update the table comment
ALTER TABLE quote_requests COMMENT = 'Customer quote requests - can be sent to all contractors or specific contractor';
