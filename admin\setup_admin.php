<?php
// Admin Setup Script - Run this once to create admin user
require_once '../config/database.php';

// Check if admin already exists
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE user_type = 'admin'");
    $stmt->execute();
    $admin_count = $stmt->fetchColumn();
    
    if ($admin_count > 0) {
        die("Admin user already exists! Delete this file for security.");
    }
} catch (PDOException $e) {
    die("Database error: " . $e->getMessage());
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validation
    if (empty($email) || empty($password) || empty($confirm_password)) {
        $error = 'All fields are required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Invalid email format.';
    } elseif (strlen($password) < 8) {
        $error = 'Password must be at least 8 characters long.';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } else {
        try {
            // Hash password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert admin user
            $stmt = $pdo->prepare("
                INSERT INTO users (email, password, user_type, status, created_at) 
                VALUES (?, ?, 'admin', 'approved', NOW())
            ");
            $stmt->execute([$email, $hashed_password]);
            
            $message = "Admin user created successfully! You can now login with your credentials.";
            
            // For security, you should delete this file after creating admin
            $message .= "<br><br><strong>IMPORTANT:</strong> Delete this setup file (setup_admin.php) for security reasons.";
            
        } catch (PDOException $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Admin Setup - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #06202B 0%, #1a3a4a 50%, #C5172E 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
        }
        
        .setup-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
            width: 100%;
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .setup-header i {
            font-size: 4rem;
            background: linear-gradient(135deg, #C5172E, #FF8C00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .setup-header h2 {
            color: #06202B;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .setup-header p {
            color: #6C757D;
            margin: 0;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #C5172E;
            box-shadow: 0 0 0 0.2rem rgba(197, 23, 46, 0.25);
        }
        
        .btn-setup {
            background: linear-gradient(135deg, #C5172E, #e41e3f);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-setup:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(197, 23, 46, 0.4);
            color: white;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            padding: 1rem 1.5rem;
        }
        
        .security-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 2rem;
            color: #856404;
        }
        
        .security-warning i {
            color: #FF8C00;
            margin-right: 0.5rem;
        }
    </style>
</head>

<body>
    <div class="setup-card">
        <div class="setup-header">
            <i class="fas fa-user-shield"></i>
            <h2>Admin Setup</h2>
            <p>Create your administrator account</p>
        </div>
        
        <div class="security-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Security Notice:</strong> This setup should only be run once. Delete this file after creating your admin account.
        </div>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
            </div>
            <div class="text-center mt-3">
                <a href="login.php" class="btn btn-setup">
                    <i class="fas fa-sign-in-alt me-2"></i>Go to Admin Login
                </a>
            </div>
        <?php else: ?>
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="mb-3">
                    <label for="email" class="form-label">Admin Email</label>
                    <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="password" name="password" placeholder="Enter secure password" required>
                    <div class="form-text">Password must be at least 8 characters long.</div>
                </div>
                
                <div class="mb-4">
                    <label for="confirm_password" class="form-label">Confirm Password</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Confirm your password" required>
                </div>
                
                <button type="submit" class="btn btn-setup">
                    <i class="fas fa-user-plus me-2"></i>Create Admin Account
                </button>
            </form>
        <?php endif; ?>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
