<?php
require_once 'config/database.php';

echo "<h2>🔍 Debug General Quote Issue</h2>";

try {
    // Step 1: Check database structure
    echo "<h3>Step 1: Database Structure Check</h3>";
    
    // Check if specific_contractor_id column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $column_exists = $stmt->fetch();
    
    if ($column_exists) {
        echo "<p style='color: green;'>✅ specific_contractor_id column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ specific_contractor_id column missing - adding it...</p>";
        $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        echo "<p style='color: green;'>✅ Added specific_contractor_id column</p>";
    }
    
    // Step 2: Check existing quote requests
    echo "<h3>Step 2: Existing Quote Requests</h3>";
    
    $stmt = $pdo->query("
        SELECT qr.*, sc.name_en as service_name 
        FROM quote_requests qr 
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id 
        WHERE qr.status = 'open' 
        ORDER BY qr.created_at DESC 
        LIMIT 10
    ");
    $quotes = $stmt->fetchAll();
    
    echo "<p>Found " . count($quotes) . " open quote requests:</p>";
    
    if (count($quotes) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Service</th><th>District</th><th>Specific Contractor</th><th>Created</th></tr>";
        foreach ($quotes as $quote) {
            echo "<tr>";
            echo "<td>" . $quote['id'] . "</td>";
            echo "<td>" . htmlspecialchars(substr($quote['title'], 0, 30)) . "...</td>";
            echo "<td>" . htmlspecialchars($quote['service_name'] ?? 'Unknown') . "</td>";
            echo "<td>" . htmlspecialchars($quote['district']) . "</td>";
            echo "<td>" . ($quote['specific_contractor_id'] ? 'Yes (' . $quote['specific_contractor_id'] . ')' : 'No (General)') . "</td>";
            echo "<td>" . $quote['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No open quote requests found</p>";
    }
    
    // Step 3: Check contractors
    echo "<h3>Step 3: Contractor Data Check</h3>";
    
    $stmt = $pdo->query("
        SELECT u.id, u.email, u.status, cp.business_name, cp.service_areas, cp.service_types 
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' 
        ORDER BY u.status DESC, u.id 
        LIMIT 10
    ");
    $contractors = $stmt->fetchAll();
    
    echo "<p>Found " . count($contractors) . " contractors:</p>";
    
    if (count($contractors) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Business Name</th><th>Status</th><th>Service Areas</th><th>Service Types</th></tr>";
        foreach ($contractors as $contractor) {
            $areas = json_decode($contractor['service_areas'], true);
            $types = json_decode($contractor['service_types'], true);
            
            echo "<tr>";
            echo "<td>" . $contractor['id'] . "</td>";
            echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
            echo "<td>" . $contractor['status'] . "</td>";
            echo "<td>" . (is_array($areas) ? implode(', ', $areas) : 'Invalid JSON') . "</td>";
            echo "<td>" . (is_array($types) ? implode(', ', $types) : 'Invalid JSON') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 4: Test matching logic with real data
    echo "<h3>Step 4: Test Matching Logic</h3>";
    
    if (count($quotes) > 0 && count($contractors) > 0) {
        $test_quote = $quotes[0]; // Use first quote
        echo "<p>Testing with quote: <strong>" . htmlspecialchars($test_quote['title']) . "</strong></p>";
        echo "<p>Service ID: " . $test_quote['service_category_id'] . ", District: " . $test_quote['district'] . "</p>";
        
        $matching_contractors = [];
        
        foreach ($contractors as $contractor) {
            if ($contractor['status'] !== 'approved') continue;
            
            $contractor_services = json_decode($contractor['service_types'], true) ?: [];
            $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$test_quote['service_category_id'], $contractor_services) ||
                          in_array((string)$test_quote['service_category_id'], $contractor_services);
            
            $has_area = in_array($test_quote['district'], $contractor_areas);
            
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
            echo "<strong>" . htmlspecialchars($contractor['business_name']) . "</strong> (ID: " . $contractor['id'] . ")<br>";
            echo "Status: " . $contractor['status'] . "<br>";
            echo "Service Areas: " . implode(', ', $contractor_areas) . "<br>";
            echo "Service Types: " . implode(', ', $contractor_services) . "<br>";
            echo "Has Service (" . $test_quote['service_category_id'] . "): " . ($has_service ? '✅ Yes' : '❌ No') . "<br>";
            echo "Has Area (" . $test_quote['district'] . "): " . ($has_area ? '✅ Yes' : '❌ No') . "<br>";
            echo "Match: " . (($has_service && $has_area) ? '✅ YES' : '❌ NO') . "<br>";
            echo "</div>";
            
            if ($has_service && $has_area) {
                $matching_contractors[] = $contractor;
            }
        }
        
        echo "<p><strong>Result: " . count($matching_contractors) . " contractors should see this quote</strong></p>";
        
        // Step 5: Test contractor dashboard query
        if (count($matching_contractors) > 0) {
            echo "<h3>Step 5: Test Contractor Dashboard Query</h3>";
            
            $test_contractor = $matching_contractors[0];
            $contractor_id = $test_contractor['id'];
            
            echo "<p>Testing with contractor: <strong>" . htmlspecialchars($test_contractor['business_name']) . "</strong> (ID: $contractor_id)</p>";
            
            // Test the exact dashboard query
            $stmt = $pdo->prepare("
                SELECT qr.*, cp.first_name, cp.last_name, cp.district,
                       (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
                       cp_contractor.service_types, cp_contractor.service_areas
                FROM quote_requests qr
                JOIN customer_profiles cp ON qr.customer_id = cp.user_id
                JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
                WHERE qr.status = 'open'
                AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
                ORDER BY qr.created_at DESC
                LIMIT 10
            ");
            $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
            $dashboard_quotes = $stmt->fetchAll();
            
            echo "<p>Dashboard SQL returned: " . count($dashboard_quotes) . " quotes</p>";
            
            // Apply PHP filtering
            $filtered_quotes = [];
            foreach ($dashboard_quotes as $quote) {
                if ($quote['specific_contractor_id'] == $contractor_id) {
                    $filtered_quotes[] = $quote;
                    continue;
                }
                
                if ($quote['specific_contractor_id'] === null) {
                    $contractor_services = json_decode($quote['service_types'], true) ?: [];
                    $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
                    
                    $has_service = in_array((int)$quote['service_category_id'], $contractor_services);
                    $has_area = in_array($quote['district'], $contractor_areas);
                    
                    if ($has_service && $has_area) {
                        $filtered_quotes[] = $quote;
                    }
                }
            }
            
            echo "<p>After PHP filtering: " . count($filtered_quotes) . " quotes</p>";
            
            if (count($filtered_quotes) > 0) {
                echo "<p style='color: green;'>✅ Contractor should see quotes on dashboard</p>";
            } else {
                echo "<p style='color: red;'>❌ Contractor won't see any quotes on dashboard</p>";
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Cannot test matching - need both quotes and contractors</p>";
    }
    
    // Step 6: Check notifications
    echo "<h3>Step 6: Check Notifications</h3>";
    
    $stmt = $pdo->query("
        SELECT n.*, u.email 
        FROM notifications n 
        JOIN users u ON n.user_id = u.id 
        WHERE n.type = 'quote_received' 
        ORDER BY n.created_at DESC 
        LIMIT 10
    ");
    $notifications = $stmt->fetchAll();
    
    echo "<p>Found " . count($notifications) . " quote notifications:</p>";
    
    if (count($notifications) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>User Email</th><th>Title</th><th>Message</th><th>Read</th><th>Created</th></tr>";
        foreach ($notifications as $notification) {
            echo "<tr>";
            echo "<td>" . $notification['id'] . "</td>";
            echo "<td>" . htmlspecialchars($notification['email']) . "</td>";
            echo "<td>" . htmlspecialchars($notification['title']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($notification['message'], 0, 50)) . "...</td>";
            echo "<td>" . ($notification['is_read'] ? 'Yes' : 'No') . "</td>";
            echo "<td>" . $notification['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>🎯 Summary</h3>";
    echo "<ul>";
    echo "<li>Database structure: " . ($column_exists ? '✅ OK' : '❌ Fixed') . "</li>";
    echo "<li>Open quotes: " . count($quotes) . "</li>";
    echo "<li>Approved contractors: " . count(array_filter($contractors, function($c) { return $c['status'] === 'approved'; })) . "</li>";
    echo "<li>Quote notifications: " . count($notifications) . "</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
