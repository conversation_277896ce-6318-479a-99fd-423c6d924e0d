<?php
require_once 'database.php';

echo "<h2>🔍 Testing Filter Functionality</h2>";

// Simulate filter parameters
$test_cases = [
    ['cida_grade' => 'C4', 'min_rating' => 0, 'sort_by' => 'rating'],
    ['cida_grade' => '', 'min_rating' => 4, 'sort_by' => 'rating'],
    ['cida_grade' => 'C3', 'min_rating' => 3, 'sort_by' => 'projects'],
    ['cida_grade' => '', 'min_rating' => 0, 'sort_by' => 'name']
];

foreach ($test_cases as $index => $test) {
    echo "<h3>Test Case " . ($index + 1) . "</h3>";
    echo "<p><strong>Parameters:</strong> CIDA Grade: " . ($test['cida_grade'] ?: 'Any') . 
         ", Min Rating: " . ($test['min_rating'] ?: 'Any') . 
         ", Sort By: " . $test['sort_by'] . "</p>";
    
    try {
        // Build search query like in contractors.php
        $where_conditions = ["u.status = 'approved'"];
        $params = [];
        
        if (!empty($test['cida_grade'])) {
            $where_conditions[] = "cp.cida_grade = ?";
            $params[] = $test['cida_grade'];
        }
        
        if ($test['min_rating'] > 0) {
            $where_conditions[] = "cp.average_rating >= ?";
            $params[] = $test['min_rating'];
        }
        
        // Build ORDER BY clause
        $order_by = "cp.average_rating DESC, cp.total_projects DESC";
        switch ($test['sort_by']) {
            case 'name':
                $order_by = "cp.business_name ASC";
                break;
            case 'projects':
                $order_by = "cp.total_projects DESC, cp.average_rating DESC";
                break;
            case 'newest':
                $order_by = "cp.created_at DESC";
                break;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "
            SELECT cp.business_name, cp.cida_grade, cp.average_rating, cp.total_projects, cp.created_at
            FROM contractor_profiles cp 
            JOIN users u ON cp.user_id = u.id 
            WHERE $where_clause
            ORDER BY $order_by
            LIMIT 5
        ";
        
        echo "<p><strong>SQL Query:</strong></p>";
        echo "<pre>" . htmlspecialchars($sql) . "</pre>";
        echo "<p><strong>Parameters:</strong> " . implode(', ', $params) . "</p>";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $results = $stmt->fetchAll();
        
        echo "<p><strong>Results:</strong> " . count($results) . " contractors found</p>";
        
        if (!empty($results)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 2rem;'>";
            echo "<tr><th>Business Name</th><th>CIDA Grade</th><th>Rating</th><th>Projects</th><th>Created</th></tr>";
            
            foreach ($results as $contractor) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
                echo "<td>" . htmlspecialchars($contractor['cida_grade']) . "</td>";
                echo "<td>" . number_format($contractor['average_rating'], 1) . "</td>";
                echo "<td>" . $contractor['total_projects'] . "</td>";
                echo "<td>" . date('Y-m-d', strtotime($contractor['created_at'])) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// Test available CIDA grades
echo "<h3>Available CIDA Grades</h3>";
try {
    $stmt = $pdo->prepare("
        SELECT DISTINCT cida_grade, COUNT(*) as count 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        GROUP BY cida_grade 
        ORDER BY cida_grade
    ");
    $stmt->execute();
    $grades = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>CIDA Grade</th><th>Count</th></tr>";
    foreach ($grades as $grade) {
        echo "<tr><td>" . htmlspecialchars($grade['cida_grade']) . "</td><td>" . $grade['count'] . "</td></tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test available ratings
echo "<h3>Available Ratings</h3>";
try {
    $stmt = $pdo->prepare("
        SELECT 
            CASE 
                WHEN average_rating >= 4 THEN '4+ Stars'
                WHEN average_rating >= 3 THEN '3+ Stars'
                WHEN average_rating >= 2 THEN '2+ Stars'
                ELSE 'Below 2 Stars'
            END as rating_range,
            COUNT(*) as count
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        GROUP BY rating_range
        ORDER BY MIN(average_rating) DESC
    ");
    $stmt->execute();
    $ratings = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Rating Range</th><th>Count</th></tr>";
    foreach ($ratings as $rating) {
        echo "<tr><td>" . htmlspecialchars($rating['rating_range']) . "</td><td>" . $rating['count'] . "</td></tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
