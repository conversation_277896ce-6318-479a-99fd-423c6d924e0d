<?php
require_once 'config/database.php';

echo "<h2>🔍 Contractor Quote System Test</h2>";

try {
    // Step 1: Check if specific_contractor_id column exists
    echo "<h3>Step 1: Database Structure Check</h3>";
    
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $column_exists = $stmt->fetch();
    
    if ($column_exists) {
        echo "<p style='color: green;'>✅ specific_contractor_id column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ specific_contractor_id column missing - adding it...</p>";
        $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        echo "<p style='color: green;'>✅ Added specific_contractor_id column</p>";
    }
    
    // Step 2: Check quote requests
    echo "<h3>Step 2: Quote Requests Analysis</h3>";
    
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_quotes,
            COUNT(CASE WHEN specific_contractor_id IS NOT NULL THEN 1 END) as direct_quotes,
            COUNT(CASE WHEN specific_contractor_id IS NULL THEN 1 END) as general_quotes,
            COUNT(CASE WHEN status = 'open' THEN 1 END) as open_quotes
        FROM quote_requests
    ");
    $quote_stats = $stmt->fetch();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Quote Statistics:</strong><br>";
    echo "Total Quotes: {$quote_stats['total_quotes']}<br>";
    echo "Direct Quotes: {$quote_stats['direct_quotes']}<br>";
    echo "General Quotes: {$quote_stats['general_quotes']}<br>";
    echo "Open Quotes: {$quote_stats['open_quotes']}<br>";
    echo "</div>";
    
    // Step 3: Check recent quotes
    if ($quote_stats['total_quotes'] > 0) {
        echo "<h4>Recent Quote Requests:</h4>";
        $stmt = $pdo->query("
            SELECT qr.id, qr.title, qr.district, qr.service_category_id, qr.specific_contractor_id, 
                   qr.status, qr.created_at, sc.name_en as service_category,
                   CASE WHEN qr.specific_contractor_id IS NOT NULL THEN 'Direct' ELSE 'General' END as quote_type
            FROM quote_requests qr
            LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
            ORDER BY qr.created_at DESC 
            LIMIT 5
        ");
        $recent_quotes = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Service</th>";
        echo "<th style='padding: 8px;'>District</th>";
        echo "<th style='padding: 8px;'>Type</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Created</th>";
        echo "</tr>";
        
        foreach ($recent_quotes as $quote) {
            $type_color = $quote['quote_type'] === 'Direct' ? '#007bff' : '#28a745';
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$quote['id']}</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($quote['service_category'] ?? 'Unknown') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($quote['district']) . "</td>";
            echo "<td style='padding: 8px; color: $type_color; font-weight: bold;'>{$quote['quote_type']}</td>";
            echo "<td style='padding: 8px;'>" . ucfirst($quote['status']) . "</td>";
            echo "<td style='padding: 8px;'>" . date('M j, Y g:i A', strtotime($quote['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 4: Check contractors
    echo "<h3>Step 3: Contractor Analysis</h3>";
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as total_contractors,
               COUNT(CASE WHEN cp.status = 'approved' THEN 1 END) as approved_contractors
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.user_type = 'contractor'
    ");
    $contractor_stats = $stmt->fetch();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Contractor Statistics:</strong><br>";
    echo "Total Contractors: {$contractor_stats['total_contractors']}<br>";
    echo "Approved Contractors: {$contractor_stats['approved_contractors']}<br>";
    echo "</div>";
    
    // Step 5: Test contractor quote matching
    if ($contractor_stats['approved_contractors'] > 0) {
        echo "<h4>Sample Contractor Quote Matching:</h4>";
        
        $stmt = $pdo->query("
            SELECT u.id, cp.business_name, cp.service_types, cp.service_areas, cp.status
            FROM users u
            JOIN contractor_profiles cp ON u.id = cp.user_id
            WHERE u.user_type = 'contractor' AND cp.status = 'approved'
            LIMIT 3
        ");
        $sample_contractors = $stmt->fetchAll();
        
        foreach ($sample_contractors as $contractor) {
            echo "<div style='border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>Contractor:</strong> " . htmlspecialchars($contractor['business_name']) . " (ID: {$contractor['id']})<br>";
            echo "<strong>Services:</strong> " . htmlspecialchars($contractor['service_types']) . "<br>";
            echo "<strong>Areas:</strong> " . htmlspecialchars($contractor['service_areas']) . "<br>";
            echo "<strong>Status:</strong> " . ucfirst($contractor['status']) . "<br>";
            
            // Test quote matching for this contractor
            $stmt = $pdo->prepare("
                SELECT qr.id, qr.title, qr.district, qr.service_category_id, qr.specific_contractor_id,
                       CASE WHEN qr.specific_contractor_id = ? THEN 'direct' ELSE 'general' END as quote_type
                FROM quote_requests qr
                WHERE qr.status = 'open'
                AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
                AND qr.id NOT IN (
                    SELECT COALESCE(quote_request_id, 0)
                    FROM quote_responses
                    WHERE contractor_id = ?
                )
                LIMIT 5
            ");
            $stmt->execute([$contractor['id'], $contractor['id'], $contractor['id']]);
            $potential_quotes = $stmt->fetchAll();
            
            // Filter quotes using PHP logic
            $matching_quotes = [];
            foreach ($potential_quotes as $quote) {
                if ($quote['quote_type'] === 'direct') {
                    $matching_quotes[] = $quote;
                } else {
                    // Check service and area match for general quotes
                    $contractor_services = json_decode($contractor['service_types'], true) ?: [];
                    $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];
                    
                    $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                                  in_array((string)$quote['service_category_id'], $contractor_services);
                    $has_area = in_array($quote['district'], $contractor_areas);
                    
                    if ($has_service && $has_area) {
                        $matching_quotes[] = $quote;
                    }
                }
            }
            
            echo "<strong>Matching Quotes:</strong> " . count($matching_quotes) . " out of " . count($potential_quotes) . " potential quotes<br>";
            echo "</div>";
        }
    }
    
    echo "<h3>✅ Quote System Test Complete</h3>";
    echo "<p>The contractor quote system appears to be properly configured and ready for use.</p>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<strong>Key Features Verified:</strong><br>";
    echo "• Database structure with specific_contractor_id column<br>";
    echo "• Direct vs General quote distinction<br>";
    echo "• Contractor service and area matching<br>";
    echo "• Quote response tracking<br>";
    echo "• Proper filtering logic<br>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h2, h3, h4 {
    color: #333;
}

table {
    font-size: 14px;
}

th {
    font-weight: bold;
}
</style>
