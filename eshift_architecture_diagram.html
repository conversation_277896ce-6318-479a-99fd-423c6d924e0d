<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>e-Shift System Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background-color: white;
        }

        .architecture-container {
            width: 900px;
            margin: 0 auto;
            position: relative;
        }

        .layer {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-bottom: 120px;
            align-items: center;
            position: relative;
        }

        .layer-label {
            position: absolute;
            left: -150px;
            font-weight: bold;
            font-size: 16px;
            color: #333;
            width: 120px;
            text-align: right;
            top: 50%;
            transform: translateY(-50%);
        }

        .component {
            border: 2px solid #333;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            background-color: white;
            width: 160px;
            height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .component-icon {
            font-size: 28px;
            margin-bottom: 8px;
        }

        .component-title {
            font-weight: bold;
            font-size: 14px;
            line-height: 1.3;
            margin-bottom: 4px;
        }

        .component-subtitle {
            font-size: 11px;
            color: #666;
            font-style: italic;
        }

        .database {
            border-radius: 60px;
            width: 300px;
            height: 120px;
            margin: 0 auto;
        }

        .arrow {
            position: absolute;
            z-index: 10;
        }

        .arrow-line {
            stroke: #333;
            stroke-width: 2;
            fill: none;
            marker-end: url(#arrowhead);
        }

        .connection-label {
            position: absolute;
            font-size: 12px;
            color: #666;
            background-color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="architecture-container">
        <!-- SVG for arrows -->
        <svg width="900" height="600" style="position: absolute; top: 0; left: 0; z-index: 1;">
            <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7"
                        refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
                </marker>
            </defs>

            <!-- Arrows from Presentation to Application -->
            <line x1="230" y1="140" x2="230" y2="200" class="arrow-line" />
            <line x1="450" y1="140" x2="450" y2="200" class="arrow-line" />
            <line x1="670" y1="140" x2="670" y2="200" class="arrow-line" />

            <!-- Arrows from Application to Data Layer -->
            <line x1="300" y1="340" x2="400" y2="420" class="arrow-line" />
            <line x1="450" y1="340" x2="450" y2="420" class="arrow-line" />
            <line x1="600" y1="340" x2="500" y2="420" class="arrow-line" />

            <!-- Connection label -->
            <text x="420" y="390" font-family="Arial" font-size="12" fill="#666" text-anchor="middle">HTTPS</text>
        </svg>

        <!-- Presentation Layer -->
        <div class="layer" style="position: relative; z-index: 2;">
            <div class="layer-label">Presentation</div>

            <div class="component">
                <div class="component-icon">🖥️</div>
                <div class="component-title">Admin Dashboard</div>
                <div class="component-subtitle">WinForms Interface</div>
            </div>

            <div class="component">
                <div class="component-icon">👤</div>
                <div class="component-title">Customer Interface</div>
                <div class="component-subtitle">User Portal</div>
            </div>

            <div class="component">
                <div class="component-icon">📱</div>
                <div class="component-title">WinForms Application</div>
                <div class="component-subtitle">Desktop Client</div>
            </div>
        </div>

        <!-- Application Layer -->
        <div class="layer" style="position: relative; z-index: 2;">
            <div class="layer-label">Application</div>

            <div class="component">
                <div class="component-icon">📋</div>
                <div class="component-title">Job Management Service</div>
                <div class="component-subtitle">(Core Logic)</div>
            </div>

            <div class="component">
                <div class="component-icon">🚛</div>
                <div class="component-title">Load Assignment Service</div>
                <div class="component-subtitle">(Transport Units)</div>
            </div>

            <div class="component">
                <div class="component-icon">🔐</div>
                <div class="component-title">Authentication & Authorization</div>
                <div class="component-subtitle">(Multi-role Login)</div>
            </div>
        </div>

        <!-- Data Layer -->
        <div class="layer" style="position: relative; z-index: 2;">
            <div class="layer-label">Data Layer</div>

            <div class="component database">
                <div class="component-icon">🗄️</div>
                <div class="component-title">SQL Server 2019 Database</div>
                <div class="component-subtitle">(e-Shift System<br/>Customers, Jobs, Loads, Admins, Transport Units)</div>
            </div>

            <!-- Additional components beside database -->
            <div class="component" style="position: absolute; right: -220px; top: 0;">
                <div class="component-icon">⚙️</div>
                <div class="component-title">Automated Reports</div>
                <div class="component-subtitle">• Daily job reports<br/>• Transport analytics</div>
            </div>
        </div>
    </div>
</body>
</html>
