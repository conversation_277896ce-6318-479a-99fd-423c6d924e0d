<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>e-Shift System Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: white;
        }
        
        .architecture-container {
            width: 800px;
            margin: 0 auto;
            position: relative;
        }
        
        .layer {
            display: flex;
            justify-content: space-around;
            margin-bottom: 80px;
            align-items: center;
        }
        
        .layer-label {
            position: absolute;
            left: -120px;
            font-weight: bold;
            font-size: 14px;
            color: #333;
            width: 100px;
            text-align: right;
        }
        
        .component {
            border: 2px solid #333;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            background-color: white;
            width: 140px;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }
        
        .component-icon {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .component-title {
            font-weight: bold;
            font-size: 12px;
            line-height: 1.2;
        }
        
        .component-subtitle {
            font-size: 10px;
            color: #666;
            margin-top: 2px;
        }
        
        .arrow {
            position: absolute;
            width: 2px;
            background-color: #333;
            z-index: 1;
        }
        
        .arrow-vertical {
            height: 60px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .arrow-head {
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 10px solid #333;
        }
        
        .database {
            border-radius: 50px;
            width: 200px;
            height: 100px;
            margin: 0 auto;
        }
        
        .connection-label {
            position: absolute;
            font-size: 10px;
            color: #666;
            background-color: white;
            padding: 2px 4px;
        }
    </style>
</head>
<body>
    <div class="architecture-container">
        <!-- Presentation Layer -->
        <div class="layer" style="position: relative;">
            <div class="layer-label">Presentation</div>
            
            <div class="component">
                <div class="component-icon">🖥️</div>
                <div class="component-title">Admin Dashboard</div>
                <div class="component-subtitle">WinForms Interface</div>
            </div>
            
            <div class="component">
                <div class="component-icon">👤</div>
                <div class="component-title">Customer Interface</div>
                <div class="component-subtitle">User Portal</div>
            </div>
            
            <div class="component">
                <div class="component-icon">📱</div>
                <div class="component-title">WinForms Application</div>
                <div class="component-subtitle">Desktop Client</div>
            </div>
            
            <!-- Arrows from Presentation to Business Logic -->
            <div class="arrow arrow-vertical" style="top: 95px; left: 140px;">
                <div class="arrow-head"></div>
            </div>
            <div class="arrow arrow-vertical" style="top: 95px; left: 400px;">
                <div class="arrow-head"></div>
            </div>
            <div class="arrow arrow-vertical" style="top: 95px; left: 660px;">
                <div class="arrow-head"></div>
            </div>
        </div>
        
        <!-- Business Logic Layer -->
        <div class="layer" style="position: relative;">
            <div class="layer-label">Application</div>
            
            <div class="component">
                <div class="component-icon">📋</div>
                <div class="component-title">Job Management</div>
                <div class="component-subtitle">(Core Logic)</div>
            </div>
            
            <div class="component">
                <div class="component-icon">🚛</div>
                <div class="component-title">Load Assignment</div>
                <div class="component-subtitle">(Transport Units)</div>
            </div>
            
            <div class="component">
                <div class="component-icon">🔐</div>
                <div class="component-title">Authentication & Authorization</div>
                <div class="component-subtitle">(Multi-role Access)</div>
            </div>

            <!-- Additional Business Logic Components -->
            <div style="position: absolute; top: 120px; left: 50px;">
                <div class="component" style="width: 120px;">
                    <div class="component-icon">👥</div>
                    <div class="component-title">Customer Management</div>
                    <div class="component-subtitle">(User Operations)</div>
                </div>
            </div>

            <div style="position: absolute; top: 120px; right: 50px;">
                <div class="component" style="width: 120px;">
                    <div class="component-icon">📊</div>
                    <div class="component-title">Report Generator</div>
                    <div class="component-subtitle">(Analytics)</div>
                </div>
            </div>

            <!-- Arrows from Business Logic to Data Access -->
            <div class="arrow arrow-vertical" style="top: 95px; left: 140px;">
                <div class="arrow-head"></div>
            </div>
            <div class="arrow arrow-vertical" style="top: 95px; left: 400px;">
                <div class="arrow-head"></div>
            </div>
            <div class="arrow arrow-vertical" style="top: 95px; left: 660px;">
                <div class="arrow-head"></div>
            </div>

            <div class="connection-label" style="top: 110px; left: 350px;">CRUD Operations</div>
        </div>

        <!-- Data Access Layer -->
        <div class="layer" style="position: relative; margin-top: 140px;">
            <div class="layer-label">Data Layer</div>

            <div class="component database">
                <div class="component-icon">🗄️</div>
                <div class="component-title">SQL Server 2019 Database</div>
                <div class="component-subtitle">(Customers, Jobs, Loads, Admins, Transport Units)</div>
            </div>

            <!-- Data Access Objects positioned around database -->
            <div style="position: absolute; top: -60px; left: 50px;">
                <div class="component" style="width: 100px; height: 60px;">
                    <div class="component-title">CustomerDAO</div>
                    <div class="component-subtitle">CRUD Ops</div>
                </div>
            </div>

            <div style="position: absolute; top: -60px; right: 50px;">
                <div class="component" style="width: 100px; height: 60px;">
                    <div class="component-title">JobDAO</div>
                    <div class="component-subtitle">Job Ops</div>
                </div>
            </div>

            <div style="position: absolute; top: 120px; left: 50px;">
                <div class="component" style="width: 100px; height: 60px;">
                    <div class="component-title">LoadDAO</div>
                    <div class="component-subtitle">Load Ops</div>
                </div>
            </div>

            <div style="position: absolute; top: 120px; right: 50px;">
                <div class="component" style="width: 100px; height: 60px;">
                    <div class="component-title">AdminDAO</div>
                    <div class="component-subtitle">Admin Ops</div>
                </div>
            </div>

            <!-- Arrows pointing to database -->
            <div class="arrow" style="width: 80px; height: 2px; top: 30px; left: 150px; transform: rotate(-30deg);"></div>
            <div class="arrow" style="width: 80px; height: 2px; top: 30px; right: 150px; transform: rotate(30deg);"></div>
            <div class="arrow" style="width: 80px; height: 2px; bottom: 30px; left: 150px; transform: rotate(30deg);"></div>
            <div class="arrow" style="width: 80px; height: 2px; bottom: 30px; right: 150px; transform: rotate(-30deg);"></div>
        </div>
    </div>
</body>
</html>
