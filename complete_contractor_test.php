<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔧 Complete Contractor Test & Fix</h2>";

// Step 1: Check/Create contractor
echo "<h3>Step 1: Contractor Setup</h3>";

$stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND user_type = 'contractor'");
$stmt->execute(['<EMAIL>']);
$contractor_user = $stmt->fetch();

if (!$contractor_user) {
    echo "<p>❌ Contractor not found. Creating contractor...</p>";
    
    try {
        // Create contractor user
        $stmt = $pdo->prepare("
            INSERT INTO users (email, password, user_type, status, created_at)
            VALUES (?, ?, 'contractor', 'approved', NOW())
        ");
        $stmt->execute(['<EMAIL>', password_hash('password123', PASSWORD_DEFAULT)]);
        $contractor_id = $pdo->lastInsertId();
        
        // Create contractor profile
        $stmt = $pdo->prepare("
            INSERT INTO contractor_profiles (user_id, business_name, contact_person, phone, service_types, service_areas, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $contractor_id,
            'K Constructions',
            'K Construction Manager',
            '0771234567',
            json_encode([1, 2, 3, 4, 5, 6]), // All services
            json_encode(['Colombo', 'Gampaha', 'Kalutara', 'Kandy']) // Multiple areas including Kalutara
        ]);
        
        echo "<p>✅ Created contractor user and profile</p>";
        
        // Fetch the created user
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$contractor_id]);
        $contractor_user = $stmt->fetch();
        
    } catch (PDOException $e) {
        echo "<p>❌ Error creating contractor: " . $e->getMessage() . "</p>";
        exit;
    }
} else {
    echo "<p>✅ Contractor exists: {$contractor_user['email']}</p>";
}

// Ensure contractor is approved
if ($contractor_user['status'] !== 'approved') {
    $stmt = $pdo->prepare("UPDATE users SET status = 'approved' WHERE id = ?");
    $stmt->execute([$contractor_user['id']]);
    echo "<p>✅ Contractor approved</p>";
}

// Check/fix contractor profile
$stmt = $pdo->prepare("SELECT * FROM contractor_profiles WHERE user_id = ?");
$stmt->execute([$contractor_user['id']]);
$contractor_profile = $stmt->fetch();

if (!$contractor_profile) {
    echo "<p>❌ No contractor profile. Creating...</p>";
    
    $stmt = $pdo->prepare("
        INSERT INTO contractor_profiles (user_id, business_name, contact_person, phone, service_types, service_areas, created_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW())
    ");
    $stmt->execute([
        $contractor_user['id'],
        'K Constructions',
        'K Construction Manager',
        '0771234567',
        json_encode([1, 2, 3, 4, 5, 6]),
        json_encode(['Colombo', 'Gampaha', 'Kalutara', 'Kandy'])
    ]);
    echo "<p>✅ Created contractor profile</p>";
} else {
    // Update profile to ensure it has all needed services and areas
    $services = json_decode($contractor_profile['service_types'], true) ?: [];
    $areas = json_decode($contractor_profile['service_areas'], true) ?: [];
    
    // Add missing services (1-6)
    for ($i = 1; $i <= 6; $i++) {
        if (!in_array($i, $services) && !in_array((string)$i, $services)) {
            $services[] = $i;
        }
    }
    
    // Add missing areas
    $required_areas = ['Colombo', 'Gampaha', 'Kalutara', 'Kandy'];
    foreach ($required_areas as $area) {
        if (!in_array($area, $areas)) {
            $areas[] = $area;
        }
    }
    
    $stmt = $pdo->prepare("UPDATE contractor_profiles SET service_types = ?, service_areas = ? WHERE user_id = ?");
    $stmt->execute([json_encode($services), json_encode($areas), $contractor_user['id']]);
    echo "<p>✅ Updated contractor profile with all services and areas</p>";
}

// Step 2: Check/Create quote requests
echo "<h3>Step 2: Quote Requests Setup</h3>";

$stmt = $pdo->query("SELECT COUNT(*) FROM quote_requests WHERE status = 'open'");
$open_quotes = $stmt->fetchColumn();

if ($open_quotes == 0) {
    echo "<p>❌ No open quotes. Creating test quotes...</p>";
    
    // Check/create customer
    $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' LIMIT 1");
    $customer = $stmt->fetch();
    
    if (!$customer) {
        // Create test customer
        $stmt = $pdo->prepare("
            INSERT INTO users (email, password, user_type, status, created_at)
            VALUES (?, ?, 'customer', 'active', NOW())
        ");
        $stmt->execute(['<EMAIL>', password_hash('password123', PASSWORD_DEFAULT)]);
        $customer_id = $pdo->lastInsertId();
        
        $stmt = $pdo->prepare("
            INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district, created_at)
            VALUES (?, 'Test', 'Customer', '0771234567', 'Kalutara', NOW())
        ");
        $stmt->execute([$customer_id]);
        
        $customer = ['id' => $customer_id];
        echo "<p>✅ Created test customer</p>";
    }
    
    // Create test quotes
    $test_quotes = [
        [
            'title' => 'Plumbing Work in Kalutara',
            'service_id' => 6,
            'district' => 'Kalutara',
            'description' => 'Need plumbing work for bathroom renovation'
        ],
        [
            'title' => 'Electrical Work in Colombo',
            'service_id' => 5,
            'district' => 'Colombo',
            'description' => 'House wiring and electrical installation'
        ],
        [
            'title' => 'Construction Work in Gampaha',
            'service_id' => 1,
            'district' => 'Gampaha',
            'description' => 'Small house construction project'
        ]
    ];
    
    foreach ($test_quotes as $quote) {
        $stmt = $pdo->prepare("
            INSERT INTO quote_requests (customer_id, service_category_id, title, description, estimated_budget, location, district, timeline, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'open', NOW())
        ");
        $stmt->execute([
            $customer['id'],
            $quote['service_id'],
            $quote['title'],
            $quote['description'],
            50000,
            'Test Location, ' . $quote['district'],
            $quote['district'],
            '2 weeks'
        ]);
    }
    
    echo "<p>✅ Created " . count($test_quotes) . " test quotes</p>";
} else {
    echo "<p>✅ Found {$open_quotes} open quotes</p>";
}

// Step 3: Test contractor login
echo "<h3>Step 3: Contractor Login Test</h3>";

$_SESSION['user_id'] = $contractor_user['id'];
$_SESSION['user_type'] = 'contractor';
$_SESSION['email'] = $contractor_user['email'];

echo "<p>✅ Logged in as contractor (ID: {$contractor_user['id']})</p>";

// Step 4: Test dashboard logic
echo "<h3>Step 4: Dashboard Logic Test</h3>";

// Get contractor data
$stmt = $pdo->prepare("SELECT * FROM contractor_profiles WHERE user_id = ?");
$stmt->execute([$contractor_user['id']]);
$contractor_data = $stmt->fetch();

$contractor_services = json_decode($contractor_data['service_types'], true) ?: [];
$contractor_areas = json_decode($contractor_data['service_areas'], true) ?: [];

echo "<p><strong>Contractor Services:</strong> [" . implode(', ', $contractor_services) . "]</p>";
echo "<p><strong>Contractor Areas:</strong> [" . implode(', ', $contractor_areas) . "]</p>";

// Test pending quotes
$stmt = $pdo->prepare("
    SELECT qr.*
    FROM quote_requests qr
    WHERE qr.status = 'open'
    AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
    AND qr.id NOT IN (
        SELECT COALESCE(quote_request_id, 0)
        FROM quote_responses
        WHERE contractor_id = ?
    )
");
$stmt->execute([$contractor_user['id'], $contractor_user['id']]);
$all_pending_quotes = $stmt->fetchAll();

$pending_quotes = [];
foreach ($all_pending_quotes as $quote) {
    if ($quote['specific_contractor_id'] == $contractor_user['id']) {
        $pending_quotes[] = $quote;
    } elseif ($quote['specific_contractor_id'] === null) {
        $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                      in_array((string)$quote['service_category_id'], $contractor_services);
        $has_area = in_array($quote['district'], $contractor_areas);
        if ($has_service && $has_area) {
            $pending_quotes[] = $quote;
        }
    }
}

echo "<p><strong>Pending Quotes Count:</strong> " . count($pending_quotes) . "</p>";

// Test recent quotes
$stmt = $pdo->prepare("
    SELECT qr.*, cp.first_name, cp.last_name, cp.district,
           (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
    FROM quote_requests qr
    JOIN customer_profiles cp ON qr.customer_id = cp.user_id
    WHERE qr.status = 'open'
    AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
    ORDER BY qr.created_at DESC
    LIMIT 10
");
$stmt->execute([$contractor_user['id'], $contractor_user['id']]);
$all_recent_quotes = $stmt->fetchAll();

$recent_quotes = [];
foreach ($all_recent_quotes as $quote) {
    if ($quote['specific_contractor_id'] == $contractor_user['id']) {
        $recent_quotes[] = $quote;
    } elseif ($quote['specific_contractor_id'] === null) {
        $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                      in_array((string)$quote['service_category_id'], $contractor_services);
        $has_area = in_array($quote['district'], $contractor_areas);
        if ($has_service && $has_area) {
            $recent_quotes[] = $quote;
        }
    }
    if (count($recent_quotes) >= 5) break;
}

echo "<p><strong>Recent Quotes Count:</strong> " . count($recent_quotes) . "</p>";

if (count($recent_quotes) > 0) {
    echo "<h4>Recent Quotes:</h4>";
    foreach ($recent_quotes as $quote) {
        echo "<p>- ID {$quote['id']}: {$quote['title']} ({$quote['district']})</p>";
    }
}

// Step 5: Results
echo "<h3>🎯 Results</h3>";

if (count($pending_quotes) > 0 || count($recent_quotes) > 0) {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<p><strong>✅ SUCCESS!</strong> The contractor should now see quotes in their dashboard.</p>";
    echo "<p><strong>Dashboard should show:</strong></p>";
    echo "<ul>";
    echo "<li>Pending Quotes: " . count($pending_quotes) . "</li>";
    echo "<li>Recent Quotes: " . count($recent_quotes) . "</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<p><strong>❌ ISSUE:</strong> No quotes will be displayed.</p>";
    echo "<p>This could be due to:</p>";
    echo "<ul>";
    echo "<li>No matching service types or areas</li>";
    echo "<li>All quotes have been responded to</li>";
    echo "<li>Code issues in the dashboard</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h3>🔗 Access Dashboard</h3>";
echo "<p><a href='contractor/dashboard.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Open Contractor Dashboard</a></p>";
echo "<p><a href='contractor/quotes.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📋 Open Quote Requests Page</a></p>";

echo "<p><strong>Login Details:</strong></p>";
echo "<p>Email: <EMAIL></p>";
echo "<p>Password: password123</p>";
?>
