<?php
require_once 'database.php';

echo "<h2>Debug Contractor Dashboard Data</h2>";

// Check if contractor exists
$stmt = $pdo->prepare("SELECT * FROM users WHERE email = '<EMAIL>'");
$stmt->execute();
$contractor_user = $stmt->fetch();

if ($contractor_user) {
    echo "<h3>✅ Contractor User Found</h3>";
    echo "<p><strong>User ID:</strong> " . $contractor_user['id'] . "</p>";
    echo "<p><strong>Email:</strong> " . $contractor_user['email'] . "</p>";
    echo "<p><strong>Status:</strong> " . $contractor_user['status'] . "</p>";
    
    $contractor_id = $contractor_user['id'];
    
    // Check quote responses
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM quote_responses WHERE contractor_id = ?");
    $stmt->execute([$contractor_id]);
    $quote_responses_count = $stmt->fetchColumn();
    echo "<p><strong>Quote Responses:</strong> " . $quote_responses_count . "</p>";
    
    // Check all quote responses for this contractor
    $stmt = $pdo->prepare("SELECT * FROM quote_responses WHERE contractor_id = ?");
    $stmt->execute([$contractor_id]);
    $quote_responses = $stmt->fetchAll();
    echo "<h4>Quote Responses Details:</h4>";
    foreach ($quote_responses as $response) {
        echo "<p>- Quote Request ID: " . $response['quote_request_id'] . ", Amount: " . $response['quoted_amount'] . ", Status: " . $response['status'] . "</p>";
    }
    
    // Check quote requests
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM quote_requests WHERE status = 'open'");
    $stmt->execute();
    $open_quotes_count = $stmt->fetchColumn();
    echo "<p><strong>Total Open Quote Requests:</strong> " . $open_quotes_count . "</p>";
    
    // Check pending quotes (open quotes not responded to by this contractor)
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM quote_requests qr
        WHERE qr.status = 'open'
        AND qr.id NOT IN (
            SELECT quote_request_id
            FROM quote_responses
            WHERE contractor_id = ?
        )
    ");
    $stmt->execute([$contractor_id]);
    $pending_quotes_count = $stmt->fetchColumn();
    echo "<p><strong>Pending Quotes (not responded):</strong> " . $pending_quotes_count . "</p>";
    
    // Check reviews
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM reviews WHERE contractor_id = ?");
    $stmt->execute([$contractor_id]);
    $reviews_count = $stmt->fetchColumn();
    echo "<p><strong>Total Reviews:</strong> " . $reviews_count . "</p>";
    
    // Check approved reviews
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM reviews WHERE contractor_id = ? AND is_approved = 1");
    $stmt->execute([$contractor_id]);
    $approved_reviews_count = $stmt->fetchColumn();
    echo "<p><strong>Approved Reviews:</strong> " . $approved_reviews_count . "</p>";
    
    // Check all reviews for this contractor
    $stmt = $pdo->prepare("SELECT * FROM reviews WHERE contractor_id = ?");
    $stmt->execute([$contractor_id]);
    $reviews = $stmt->fetchAll();
    echo "<h4>Reviews Details:</h4>";
    foreach ($reviews as $review) {
        echo "<p>- Customer ID: " . $review['customer_id'] . ", Rating: " . $review['rating'] . ", Approved: " . (isset($review['is_approved']) ? $review['is_approved'] : 'N/A') . "</p>";
    }
    
    // Check portfolio projects
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM contractor_portfolios WHERE contractor_id = ?");
    $stmt->execute([$contractor_id]);
    $portfolio_count = $stmt->fetchColumn();
    echo "<p><strong>Portfolio Projects:</strong> " . $portfolio_count . "</p>";
    
    // Check all portfolio projects for this contractor
    $stmt = $pdo->prepare("SELECT * FROM contractor_portfolios WHERE contractor_id = ?");
    $stmt->execute([$contractor_id]);
    $portfolios = $stmt->fetchAll();
    echo "<h4>Portfolio Projects Details:</h4>";
    foreach ($portfolios as $portfolio) {
        echo "<p>- Project: " . $portfolio['project_name'] . ", Value: " . $portfolio['project_value'] . ", Featured: " . $portfolio['is_featured'] . "</p>";
    }
    
} else {
    echo "<h3>❌ Contractor User Not Found</h3>";
    echo "<p>Email '<EMAIL>' not found in users table.</p>";
}

// Check table structures
echo "<h3>Table Structure Check</h3>";

// Check if reviews table has the right columns
$stmt = $pdo->prepare("DESCRIBE reviews");
$stmt->execute();
$reviews_columns = $stmt->fetchAll();
echo "<h4>Reviews Table Columns:</h4>";
foreach ($reviews_columns as $column) {
    echo "<p>- " . $column['Field'] . " (" . $column['Type'] . ")</p>";
}

?>
