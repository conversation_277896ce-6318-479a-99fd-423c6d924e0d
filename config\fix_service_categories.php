<?php
require_once 'database.php';

echo "<h2>🔧 Fixing Service Categories and Contractor Data</h2>";

try {
    // First, let's update the service categories to match what we want
    $new_categories = [
        1 => 'House Construction',
        2 => 'Building Renovation', 
        3 => 'Commercial Construction',
        4 => 'Interior Design & Finishing',
        5 => 'Roofing & Waterproofing',
        6 => 'Electrical Work',
        7 => 'Plumbing & Sanitation',
        8 => 'Landscaping & Gardening',
        9 => 'Swimming Pool Construction',
        10 => 'Road & Infrastructure'
    ];
    
    echo "<h3>Step 1: Updating Service Categories</h3>";

    // Update existing categories instead of deleting (due to foreign key constraints)
    foreach ($new_categories as $id => $name) {
        $stmt = $pdo->prepare("
            INSERT INTO service_categories (id, name_en, name_si, name_ta, is_active)
            VALUES (?, ?, ?, ?, 1)
            ON DUPLICATE KEY UPDATE
            name_en = VALUES(name_en),
            name_si = VALUES(name_si),
            name_ta = VALUES(name_ta),
            is_active = 1
        ");
        $stmt->execute([$id, $name, $name, $name]);
        echo "<p>✅ Updated: ID $id - $name</p>";
    }

    // Deactivate any categories beyond our range
    $stmt = $pdo->prepare("UPDATE service_categories SET is_active = 0 WHERE id > 10");
    $stmt->execute();
    
    echo "<h3>Step 2: Updating Contractor Service Types</h3>";
    
    // Create mapping from old service names to new IDs
    $service_name_to_id = [
        'House Construction' => 1,
        'Building Renovation' => 2,
        'Commercial Construction' => 3,
        'Interior Design & Finishing' => 4,
        'Roofing & Waterproofing' => 5,
        'Electrical Work' => 6,
        'Plumbing & Sanitation' => 7,
        'Landscaping & Gardening' => 8,
        'Swimming Pool Construction' => 9,
        'Road & Infrastructure' => 10,
        // Handle variations
        'Renovation' => 2,
        'Interior Design' => 4,
        'Roofing' => 5,
        'Electrical' => 6,
        'Plumbing' => 7,
        'Landscaping' => 8,
        'Swimming Pool' => 9,
        'Infrastructure' => 10
    ];
    
    // Get all contractors
    $stmt = $pdo->prepare("SELECT user_id, service_types FROM contractor_profiles");
    $stmt->execute();
    $contractors = $stmt->fetchAll();
    
    foreach ($contractors as $contractor) {
        $old_services = json_decode($contractor['service_types'], true);
        $new_service_ids = [];
        
        if (is_array($old_services)) {
            foreach ($old_services as $service) {
                // If it's already an ID, keep it
                if (is_numeric($service) && isset($new_categories[(int)$service])) {
                    $new_service_ids[] = (int)$service;
                }
                // If it's a service name, convert to ID
                elseif (is_string($service) && isset($service_name_to_id[$service])) {
                    $new_service_ids[] = $service_name_to_id[$service];
                }
                // Try partial matching for service names
                elseif (is_string($service)) {
                    foreach ($service_name_to_id as $name => $id) {
                        if (stripos($service, $name) !== false || stripos($name, $service) !== false) {
                            $new_service_ids[] = $id;
                            break;
                        }
                    }
                }
            }
        }
        
        // Remove duplicates and ensure we have at least one service
        $new_service_ids = array_unique($new_service_ids);
        if (empty($new_service_ids)) {
            $new_service_ids = [1]; // Default to House Construction
        }
        
        // Update contractor
        $stmt = $pdo->prepare("UPDATE contractor_profiles SET service_types = ? WHERE user_id = ?");
        $stmt->execute([json_encode($new_service_ids), $contractor['user_id']]);
        
        echo "<p>Updated contractor {$contractor['user_id']}: " . implode(', ', $new_service_ids) . "</p>";
    }
    
    echo "<h3>Step 3: Verification</h3>";
    
    // Verify the updates
    $stmt = $pdo->prepare("
        SELECT cp.business_name, cp.service_types, cp.service_areas 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        LIMIT 5
    ");
    $stmt->execute();
    $updated_contractors = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Business Name</th><th>Service IDs</th><th>Service Names</th></tr>";
    
    foreach ($updated_contractors as $contractor) {
        $service_ids = json_decode($contractor['service_types'], true);
        $service_names = [];
        
        if (is_array($service_ids)) {
            foreach ($service_ids as $id) {
                if (isset($new_categories[$id])) {
                    $service_names[] = $new_categories[$id];
                }
            }
        }
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
        echo "<td>" . implode(', ', $service_ids ?: []) . "</td>";
        echo "<td>" . implode(', ', $service_names) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>✅ Service Categories and Contractor Data Fixed!</h3>";
    echo "<p>Now the search should work properly with the correct service IDs.</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
