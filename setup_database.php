<?php
/**
 * Database Setup Script for Brick & Click
 * Run this script once to set up the complete database structure
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'brick_click';

try {
    // First connect without database to create it
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to MySQL server successfully.\n";
    
    // Read and execute the SQL file
    $sql = file_get_contents('config/create_database.sql');
    
    if ($sql === false) {
        throw new Exception("Could not read SQL file");
    }
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($statement) {
            return !empty($statement) && !preg_match('/^\s*--/', $statement);
        }
    );
    
    echo "Executing " . count($statements) . " SQL statements...\n";
    
    foreach ($statements as $statement) {
        if (!empty(trim($statement))) {
            try {
                $pdo->exec($statement);
                echo ".";
            } catch (PDOException $e) {
                echo "\nError executing statement: " . substr($statement, 0, 50) . "...\n";
                echo "Error: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n\nDatabase setup completed successfully!\n";
    echo "Database: $dbname\n";
    echo "Default admin login: <EMAIL> / admin123\n";
    echo "\nYou can now run the application.\n";
    
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "Setup failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>

<!-- HTML Interface for Web-based Setup -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brick & Click - Database Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #212529 0%, #34495e 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo h1 {
            color: #fd7e14;
            font-size: 2.5rem;
            margin: 0;
        }
        .setup-form {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
        }
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #fd7e14;
        }
        .btn {
            background: linear-gradient(135deg, #fd7e14, #ff9500);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(253, 126, 20, 0.3);
        }
        .status {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .status.success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
        }
        .status.error {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        .feature {
            background: rgba(255, 255, 255, 0.05);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
        }
        .feature-icon {
            font-size: 2rem;
            color: #fd7e14;
            margin-bottom: 1rem;
        }
        .database-info {
            background: rgba(255, 255, 255, 0.05);
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 2rem;
        }
        .database-info h3 {
            color: #fd7e14;
            margin-top: 0;
        }
        .table-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
            margin-top: 1rem;
        }
        .table-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem;
            border-radius: 5px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>🧱 Brick & Click</h1>
            <p>Database Setup & Configuration</p>
        </div>

        <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
            <?php
            // Process form submission
            $db_host = $_POST['db_host'] ?? 'localhost';
            $db_user = $_POST['db_user'] ?? 'root';
            $db_pass = $_POST['db_pass'] ?? '';
            $db_name = $_POST['db_name'] ?? 'brick_click';
            
            try {
                $setup_pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
                $setup_pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Update database config file
                $config_content = "<?php\n";
                $config_content .= "// Database configuration\n";
                $config_content .= "\$host = '$db_host';\n";
                $config_content .= "\$dbname = '$db_name';\n";
                $config_content .= "\$username = '$db_user';\n";
                $config_content .= "\$password = '$db_pass';\n\n";
                $config_content .= "try {\n";
                $config_content .= "    \$pdo = new PDO(\"mysql:host=\$host;dbname=\$dbname;charset=utf8mb4\", \$username, \$password);\n";
                $config_content .= "    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);\n";
                $config_content .= "    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);\n";
                $config_content .= "} catch(PDOException \$e) {\n";
                $config_content .= "    die(\"Connection failed: \" . \$e->getMessage());\n";
                $config_content .= "}\n";
                $config_content .= "?>";
                
                file_put_contents('config/database.php', $config_content);
                
                // Execute SQL
                $sql = file_get_contents('config/create_database.sql');
                $statements = array_filter(
                    array_map('trim', explode(';', $sql)),
                    function($statement) {
                        return !empty($statement) && !preg_match('/^\s*--/', $statement);
                    }
                );
                
                foreach ($statements as $statement) {
                    if (!empty(trim($statement))) {
                        $setup_pdo->exec($statement);
                    }
                }
                
                echo '<div class="status success">';
                echo '<h3>✅ Setup Completed Successfully!</h3>';
                echo '<p>Database has been created and configured.</p>';
                echo '<p><strong>Database:</strong> ' . htmlspecialchars($db_name) . '</p>';
                echo '<p><strong>Admin Login:</strong> <EMAIL> / admin123</p>';
                echo '<p><a href="index.php" class="btn">Go to Website</a></p>';
                echo '</div>';
                
            } catch (Exception $e) {
                echo '<div class="status error">';
                echo '<h3>❌ Setup Failed</h3>';
                echo '<p>Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
                echo '</div>';
            }
            ?>
        <?php endif; ?>

        <form method="POST" class="setup-form">
            <h2>Database Configuration</h2>
            
            <div class="form-group">
                <label for="db_host">Database Host:</label>
                <input type="text" id="db_host" name="db_host" value="localhost" required>
            </div>
            
            <div class="form-group">
                <label for="db_user">Database Username:</label>
                <input type="text" id="db_user" name="db_user" value="root" required>
            </div>
            
            <div class="form-group">
                <label for="db_pass">Database Password:</label>
                <input type="password" id="db_pass" name="db_pass" value="">
            </div>
            
            <div class="form-group">
                <label for="db_name">Database Name:</label>
                <input type="text" id="db_name" name="db_name" value="brick_click" required>
            </div>
            
            <button type="submit" class="btn">Setup Database</button>
        </form>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">👥</div>
                <h4>User Management</h4>
                <p>Customers, contractors, and admin users with role-based access</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🔍</div>
                <h4>Smart Search</h4>
                <p>Advanced filtering by location, services, ratings, and CIDA grades</p>
            </div>
            <div class="feature">
                <div class="feature-icon">💬</div>
                <h4>Quote System</h4>
                <p>Request and manage quotes with contractor responses</p>
            </div>
            <div class="feature">
                <div class="feature-icon">⭐</div>
                <h4>Reviews & Ratings</h4>
                <p>Verified customer reviews and contractor ratings</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🌐</div>
                <h4>Multilingual</h4>
                <p>Support for English, Sinhala, and Tamil languages</p>
            </div>
            <div class="feature">
                <div class="feature-icon">💳</div>
                <h4>Payment System</h4>
                <p>Down payments and project milestone tracking</p>
            </div>
        </div>

        <div class="database-info">
            <h3>Database Schema Overview</h3>
            <p>The system includes the following main tables:</p>
            <div class="table-list">
                <div class="table-item">👤 users</div>
                <div class="table-item">👨‍💼 customer_profiles</div>
                <div class="table-item">🏗️ contractor_profiles</div>
                <div class="table-item">🛠️ service_categories</div>
                <div class="table-item">📝 quote_requests</div>
                <div class="table-item">💬 quote_responses</div>
                <div class="table-item">❤️ customer_favorites</div>
                <div class="table-item">⭐ reviews</div>
                <div class="table-item">📸 contractor_portfolios</div>
                <div class="table-item">🔔 notifications</div>
                <div class="table-item">💳 project_payments</div>
                <div class="table-item">💰 cost_estimator_data</div>
                <div class="table-item">🌐 translations</div>
                <div class="table-item">🔐 user_sessions</div>
                <div class="table-item">📊 admin_logs</div>
                <div class="table-item">📈 system_reports</div>
                <div class="table-item">⚙️ admin_settings</div>
            </div>
        </div>
    </div>
</body>
</html>
