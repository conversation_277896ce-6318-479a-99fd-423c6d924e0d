<?php
require_once 'config/database.php';

echo "<h2>🔍 Quote System Schema Check</h2>";

try {
    // Check quote_requests table structure
    echo "<h3>1. Quote Requests Table Structure</h3>";
    
    $stmt = $pdo->query("DESCRIBE quote_requests");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $has_specific_contractor_id = false;
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'specific_contractor_id') {
            $has_specific_contractor_id = true;
        }
    }
    echo "</table>";
    
    if ($has_specific_contractor_id) {
        echo "<p style='color: green;'>✅ specific_contractor_id column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ specific_contractor_id column is MISSING!</p>";
        echo "<p>Adding the missing column...</p>";
        
        try {
            $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL");
            echo "<p style='color: green;'>✅ Added specific_contractor_id column</p>";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ Error adding column: " . $e->getMessage() . "</p>";
        }
    }
    
    // Check if we have any data
    echo "<h3>2. Data Check</h3>";
    
    // Check users
    $stmt = $pdo->query("SELECT user_type, status, COUNT(*) as count FROM users GROUP BY user_type, status");
    $user_stats = $stmt->fetchAll();
    
    echo "<h4>Users:</h4>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Type</th><th>Status</th><th>Count</th></tr>";
    foreach ($user_stats as $stat) {
        echo "<tr>";
        echo "<td>" . $stat['user_type'] . "</td>";
        echo "<td>" . $stat['status'] . "</td>";
        echo "<td>" . $stat['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check quote requests
    $stmt = $pdo->query("SELECT COUNT(*) FROM quote_requests");
    $quote_count = $stmt->fetchColumn();
    echo "<p>Total quote requests: $quote_count</p>";
    
    if ($quote_count == 0) {
        echo "<p style='color: red;'>❌ No quote requests found!</p>";
        echo "<p>This explains why contractors don't see any quotes.</p>";
        
        // Check if we have customers to create test data
        $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'customer' AND status = 'approved'");
        $customer_count = $stmt->fetchColumn();
        
        if ($customer_count == 0) {
            echo "<p style='color: red;'>❌ No approved customers found either!</p>";
            echo "<p>Creating test customer and quote request...</p>";
            
            // Create test customer
            $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, 'customer', 'approved')");
            $stmt->execute(['<EMAIL>', password_hash('password', PASSWORD_DEFAULT)]);
            $customer_id = $pdo->lastInsertId();
            
            // Create customer profile
            $stmt = $pdo->prepare("
                INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district, address) 
                VALUES (?, 'Test', 'Customer', '+94 77 123 4567', 'Colombo', 'Test Address, Colombo')
            ");
            $stmt->execute([$customer_id]);
            
            echo "<p style='color: green;'>✅ Created test customer</p>";
        } else {
            // Get existing customer
            $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' AND status = 'approved' LIMIT 1");
            $customer_id = $stmt->fetchColumn();
        }
        
        // Create test quote request
        $stmt = $pdo->prepare("
            INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'open')
        ");
        $stmt->execute([
            $customer_id,
            1, // House Construction
            'Test Quote Request - ' . date('Y-m-d H:i:s'),
            'This is a test quote request to verify the system is working.',
            'Test Location, Colombo',
            'Colombo',
            5000000,
            '6 months'
        ]);
        
        echo "<p style='color: green;'>✅ Created test quote request</p>";
        
        // Update count
        $stmt = $pdo->query("SELECT COUNT(*) FROM quote_requests");
        $quote_count = $stmt->fetchColumn();
        echo "<p>Updated quote requests count: $quote_count</p>";
    }
    
    // Check contractors
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'contractor' AND status = 'approved'");
    $contractor_count = $stmt->fetchColumn();
    echo "<p>Approved contractors: $contractor_count</p>";
    
    if ($contractor_count == 0) {
        echo "<p style='color: red;'>❌ No approved contractors found!</p>";
        echo "<p>Creating test contractor...</p>";
        
        // Create test contractor
        $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, 'contractor', 'approved')");
        $stmt->execute(['<EMAIL>', password_hash('password', PASSWORD_DEFAULT)]);
        $contractor_id = $pdo->lastInsertId();
        
        // Create contractor profile
        $stmt = $pdo->prepare("
            INSERT INTO contractor_profiles (
                user_id, business_name, contact_person, phone, business_address,
                service_areas, service_types, cida_registration, cida_grade,
                business_description, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        $stmt->execute([
            $contractor_id,
            'Test Construction Company',
            'Test Contractor',
            '+94 77 123 4567',
            'Test Address, Colombo',
            json_encode(['Colombo', 'Gampaha', 'Kalutara']),
            json_encode([1, 2, 3]), // House Construction, Renovation, Commercial
            'CIDA/TEST/2024/001',
            'C5',
            'Test contractor for debugging purposes'
        ]);
        
        echo "<p style='color: green;'>✅ Created test contractor</p>";
        echo "<p><strong>Login:</strong> <EMAIL> / password</p>";
    }
    
    // Check service categories
    $stmt = $pdo->query("SELECT COUNT(*) FROM service_categories");
    $category_count = $stmt->fetchColumn();
    echo "<p>Service categories: $category_count</p>";
    
    if ($category_count == 0) {
        echo "<p style='color: red;'>❌ No service categories found!</p>";
        echo "<p>Creating default service categories...</p>";
        
        $categories = [
            ['House Construction', 'නිවාස ඉදිකිරීම්', 'வீட்டு கட்டுமானம்'],
            ['Building Renovation', 'ගොඩනැගිලි අලුත්වැඩියා', 'கட்டிட புதுப்பித்தல்'],
            ['Commercial Construction', 'වාණිජ ඉදිකිරීම්', 'வணிக கட்டுமானம்']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO service_categories (name_en, name_si, name_ta) VALUES (?, ?, ?)");
        foreach ($categories as $category) {
            $stmt->execute($category);
        }
        
        echo "<p style='color: green;'>✅ Created " . count($categories) . " service categories</p>";
    }
    
    // Final test - try the contractor query
    echo "<h3>3. Test Contractor Query</h3>";
    
    $stmt = $pdo->query("
        SELECT u.id, cp.business_name 
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved' 
        LIMIT 1
    ");
    $contractor = $stmt->fetch();
    
    if ($contractor) {
        echo "<p>Testing with contractor: " . htmlspecialchars($contractor['business_name']) . "</p>";
        
        // Test the quotes query
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, sc.name_en as service_category,
                   cp_contractor.service_types, cp_contractor.service_areas
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
            JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
            WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
            AND qr.status = 'open'
        ");
        
        $contractor_id = $contractor['id'];
        $stmt->execute([$contractor_id, $contractor_id]);
        $quotes = $stmt->fetchAll();
        
        echo "<p>Quotes found for contractor: " . count($quotes) . "</p>";
        
        if (count($quotes) > 0) {
            echo "<p style='color: green;'>✅ Contractor should be able to see quotes!</p>";
            foreach ($quotes as $quote) {
                echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0;'>";
                echo "<strong>Title:</strong> " . htmlspecialchars($quote['title']) . "<br>";
                echo "<strong>Service:</strong> " . htmlspecialchars($quote['service_category'] ?? 'Unknown') . "<br>";
                echo "<strong>District:</strong> " . $quote['district'] . "<br>";
                echo "</div>";
            }
        } else {
            echo "<p style='color: red;'>❌ No quotes found for contractor</p>";
            echo "<p>This could be due to service/area mismatch or missing data</p>";
        }
    }
    
    echo "<h3>✅ Schema Check Complete</h3>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>Quote requests: $quote_count</li>";
    echo "<li>Approved contractors: $contractor_count</li>";
    echo "<li>Service categories: $category_count</li>";
    echo "</ul>";
    
    if ($quote_count > 0 && $contractor_count > 0 && $category_count > 0) {
        echo "<p style='color: green;'>🎉 Basic data exists. Contractors should be able to see quotes!</p>";
        echo "<p><strong>Next steps:</strong></p>";
        echo "<ul>";
        echo "<li><a href='contractor/login.php'>Login as contractor</a> (<EMAIL> / password)</li>";
        echo "<li><a href='contractor/quotes.php'>Check contractor quotes page</a></li>";
        echo "<li><a href='test_contractor_access.php'>Run contractor access test</a></li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ Missing basic data. Please run the debug scripts to create test data.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
