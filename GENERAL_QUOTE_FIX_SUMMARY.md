# General Quote Request System Fix

## Problem Identified
The general quote request feature was not working properly - contractors matching the criteria were not receiving quote requests even though the system showed "contractors successfully notified".

## Root Causes Found

### 1. Database Structure Issue
- Missing `specific_contractor_id` column in `quote_requests` table
- This column is essential to distinguish between direct quotes (sent to specific contractors) and general quotes (sent to all matching contractors)

### 2. Query Logic Issues
- The contractor matching query in `customer/process_quote_request.php` was not robust enough
- JSON_CONTAINS function was failing in some cases due to data format inconsistencies
- No fallback mechanism for different JSON formats

### 3. Contractor Display Logic Issues
- The contractor quotes page (`contractor/quotes.php`) had similar JSON matching issues
- Service type matching was not handling both integer and string formats properly

### 4. Data Format Inconsistencies
- Some contractors had service types stored as strings instead of integers
- Some contractors had empty or invalid JSON arrays for service areas/types

## Fixes Implemented

### 1. Database Structure Fix
**File: `fix_general_quotes.php`**
- Added missing `specific_contractor_id` column to `quote_requests` table
- Added proper indexing and foreign key constraints
- Ensured service categories are properly set up

### 2. Enhanced Contractor Matching Logic
**File: `customer/process_quote_request.php`**
- Improved the contractor matching query with fallback mechanisms
- Added both `JSON_CONTAINS` and `LIKE` pattern matching
- Added PHP-level filtering to ensure accurate matching
- Enhanced error handling and debugging capabilities

**Changes made:**
```php
// Before: Simple JSON_CONTAINS query
AND JSON_CONTAINS(cp.service_areas, ?)
AND JSON_CONTAINS(cp.service_types, ?)

// After: Robust query with fallbacks
AND (
    JSON_CONTAINS(cp.service_areas, ?) 
    OR cp.service_areas LIKE ?
)
AND (
    JSON_CONTAINS(cp.service_types, ?)
    OR cp.service_types LIKE ?
)
```

### 3. Fixed Contractor Quote Display
**File: `contractor/quotes.php`**
- Enhanced the filtering logic for general quotes
- Added support for both integer and string service type formats
- Improved the matching algorithm for better accuracy

**Changes made:**
```php
// Before: Basic integer matching
$has_service = in_array((int)$quote['service_category_id'], $contractor_services);

// After: Flexible format matching
$has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
              in_array((string)$quote['service_category_id'], $contractor_services);
```

### 4. Data Cleanup and Validation
**File: `fix_general_quotes.php`**
- Automatic cleanup of contractor profile data
- Conversion of service types to proper integer format
- Addition of default service areas for contractors with missing data
- Validation and fixing of JSON format issues

## Testing Tools Created

### 1. `debug_quote_issue.php`
- Comprehensive debugging tool to analyze the quote request system
- Shows contractor data format and matching logic
- Helps identify data inconsistencies

### 2. `fix_general_quotes.php`
- Complete fix script that addresses all identified issues
- Includes database structure fixes, data cleanup, and validation
- Provides detailed feedback on what was fixed

### 3. `test_general_quotes.php`
- End-to-end testing tool for the general quote system
- Creates test data and simulates the entire quote request flow
- Validates that contractors receive and can view quote requests

## How to Apply the Fix

### Step 1: Run the Fix Script
1. Open your browser and navigate to: `http://localhost/Brick2/fix_general_quotes.php`
2. This will automatically:
   - Add missing database columns
   - Fix contractor data format issues
   - Validate the system is working

### Step 2: Test the System
1. Run the test script: `http://localhost/Brick2/test_general_quotes.php`
2. This will create a test quote request and verify contractors receive it

### Step 3: Manual Testing
1. Log in as a customer
2. Go to "Request Quote" and create a general quote request (don't select a specific contractor)
3. Log in as a contractor who matches the service type and area
4. Check the "Quotes" section to see if the quote request appears
5. Verify notifications are created

## Expected Results After Fix

### For Customers:
- General quote requests will successfully notify all matching contractors
- Success message will show accurate count of notified contractors
- Quote requests will appear in customer's quote history

### For Contractors:
- General quote requests matching their service types and areas will appear in their quotes list
- Notifications will be created when new general quotes are submitted
- Contractors can respond to general quotes normally

### System Behavior:
- Direct quotes (to specific contractors) continue to work as before
- General quotes now properly match contractors based on:
  - Service type (what services they provide)
  - Service area (which districts they serve)
  - Contractor status (must be approved)

## Verification Checklist

- [ ] `specific_contractor_id` column exists in `quote_requests` table
- [ ] Service categories are properly set up with correct IDs
- [ ] Contractors have valid service_areas and service_types JSON data
- [ ] General quote requests create notifications for matching contractors
- [ ] Contractors can see general quote requests in their quotes list
- [ ] Contractors can respond to general quote requests
- [ ] Customer receives responses from contractors

## Files Modified

1. `customer/process_quote_request.php` - Enhanced contractor matching logic
2. `contractor/quotes.php` - Fixed quote display filtering
3. `fix_general_quotes.php` - Complete fix and validation script (new)
4. `test_general_quotes.php` - Testing and validation tool (new)
5. `debug_quote_issue.php` - Debugging tool (new)

## Database Changes

```sql
-- Added missing column
ALTER TABLE quote_requests 
ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline,
ADD INDEX idx_specific_contractor_id (specific_contractor_id),
ADD FOREIGN KEY (specific_contractor_id) REFERENCES users(id) ON DELETE SET NULL;
```

The general quote request system should now work correctly, with contractors receiving quote requests that match their service offerings and coverage areas.
