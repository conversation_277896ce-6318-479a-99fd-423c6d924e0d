<?php
require_once 'config/database.php';

echo "<h2>🧪 Test General Quote Request System</h2>";

try {
    // Step 1: Create a test customer if needed
    echo "<h3>Step 1: Setup Test Customer</h3>";
    
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>' AND user_type = 'customer'");
    $stmt->execute();
    $customer = $stmt->fetch();
    
    if (!$customer) {
        echo "<p>Creating test customer...</p>";
        
        // Create customer user
        $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, 'customer', 'approved')");
        $stmt->execute(['<EMAIL>', password_hash('password', PASSWORD_DEFAULT)]);
        $customer_id = $pdo->lastInsertId();
        
        // Create customer profile
        $stmt = $pdo->prepare("
            INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district, address) 
            VALUES (?, 'Test', 'Customer', '+94 77 123 4567', 'Colombo', 'Test Address, Colombo')
        ");
        $stmt->execute([$customer_id]);
        
        echo "<p style='color: green;'>✅ Created test customer (ID: $customer_id)</p>";
    } else {
        $customer_id = $customer['id'];
        echo "<p style='color: green;'>✅ Using existing test customer (ID: $customer_id)</p>";
    }
    
    // Step 2: Check contractors
    echo "<h3>Step 2: Check Available Contractors</h3>";
    
    $stmt = $pdo->query("
        SELECT u.id, cp.business_name, cp.service_areas, cp.service_types, u.status
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        LIMIT 5
    ");
    $contractors = $stmt->fetchAll();
    
    echo "<p>Found " . count($contractors) . " approved contractors:</p>";
    echo "<ul>";
    foreach ($contractors as $contractor) {
        $areas = json_decode($contractor['service_areas'], true);
        $types = json_decode($contractor['service_types'], true);
        
        echo "<li>";
        echo "<strong>" . htmlspecialchars($contractor['business_name']) . "</strong> (ID: " . $contractor['id'] . ")<br>";
        echo "Areas: " . (is_array($areas) ? implode(', ', $areas) : 'None') . "<br>";
        echo "Services: " . (is_array($types) ? implode(', ', $types) : 'None');
        echo "</li>";
    }
    echo "</ul>";
    
    // Step 3: Create a test general quote request
    echo "<h3>Step 3: Create Test General Quote Request</h3>";
    
    $test_service_id = 1; // House Construction
    $test_district = 'Colombo';
    $test_title = 'Test General Quote Request - ' . date('Y-m-d H:i:s');
    $test_description = 'This is a test general quote request to verify the system is working properly.';
    
    echo "<p>Creating quote request:</p>";
    echo "<ul>";
    echo "<li>Service: House Construction (ID: $test_service_id)</li>";
    echo "<li>District: $test_district</li>";
    echo "<li>Title: $test_title</li>";
    echo "</ul>";
    
    // Insert the quote request
    $stmt = $pdo->prepare("
        INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, specific_contractor_id, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NULL, 'open')
    ");
    $stmt->execute([
        $customer_id,
        $test_service_id,
        $test_title,
        $test_description,
        'Test Location, Colombo',
        $test_district,
        5000000,
        '6 months'
    ]);
    $quote_request_id = $pdo->lastInsertId();
    
    echo "<p style='color: green;'>✅ Created quote request (ID: $quote_request_id)</p>";
    
    // Step 4: Test the contractor matching logic
    echo "<h3>Step 4: Test Contractor Matching</h3>";
    
    // Use the same logic as in process_quote_request.php
    $stmt = $pdo->prepare("
        SELECT u.id, cp.business_name, cp.contact_person, cp.service_areas, cp.service_types
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.status = 'approved'
        AND u.user_type = 'contractor'
        AND cp.service_areas IS NOT NULL 
        AND cp.service_types IS NOT NULL
        AND (
            JSON_CONTAINS(cp.service_areas, ?) 
            OR cp.service_areas LIKE ?
        )
        AND (
            JSON_CONTAINS(cp.service_types, ?)
            OR cp.service_types LIKE ?
        )
    ");

    $district_json = json_encode($test_district);
    $service_json = json_encode($test_service_id);
    $district_like = "%\"$test_district\"%";
    $service_like = "%\"$test_service_id\"%";

    $stmt->execute([
        $district_json,
        $district_like,
        $service_json,
        $service_like
    ]);
    
    $potential_contractors = $stmt->fetchAll();
    
    echo "<p>SQL query found " . count($potential_contractors) . " potential contractors</p>";
    
    // Filter results in PHP (same as process_quote_request.php)
    $contractors_to_notify = [];
    foreach ($potential_contractors as $contractor) {
        $service_areas = json_decode($contractor['service_areas'], true);
        $service_types = json_decode($contractor['service_types'], true);
        
        $has_area = false;
        $has_service = false;
        
        // Check service areas
        if (is_array($service_areas)) {
            $has_area = in_array($test_district, $service_areas);
        }
        
        // Check service types (handle both string and integer formats)
        if (is_array($service_types)) {
            $has_service = in_array($test_service_id, $service_types) || 
                          in_array((string)$test_service_id, $service_types);
        }
        
        if ($has_area && $has_service) {
            $contractors_to_notify[] = [
                'id' => $contractor['id'],
                'business_name' => $contractor['business_name'],
                'contact_person' => $contractor['contact_person']
            ];
        }
        
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
        echo "<strong>" . htmlspecialchars($contractor['business_name']) . "</strong><br>";
        echo "Areas: " . htmlspecialchars($contractor['service_areas']) . "<br>";
        echo "Types: " . htmlspecialchars($contractor['service_types']) . "<br>";
        echo "Has Area ($test_district): " . ($has_area ? '✅ Yes' : '❌ No') . "<br>";
        echo "Has Service ($test_service_id): " . ($has_service ? '✅ Yes' : '❌ No') . "<br>";
        echo "Match: " . (($has_area && $has_service) ? '✅ YES' : '❌ NO') . "<br>";
        echo "</div>";
    }
    
    echo "<p><strong>Final Result: " . count($contractors_to_notify) . " contractors will be notified</strong></p>";
    
    // Step 5: Create notifications for matching contractors
    echo "<h3>Step 5: Create Notifications</h3>";
    
    foreach ($contractors_to_notify as $contractor) {
        $notification_title = "New Quote Request";
        $notification_message = "You have received a new quote request for: $test_title in Test Location, $test_district";
        
        $stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, title, message, type, related_id) 
            VALUES (?, ?, ?, 'quote_received', ?)
        ");
        $stmt->execute([$contractor['id'], $notification_title, $notification_message, $quote_request_id]);
        
        echo "<p>✅ Notified: " . htmlspecialchars($contractor['business_name']) . "</p>";
    }
    
    // Step 6: Test contractor view
    echo "<h3>Step 6: Test Contractor Quote View</h3>";
    
    if (!empty($contractors_to_notify)) {
        $test_contractor_id = $contractors_to_notify[0]['id'];
        
        echo "<p>Testing with contractor ID: $test_contractor_id</p>";
        
        // Use the same query as contractor/quotes.php
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
                   (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
                   CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
                   cp_contractor.service_types, cp_contractor.service_areas
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            JOIN service_categories sc ON qr.service_category_id = sc.id
            JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
            WHERE qr.id = ?
        ");
        
        $stmt->execute([$test_contractor_id, $test_contractor_id, $test_contractor_id, $quote_request_id]);
        $quote_for_contractor = $stmt->fetch();
        
        if ($quote_for_contractor) {
            echo "<p style='color: green;'>✅ Contractor can see the quote request</p>";
            echo "<div style='background: #e8f5e8; padding: 10px;'>";
            echo "<strong>Quote Details:</strong><br>";
            echo "Title: " . htmlspecialchars($quote_for_contractor['title']) . "<br>";
            echo "Service: " . htmlspecialchars($quote_for_contractor['service_category']) . "<br>";
            echo "Type: " . $quote_for_contractor['quote_type'] . "<br>";
            echo "Has Responded: " . ($quote_for_contractor['has_responded'] ? 'Yes' : 'No') . "<br>";
            echo "</div>";
        } else {
            echo "<p style='color: red;'>❌ Contractor cannot see the quote request</p>";
        }
    }
    
    echo "<h3>✅ Test Complete!</h3>";
    echo "<p>Summary:</p>";
    echo "<ul>";
    echo "<li>Quote Request ID: $quote_request_id</li>";
    echo "<li>Contractors Notified: " . count($contractors_to_notify) . "</li>";
    echo "<li>System Status: " . (count($contractors_to_notify) > 0 ? '✅ Working' : '❌ Not Working') . "</li>";
    echo "</ul>";
    
    if (count($contractors_to_notify) > 0) {
        echo "<p style='color: green;'><strong>🎉 General Quote Request System is working correctly!</strong></p>";
        echo "<p>Contractors should now be able to see and respond to general quote requests.</p>";
    } else {
        echo "<p style='color: red;'><strong>❌ General Quote Request System needs attention</strong></p>";
        echo "<p>No contractors were matched. Please check contractor data and service categories.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
