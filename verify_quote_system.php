<?php
require_once 'config/database.php';

echo "<h2>Quote System Verification</h2>";

try {
    echo "<h3>1. Database Tables Check</h3>";
    $tables = ['users', 'customer_profiles', 'contractor_profiles', 'service_categories', 'quote_requests', 'quote_responses'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p style='color: green;'>✓ $table: $count records</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ $table: ERROR - " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>2. Quote Request with Responses</h3>";
    $stmt = $pdo->query("
        SELECT qr.id, qr.title, qr.customer_id, 
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id) as response_count
        FROM quote_requests qr 
        ORDER BY qr.created_at DESC 
        LIMIT 5
    ");
    $quotes = $stmt->fetchAll();
    
    if (empty($quotes)) {
        echo "<p style='color: red;'>No quote requests found</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Quote ID</th><th>Title</th><th>Customer ID</th><th>Responses</th><th>Action</th></tr>";
        foreach ($quotes as $quote) {
            $color = $quote['response_count'] > 0 ? 'green' : 'orange';
            echo "<tr>";
            echo "<td>" . $quote['id'] . "</td>";
            echo "<td>" . htmlspecialchars($quote['title']) . "</td>";
            echo "<td>" . $quote['customer_id'] . "</td>";
            echo "<td style='color: $color;'>" . $quote['response_count'] . "</td>";
            echo "<td><a href='test_quote_responses_direct.php?id=" . $quote['id'] . "' target='_blank'>Test</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>3. Contractor Profiles Check</h3>";
    $stmt = $pdo->query("
        SELECT cp.user_id, cp.business_name, cp.contact_person, 
               (SELECT COUNT(*) FROM quote_responses WHERE contractor_id = cp.user_id) as response_count
        FROM contractor_profiles cp
        LIMIT 5
    ");
    $contractors = $stmt->fetchAll();
    
    if (empty($contractors)) {
        echo "<p style='color: red;'>No contractor profiles found</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>User ID</th><th>Business Name</th><th>Contact Person</th><th>Responses Given</th></tr>";
        foreach ($contractors as $contractor) {
            echo "<tr>";
            echo "<td>" . $contractor['user_id'] . "</td>";
            echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
            echo "<td>" . htmlspecialchars($contractor['contact_person']) . "</td>";
            echo "<td>" . $contractor['response_count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>4. Quote Responses Details</h3>";
    $stmt = $pdo->query("
        SELECT qres.*, cp.business_name, qr.title as quote_title
        FROM quote_responses qres
        LEFT JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
        LEFT JOIN quote_requests qr ON qres.quote_request_id = qr.id
        ORDER BY qres.created_at DESC
        LIMIT 5
    ");
    $responses = $stmt->fetchAll();
    
    if (empty($responses)) {
        echo "<p style='color: red;'>No quote responses found</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Response ID</th><th>Quote Title</th><th>Business Name</th><th>Amount</th><th>Status</th></tr>";
        foreach ($responses as $response) {
            echo "<tr>";
            echo "<td>" . $response['id'] . "</td>";
            echo "<td>" . htmlspecialchars($response['quote_title']) . "</td>";
            echo "<td>" . htmlspecialchars($response['business_name'] ?? 'Unknown') . "</td>";
            echo "<td>Rs. " . number_format($response['quoted_amount'], 2) . "</td>";
            echo "<td>" . $response['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>5. Test the Quote Responses Page</h3>";
    if (!empty($quotes)) {
        $test_quote = $quotes[0];
        echo "<p><a href='test_login.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Login and Test</a></p>";
        echo "<p><a href='customer/quote_responses.php?id=" . $test_quote['id'] . "' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Direct Test (Quote ID: " . $test_quote['id'] . ")</a></p>";
    }
    
    echo "<h3>6. System Status</h3>";
    $issues = [];
    
    if (empty($quotes)) {
        $issues[] = "No quote requests found";
    }
    
    if (empty($contractors)) {
        $issues[] = "No contractor profiles found";
    }
    
    if (empty($responses)) {
        $issues[] = "No quote responses found";
    }
    
    // Check for orphaned responses (responses without contractor profiles)
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM quote_responses qres 
        LEFT JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id 
        WHERE cp.user_id IS NULL
    ");
    $orphaned = $stmt->fetchColumn();
    
    if ($orphaned > 0) {
        $issues[] = "$orphaned quote responses have missing contractor profiles";
    }
    
    if (empty($issues)) {
        echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✓ All systems operational!</p>";
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
        echo "<h4 style='color: #856404;'>Issues Found:</h4>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li style='color: #856404;'>$issue</li>";
        }
        echo "</ul>";
        echo "<p><a href='fix_database_issues.php' style='background: #ffc107; color: #212529; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Fix Issues</a></p>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>
