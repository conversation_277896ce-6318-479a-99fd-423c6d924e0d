<?php
require_once 'database.php';

echo "<h2>🔍 Debug Contractor Approval Issue</h2>";

try {
    // Check all contractors in the database
    $stmt = $pdo->query("SELECT id, email, user_type, status, created_at FROM users WHERE user_type = 'contractor' ORDER BY created_at DESC");
    $contractors = $stmt->fetchAll();
    
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📋 All Contractors in Database</h3>";
    
    if (empty($contractors)) {
        echo "<p>❌ No contractors found in database!</p>";
        echo "<p><a href='add_sample_contractor.php'>Add Sample Contractor</a></p>";
    } else {
        echo "<table border='1' style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 10px;'>ID</th>";
        echo "<th style='padding: 10px;'>Email</th>";
        echo "<th style='padding: 10px;'>User Type</th>";
        echo "<th style='padding: 10px;'>Status</th>";
        echo "<th style='padding: 10px;'>Created</th>";
        echo "<th style='padding: 10px;'>Actions</th>";
        echo "</tr>";
        
        foreach ($contractors as $contractor) {
            $status_color = [
                'pending' => 'orange',
                'approved' => 'green', 
                'rejected' => 'red',
                'suspended' => 'gray'
            ][$contractor['status']] ?? 'black';
            
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . $contractor['id'] . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($contractor['email']) . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($contractor['user_type']) . "</td>";
            echo "<td style='padding: 10px; color: $status_color; font-weight: bold;'>" . htmlspecialchars($contractor['status']) . "</td>";
            echo "<td style='padding: 10px;'>" . $contractor['created_at'] . "</td>";
            echo "<td style='padding: 10px;'>";
            
            if ($contractor['status'] === 'pending') {
                echo "<form method='POST' style='display: inline;'>";
                echo "<input type='hidden' name='contractor_id' value='" . $contractor['id'] . "'>";
                echo "<button type='submit' name='approve_contractor' style='background: green; color: white; padding: 5px 10px; border: none; border-radius: 3px; margin: 2px;'>Approve</button>";
                echo "</form>";
                echo "<form method='POST' style='display: inline;'>";
                echo "<input type='hidden' name='contractor_id' value='" . $contractor['id'] . "'>";
                echo "<button type='submit' name='reject_contractor' style='background: red; color: white; padding: 5px 10px; border: none; border-radius: 3px; margin: 2px;'>Reject</button>";
                echo "</form>";
            } elseif ($contractor['status'] === 'approved') {
                echo "<form method='POST' style='display: inline;'>";
                echo "<input type='hidden' name='contractor_id' value='" . $contractor['id'] . "'>";
                echo "<button type='submit' name='suspend_contractor' style='background: gray; color: white; padding: 5px 10px; border: none; border-radius: 3px; margin: 2px;'>Suspend</button>";
                echo "</form>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
    // Handle form submissions for testing
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3>🔧 Processing Request</h3>";
        
        if (isset($_POST['approve_contractor'])) {
            $contractor_id = (int)$_POST['contractor_id'];
            echo "<p><strong>Action:</strong> Approve Contractor</p>";
            echo "<p><strong>Contractor ID:</strong> $contractor_id</p>";
            
            try {
                $stmt = $pdo->prepare("UPDATE users SET status = 'approved' WHERE id = ? AND user_type = 'contractor'");
                $result = $stmt->execute([$contractor_id]);
                $affected_rows = $stmt->rowCount();
                
                echo "<p><strong>SQL Result:</strong> " . ($result ? 'Success' : 'Failed') . "</p>";
                echo "<p><strong>Affected Rows:</strong> $affected_rows</p>";
                
                if ($affected_rows > 0) {
                    echo "<p style='color: green;'>✅ Contractor approved successfully!</p>";
                } else {
                    echo "<p style='color: red;'>❌ No contractor found with ID: $contractor_id</p>";
                }
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
            }
            
        } elseif (isset($_POST['reject_contractor'])) {
            $contractor_id = (int)$_POST['contractor_id'];
            echo "<p><strong>Action:</strong> Reject Contractor</p>";
            echo "<p><strong>Contractor ID:</strong> $contractor_id</p>";
            
            try {
                $stmt = $pdo->prepare("UPDATE users SET status = 'rejected' WHERE id = ? AND user_type = 'contractor'");
                $result = $stmt->execute([$contractor_id]);
                $affected_rows = $stmt->rowCount();
                
                echo "<p><strong>SQL Result:</strong> " . ($result ? 'Success' : 'Failed') . "</p>";
                echo "<p><strong>Affected Rows:</strong> $affected_rows</p>";
                
                if ($affected_rows > 0) {
                    echo "<p style='color: green;'>✅ Contractor rejected successfully!</p>";
                } else {
                    echo "<p style='color: red;'>❌ No contractor found with ID: $contractor_id</p>";
                }
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
            }
            
        } elseif (isset($_POST['suspend_contractor'])) {
            $contractor_id = (int)$_POST['contractor_id'];
            echo "<p><strong>Action:</strong> Suspend Contractor</p>";
            echo "<p><strong>Contractor ID:</strong> $contractor_id</p>";
            
            try {
                $stmt = $pdo->prepare("UPDATE users SET status = 'suspended' WHERE id = ? AND user_type = 'contractor'");
                $result = $stmt->execute([$contractor_id]);
                $affected_rows = $stmt->rowCount();
                
                echo "<p><strong>SQL Result:</strong> " . ($result ? 'Success' : 'Failed') . "</p>";
                echo "<p><strong>Affected Rows:</strong> $affected_rows</p>";
                
                if ($affected_rows > 0) {
                    echo "<p style='color: green;'>✅ Contractor suspended successfully!</p>";
                } else {
                    echo "<p style='color: red;'>❌ No contractor found with ID: $contractor_id</p>";
                }
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<p><a href='debug_contractor_approval.php'>Refresh Page</a></p>";
        echo "</div>";
    }
    
    // Check contractor profiles
    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>👤 Contractor Profiles</h3>";
    
    $stmt = $pdo->query("
        SELECT cp.*, u.email, u.status 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        ORDER BY cp.created_at DESC
    ");
    $profiles = $stmt->fetchAll();
    
    if (empty($profiles)) {
        echo "<p>❌ No contractor profiles found!</p>";
    } else {
        foreach ($profiles as $profile) {
            echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h4>" . htmlspecialchars($profile['business_name']) . "</h4>";
            echo "<p><strong>Email:</strong> " . htmlspecialchars($profile['email']) . "</p>";
            echo "<p><strong>User ID:</strong> " . $profile['user_id'] . "</p>";
            echo "<p><strong>Status:</strong> <span style='color: " . 
                 ($profile['status'] === 'approved' ? 'green' : 'orange') . ";'>" . 
                 htmlspecialchars($profile['status']) . "</span></p>";
            echo "<p><strong>Contact:</strong> " . htmlspecialchars($profile['contact_person']) . "</p>";
            echo "<p><strong>CIDA:</strong> " . htmlspecialchars($profile['cida_registration']) . " (Grade: " . htmlspecialchars($profile['cida_grade']) . ")</p>";
            echo "</div>";
        }
    }
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Debug Contractor Approval</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        h2, h3 { color: #333; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🔧 BrickClick Contractor Approval Debug</h1>
    <p>This page helps debug contractor approval issues.</p>
    <p><a href="../admin/contractors.php">← Back to Admin Contractors</a></p>
</body>
</html>
