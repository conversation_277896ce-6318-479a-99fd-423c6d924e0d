<?php
// Include authentication and authorization check
require_once 'includes/auth_check.php';

// Contractor data is now available in $contractor variable

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    $errors = [];
    
    // Validation
    if (empty($current_password)) $errors[] = 'Current password is required.';
    if (empty($new_password)) $errors[] = 'New password is required.';
    if (strlen($new_password) < 6) $errors[] = 'New password must be at least 6 characters long.';
    if ($new_password !== $confirm_password) $errors[] = 'New passwords do not match.';
    
    if (empty($errors)) {
        try {
            // Verify current password
            $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch();
            
            if (!password_verify($current_password, $user['password'])) {
                $errors[] = 'Current password is incorrect.';
            } else {
                // Update password
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$hashed_password, $_SESSION['user_id']]);
                
                $_SESSION['success'] = 'Password updated successfully!';
                header('Location: settings.php');
                exit();
            }
        } catch (PDOException $e) {
            $errors[] = 'Database error. Please try again.';
        }
    }
}

// Handle email change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_email'])) {
    $new_email = trim($_POST['new_email']);
    $password = $_POST['password_confirm'];
    
    $errors = [];
    
    // Validation
    if (empty($new_email)) $errors[] = 'New email is required.';
    if (!filter_var($new_email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Invalid email format.';
    if (empty($password)) $errors[] = 'Password confirmation is required.';
    
    if (empty($errors)) {
        try {
            // Check if email already exists
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$new_email, $_SESSION['user_id']]);
            if ($stmt->fetch()) {
                $errors[] = 'Email address is already in use.';
            } else {
                // Verify password
                $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $user = $stmt->fetch();
                
                if (!password_verify($password, $user['password'])) {
                    $errors[] = 'Password is incorrect.';
                } else {
                    // Update email
                    $stmt = $pdo->prepare("UPDATE users SET email = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$new_email, $_SESSION['user_id']]);
                    
                    $_SESSION['success'] = 'Email updated successfully!';
                    header('Location: settings.php');
                    exit();
                }
            }
        } catch (PDOException $e) {
            $errors[] = 'Database error. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Settings - BrickClick Contractor</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet"> 
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF6B35;
            --accent-yellow: #F7931E;
            --success-green: #28a745;
            --info-blue: #17a2b8;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --dark-gray: #343a40;
        }
        
        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e9ecef 100%);
            font-family: 'Open Sans', sans-serif;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            z-index: 1000;
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            flex-shrink: 0;
        }

        .sidebar-header h3 {
            color: var(--accent-orange);
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }

        .sidebar-header p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }

        .sidebar-nav {
            flex: 1;
            padding: 0.5rem 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .nav-item {
            margin: 0.15rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,107,53,0.1);
            color: var(--accent-orange);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--accent-orange), var(--primary-red));
            color: white;
            box-shadow: 0 3px 10px rgba(255,107,53,0.3);
        }

        .nav-link i {
            margin-right: 0.8rem;
            width: 18px;
            text-align: center;
            font-size: 1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--medium-gray), #5a6268);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .page-title {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 2rem;
        }
        
        .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }
        
        .settings-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .section-title {
            color: var(--primary-dark);
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--accent-orange);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            color: var(--primary-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--accent-orange);
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }
        
        .btn-update {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-update:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, var(--primary-red), #bd2130);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
        
        .required {
            color: var(--primary-red);
        }
        
        .account-info {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .info-item:last-child {
            margin-bottom: 0;
        }
        
        .info-item i {
            color: var(--info-blue);
            width: 20px;
            margin-right: 1rem;
        }
        
        .info-label {
            font-weight: 600;
            color: var(--primary-dark);
            width: 120px;
            flex-shrink: 0;
        }
        
        .info-value {
            color: var(--dark-gray);
        }
        
        .form-text {
            color: var(--medium-gray);
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>BrickClick</h3>
            <p>Contractor Portal</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice-dollar"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="portfolio.php" class="nav-link">
                    <i class="fas fa-images"></i>Portfolio
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews & Ratings
                </a>
            </div>
            <div class="nav-item">
                <a href="profile.php" class="nav-link">
                    <i class="fas fa-user-edit"></i>Profile & Services
                </a>
            </div>
            <div class="nav-item">
                <a href="payment_history.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payment History
                </a>
            </div>
            <div class="nav-item">
                <a href="notifications.php" class="nav-link">
                    <i class="fas fa-bell"></i>Notifications
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link active">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>


    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Account Settings</h1>
            <p class="page-subtitle">Manage your account security and preferences</p>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- Account Information -->
        <div class="account-info">
            <div class="row">
                <div class="col-md-6">
                    <div class="info-item">
                        <i class="fas fa-building"></i>
                        <span class="info-label">Business:</span>
                        <span class="info-value"><?php echo htmlspecialchars($contractor['business_name']); ?></span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-user"></i>
                        <span class="info-label">Contact:</span>
                        <span class="info-value"><?php echo htmlspecialchars($contractor['contact_person']); ?></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-item">
                        <i class="fas fa-envelope"></i>
                        <span class="info-label">Email:</span>
                        <span class="info-value"><?php echo htmlspecialchars($contractor['email']); ?></span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-certificate"></i>
                        <span class="info-label">CIDA Grade:</span>
                        <span class="info-value"><?php echo htmlspecialchars($contractor['cida_grade']); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Change Password -->
        <div class="settings-card">
            <h3 class="section-title">Change Password</h3>
            
            <form method="POST" action="">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Current Password <span class="required">*</span></label>
                            <input type="password" class="form-control" name="current_password" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">New Password <span class="required">*</span></label>
                            <input type="password" class="form-control" name="new_password" required>
                            <div class="form-text">Password must be at least 6 characters long</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Confirm New Password <span class="required">*</span></label>
                            <input type="password" class="form-control" name="confirm_password" required>
                        </div>
                    </div>
                </div>
                
                <button type="submit" name="change_password" class="btn btn-update">
                    <i class="fas fa-key me-2"></i>Update Password
                </button>
            </form>
        </div>

        <!-- Change Email -->
        <div class="settings-card">
            <h3 class="section-title">Change Email Address</h3>
            
            <form method="POST" action="">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Current Email</label>
                            <input type="email" class="form-control" value="<?php echo htmlspecialchars($contractor['email']); ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">New Email Address <span class="required">*</span></label>
                            <input type="email" class="form-control" name="new_email" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Confirm Password <span class="required">*</span></label>
                            <input type="password" class="form-control" name="password_confirm" required>
                            <div class="form-text">Enter your current password to confirm this change</div>
                        </div>
                    </div>
                </div>
                
                <button type="submit" name="change_email" class="btn btn-update">
                    <i class="fas fa-envelope me-2"></i>Update Email
                </button>
            </form>
        </div>

        <!-- Account Actions -->
        <div class="settings-card">
            <h3 class="section-title">Account Actions</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Business Profile</h5>
                    <p class="text-muted">Update your business information, services, and contact details.</p>
                    <a href="profile.php" class="btn btn-update">
                        <i class="fas fa-edit me-2"></i>Edit Profile
                    </a>
                </div>
                <div class="col-md-6">
                    <h5>Portfolio</h5>
                    <p class="text-muted">Manage your project portfolio and showcase your work.</p>
                    <a href="portfolio.php" class="btn btn-update">
                        <i class="fas fa-images me-2"></i>Manage Portfolio
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
