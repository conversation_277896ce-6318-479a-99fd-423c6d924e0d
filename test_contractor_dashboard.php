<?php
session_start();
require_once 'config/database.php';

echo "<h2>🧪 Test Contractor Dashboard Logic</h2>";

// Get the contractor
$stmt = $pdo->prepare("
    SELECT u.id, u.email, u.status, cp.*
    FROM users u
    LEFT JOIN contractor_profiles cp ON u.id = cp.user_id
    WHERE u.email = ? AND u.user_type = 'contractor'
");
$stmt->execute(['<EMAIL>']);
$contractor = $stmt->fetch();

if (!$contractor) {
    echo "<p>❌ Contractor not found!</p>";
    exit;
}

echo "<h3>👷 Contractor Info:</h3>";
echo "<p><strong>ID:</strong> {$contractor['id']}</p>";
echo "<p><strong>Email:</strong> {$contractor['email']}</p>";
echo "<p><strong>Status:</strong> {$contractor['status']}</p>";
echo "<p><strong>Business:</strong> {$contractor['business_name']}</p>";

if ($contractor['status'] !== 'approved') {
    echo "<p>❌ <strong>ISSUE:</strong> Contractor status is '{$contractor['status']}', should be 'approved'</p>";
    exit;
}

$contractor_id = $contractor['id'];
$contractor_services = json_decode($contractor['service_types'], true) ?: [];
$contractor_areas = json_decode($contractor['service_areas'], true) ?: [];

echo "<p><strong>Services:</strong> [" . implode(', ', $contractor_services) . "]</p>";
echo "<p><strong>Areas:</strong> [" . implode(', ', $contractor_areas) . "]</p>";

// Simulate the exact dashboard query
echo "<h3>🔍 Dashboard Query Test:</h3>";

// Recent quote requests - show open quotes for this contractor only
$stmt = $pdo->prepare("
    SELECT qr.*, cp.first_name, cp.last_name, cp.district,
           (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
    FROM quote_requests qr
    JOIN customer_profiles cp ON qr.customer_id = cp.user_id
    WHERE qr.status = 'open'
    AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
    ORDER BY qr.created_at DESC
    LIMIT 10
");
$stmt->execute([$contractor_id, $contractor_id]);
$all_recent_quotes = $stmt->fetchAll();

echo "<p><strong>Raw quotes from database:</strong> " . count($all_recent_quotes) . "</p>";

// Filter quotes in PHP to handle JSON logic properly
$recent_quotes = [];
foreach ($all_recent_quotes as $quote) {
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
    echo "<h4>Quote ID: {$quote['id']} - " . htmlspecialchars($quote['title']) . "</h4>";
    echo "<p><strong>Service ID:</strong> {$quote['service_category_id']}</p>";
    echo "<p><strong>District:</strong> {$quote['district']}</p>";
    echo "<p><strong>Specific Contractor:</strong> " . ($quote['specific_contractor_id'] ?: 'NULL (General)') . "</p>";
    echo "<p><strong>Has Responded:</strong> {$quote['has_responded']}</p>";
    
    // Always show direct quotes for this contractor
    if ($quote['specific_contractor_id'] == $contractor_id) {
        $recent_quotes[] = $quote;
        echo "<p>✅ <strong>INCLUDED:</strong> Direct quote for this contractor</p>";
        echo "</div>";
        continue;
    }

    // For general quotes (no specific contractor), check service and area match
    if ($quote['specific_contractor_id'] === null) {
        // Check if contractor provides this service
        $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                      in_array((string)$quote['service_category_id'], $contractor_services);

        // Check if contractor serves this area
        $has_area = in_array($quote['district'], $contractor_areas);

        echo "<p><strong>Service Match:</strong> " . ($has_service ? '✅ YES' : '❌ NO') . 
             " (Looking for {$quote['service_category_id']} in [" . implode(', ', $contractor_services) . "])</p>";
        echo "<p><strong>Area Match:</strong> " . ($has_area ? '✅ YES' : '❌ NO') . 
             " (Looking for '{$quote['district']}' in [" . implode(', ', $contractor_areas) . "])</p>";

        if ($has_service && $has_area) {
            $recent_quotes[] = $quote;
            echo "<p>✅ <strong>INCLUDED:</strong> General quote with service and area match</p>";
        } else {
            echo "<p>❌ <strong>EXCLUDED:</strong> No service/area match</p>";
        }
    } else {
        echo "<p>❌ <strong>EXCLUDED:</strong> Direct quote for contractor ID {$quote['specific_contractor_id']}</p>";
    }
    
    echo "</div>";

    // Limit to 5 quotes for dashboard
    if (count($recent_quotes) >= 5) {
        break;
    }
}

echo "<h3>📊 Final Dashboard Results:</h3>";
echo "<p><strong>Total quotes to display:</strong> " . count($recent_quotes) . "</p>";

if (count($recent_quotes) > 0) {
    echo "<ul>";
    foreach ($recent_quotes as $quote) {
        echo "<li><strong>ID {$quote['id']}:</strong> " . htmlspecialchars($quote['title']) . " - {$quote['first_name']} {$quote['last_name']} ({$quote['district']})</li>";
    }
    echo "</ul>";
} else {
    echo "<p>❌ <strong>NO QUOTES TO DISPLAY</strong> - This explains the empty dashboard!</p>";
}

// Test pending quotes count
echo "<h3>📋 Pending Quotes Count Test:</h3>";

$stmt = $pdo->prepare("
    SELECT qr.*
    FROM quote_requests qr
    WHERE qr.status = 'open'
    AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
    AND qr.id NOT IN (
        SELECT COALESCE(quote_request_id, 0)
        FROM quote_responses
        WHERE contractor_id = ?
    )
");
$stmt->execute([$contractor_id, $contractor_id]);
$all_pending_quotes = $stmt->fetchAll();

echo "<p><strong>Raw pending quotes:</strong> " . count($all_pending_quotes) . "</p>";

$pending_quotes = [];
foreach ($all_pending_quotes as $quote) {
    // Always include direct quotes for this contractor
    if ($quote['specific_contractor_id'] == $contractor_id) {
        $pending_quotes[] = $quote;
        continue;
    }

    // For general quotes, check service and area match
    if ($quote['specific_contractor_id'] === null) {
        $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                      in_array((string)$quote['service_category_id'], $contractor_services);
        $has_area = in_array($quote['district'], $contractor_areas);

        if ($has_service && $has_area) {
            $pending_quotes[] = $quote;
        }
    }
}

echo "<p><strong>Filtered pending quotes:</strong> " . count($pending_quotes) . "</p>";

// Check if there are any responses by this contractor
echo "<h3>📝 Response History:</h3>";
$stmt = $pdo->prepare("SELECT COUNT(*) FROM quote_responses WHERE contractor_id = ?");
$stmt->execute([$contractor_id]);
$response_count = $stmt->fetchColumn();
echo "<p><strong>Total responses by this contractor:</strong> {$response_count}</p>";
?>
