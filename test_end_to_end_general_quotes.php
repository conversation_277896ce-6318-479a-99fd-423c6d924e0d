<?php
require_once 'config/database.php';

echo "<h2>🚀 End-to-End General Quote System Test</h2>";

try {
    // Step 1: Verify system prerequisites
    echo "<h3>Step 1: System Prerequisites</h3>";
    
    // Check database structure
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    if (!$stmt->fetch()) {
        echo "<p style='color: red;'>❌ Missing specific_contractor_id column</p>";
        exit;
    }
    echo "<p style='color: green;'>✅ Database structure OK</p>";
    
    // Check for test data
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'customer' AND status = 'approved'");
    $customer_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("
        SELECT COUNT(*) FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        AND cp.service_areas IS NOT NULL AND cp.service_areas != ''
        AND cp.service_types IS NOT NULL AND cp.service_types != ''
    ");
    $contractor_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM service_categories");
    $service_count = $stmt->fetchColumn();
    
    echo "<p>Test data: $customer_count customers, $contractor_count contractors, $service_count services</p>";
    
    if ($customer_count == 0 || $contractor_count == 0 || $service_count == 0) {
        echo "<p style='color: red;'>❌ Insufficient test data</p>";
        exit;
    }
    echo "<p style='color: green;'>✅ Test data OK</p>";
    
    // Step 2: Simulate customer submitting general quote
    echo "<h3>Step 2: Customer Submits General Quote</h3>";
    
    // Get test customer
    $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' AND status = 'approved' LIMIT 1");
    $customer_id = $stmt->fetchColumn();
    
    // Get test service
    $stmt = $pdo->query("SELECT id FROM service_categories LIMIT 1");
    $service_category_id = $stmt->fetchColumn();
    
    // Create test quote (simulating customer form submission)
    $test_title = "E2E Test Quote - " . date('Y-m-d H:i:s');
    $test_district = "Colombo";
    $test_description = "End-to-end test of general quote system";
    $test_location = "Test Location, Colombo";
    
    echo "<p>Creating quote: '$test_title' for service $service_category_id in $test_district</p>";
    
    // Simulate the process_quote_request.php logic
    $pdo->beginTransaction();
    
    // Insert quote request
    $stmt = $pdo->prepare("
        INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, specific_contractor_id, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'open')
    ");
    $stmt->execute([$customer_id, $service_category_id, $test_title, $test_description, $test_location, $test_district, 1000000, '3 months', null]);
    $quote_request_id = $pdo->lastInsertId();
    
    echo "<p style='color: green;'>✅ Quote request created (ID: $quote_request_id)</p>";
    
    // Step 3: Find matching contractors (exact logic from process_quote_request.php)
    echo "<h3>Step 3: Find Matching Contractors</h3>";
    
    $stmt = $pdo->prepare("
        SELECT u.id, cp.business_name, cp.contact_person, cp.service_areas, cp.service_types
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.status = 'approved'
        AND u.user_type = 'contractor'
        AND cp.service_areas IS NOT NULL
        AND cp.service_areas != ''
        AND cp.service_types IS NOT NULL
        AND cp.service_types != ''
        AND (
            JSON_CONTAINS(cp.service_areas, ?)
            OR cp.service_areas LIKE ?
        )
        AND (
            JSON_CONTAINS(cp.service_types, ?)
            OR cp.service_types LIKE ?
        )
    ");
    
    $district_json = json_encode($test_district);
    $service_json = json_encode($service_category_id);
    $district_like = "%\"$test_district\"%";
    $service_like = "%\"$service_category_id\"%";
    
    $stmt->execute([$district_json, $district_like, $service_json, $service_like]);
    $potential_contractors = $stmt->fetchAll();
    
    echo "<p>SQL found " . count($potential_contractors) . " potential contractors</p>";
    
    // Filter in PHP (exact logic from process_quote_request.php)
    $contractors_to_notify = [];
    foreach ($potential_contractors as $contractor) {
        $service_areas = json_decode($contractor['service_areas'], true);
        $service_types = json_decode($contractor['service_types'], true);
        
        $has_area = is_array($service_areas) && in_array($test_district, $service_areas);
        $has_service = is_array($service_types) && (
            in_array($service_category_id, $service_types) ||
            in_array((string)$service_category_id, $service_types)
        );
        
        if ($has_area && $has_service) {
            $contractors_to_notify[] = [
                'id' => $contractor['id'],
                'business_name' => $contractor['business_name'],
                'contact_person' => $contractor['contact_person']
            ];
        }
    }
    
    echo "<p style='color: green;'>✅ Found " . count($contractors_to_notify) . " matching contractors</p>";
    
    if (count($contractors_to_notify) == 0) {
        echo "<p style='color: red;'>❌ No contractors match - test cannot continue</p>";
        $pdo->rollBack();
        exit;
    }
    
    // Step 4: Create notifications (exact logic from process_quote_request.php)
    echo "<h3>Step 4: Create Notifications</h3>";
    
    foreach ($contractors_to_notify as $contractor) {
        $notification_title = "New Quote Request";
        $notification_message = "You have received a new quote request for: $test_title in $test_location, $test_district";
        
        $stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, title, message, type, related_id) 
            VALUES (?, ?, ?, 'quote_received', ?)
        ");
        $stmt->execute([$contractor['id'], $notification_title, $notification_message, $quote_request_id]);
        
        echo "<p>✅ Notified: " . htmlspecialchars($contractor['business_name']) . "</p>";
    }
    
    // Create customer notification
    $customer_notification = "Quote request submitted successfully. You will receive notifications when contractors respond.";
    $stmt = $pdo->prepare("
        INSERT INTO notifications (user_id, title, message, type, related_id) 
        VALUES (?, 'Quote Request Submitted', ?, 'general', ?)
    ");
    $stmt->execute([$customer_id, $customer_notification, $quote_request_id]);
    
    $pdo->commit();
    echo "<p style='color: green;'>✅ All notifications created</p>";
    
    // Step 5: Test contractor dashboard display
    echo "<h3>Step 5: Test Contractor Dashboard Display</h3>";
    
    $dashboard_success = 0;
    foreach ($contractors_to_notify as $contractor) {
        // Test exact dashboard query
        $stmt = $pdo->prepare("
            SELECT qr.*, cp_contractor.service_types, cp_contractor.service_areas
            FROM quote_requests qr
            JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
            WHERE qr.status = 'open'
            AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
            AND qr.id NOT IN (
                SELECT COALESCE(quote_request_id, 0)
                FROM quote_responses
                WHERE contractor_id = ?
            )
        ");
        $stmt->execute([$contractor['id'], $contractor['id'], $contractor['id']]);
        $dashboard_quotes = $stmt->fetchAll();
        
        // Apply PHP filtering
        $visible_quotes = 0;
        foreach ($dashboard_quotes as $quote) {
            if ($quote['specific_contractor_id'] == $contractor['id']) {
                $visible_quotes++;
            } elseif ($quote['specific_contractor_id'] === null) {
                $contractor_services = json_decode($quote['service_types'], true) ?: [];
                $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
                
                $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                              in_array((string)$quote['service_category_id'], $contractor_services);
                $has_area = in_array($quote['district'], $contractor_areas);
                
                if ($has_service && $has_area) {
                    $visible_quotes++;
                }
            }
        }
        
        if ($visible_quotes > 0) {
            echo "<p>✅ " . htmlspecialchars($contractor['business_name']) . " will see $visible_quotes quotes on dashboard</p>";
            $dashboard_success++;
        } else {
            echo "<p>❌ " . htmlspecialchars($contractor['business_name']) . " will NOT see quotes on dashboard</p>";
        }
    }
    
    // Step 6: Test contractor quotes page display
    echo "<h3>Step 6: Test Contractor Quotes Page Display</h3>";
    
    $quotes_page_success = 0;
    foreach ($contractors_to_notify as $contractor) {
        // Test exact quotes page query
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
                   (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
                   CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
                   cp_contractor.service_types, cp_contractor.service_areas
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
            JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
            WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
            AND qr.status = 'open'
            AND qr.id NOT IN (
                SELECT COALESCE(quote_request_id, 0)
                FROM quote_responses
                WHERE contractor_id = ?
            )
            ORDER BY qr.created_at DESC
        ");
        $stmt->execute([$contractor['id'], $contractor['id'], $contractor['id'], $contractor['id'], $contractor['id']]);
        $quotes_page_quotes = $stmt->fetchAll();
        
        // Apply PHP filtering
        $visible_quotes = 0;
        foreach ($quotes_page_quotes as $quote) {
            if ($quote['specific_contractor_id'] == $contractor['id']) {
                $visible_quotes++;
            } elseif ($quote['specific_contractor_id'] === null) {
                $contractor_services = json_decode($quote['service_types'], true) ?: [];
                $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
                
                $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                              in_array((string)$quote['service_category_id'], $contractor_services);
                $has_area = in_array($quote['district'], $contractor_areas);
                
                if ($has_service && $has_area) {
                    $visible_quotes++;
                }
            }
        }
        
        if ($visible_quotes > 0) {
            echo "<p>✅ " . htmlspecialchars($contractor['business_name']) . " will see $visible_quotes quotes on quotes page</p>";
            $quotes_page_success++;
        } else {
            echo "<p>❌ " . htmlspecialchars($contractor['business_name']) . " will NOT see quotes on quotes page</p>";
        }
    }
    
    // Final results
    echo "<h3>🎯 Test Results</h3>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>Quote created: ✅ ID $quote_request_id</li>";
    echo "<li>Contractors notified: " . count($contractors_to_notify) . "</li>";
    echo "<li>Dashboard working: $dashboard_success/" . count($contractors_to_notify) . "</li>";
    echo "<li>Quotes page working: $quotes_page_success/" . count($contractors_to_notify) . "</li>";
    echo "</ul>";
    
    if ($dashboard_success == count($contractors_to_notify) && $quotes_page_success == count($contractors_to_notify)) {
        echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 SUCCESS! General quote system is working perfectly!</p>";
        echo "<p>Contractors should now see the quote on both their dashboard and quotes page.</p>";
    } else {
        echo "<p style='color: red; font-weight: bold; font-size: 18px;'>❌ ISSUES DETECTED! Some contractors won't see the quotes.</p>";
        echo "<p>Check contractor service data and matching logic.</p>";
    }
    
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
