<?php
require_once 'config/database.php';

echo "Testing database connection...\n";

try {
    // Test basic connection
    echo "✓ Database connection successful\n";
    
    // Check if project_payments table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'project_payments'");
    if ($stmt->rowCount() > 0) {
        echo "✓ project_payments table exists\n";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE project_payments");
        $columns = $stmt->fetchAll();
        echo "✓ Table structure:\n";
        foreach ($columns as $column) {
            echo "  - {$column['Field']}: {$column['Type']}\n";
        }
    } else {
        echo "✗ project_payments table does not exist\n";
    }
    
    // Check if notifications table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'notifications'");
    if ($stmt->rowCount() > 0) {
        echo "✓ notifications table exists\n";
    } else {
        echo "✗ notifications table does not exist\n";
    }
    
    // Test a simple insert (we'll rollback)
    $pdo->beginTransaction();
    
    $stmt = $pdo->prepare("
        INSERT INTO project_payments (
            quote_response_id, customer_id, contractor_id, amount, 
            payment_type, payment_status, payment_method, transaction_id, 
            payment_date, payment_details
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $test_data = [
        1, // quote_response_id
        1, // customer_id
        1, // contractor_id
        1000.00, // amount
        'down_payment', // payment_type
        'completed', // payment_status
        'card', // payment_method
        'TEST_TXN_123', // transaction_id
        date('Y-m-d H:i:s'), // payment_date
        '{"method":"card","last_four":"1234"}' // payment_details
    ];
    
    $stmt->execute($test_data);
    echo "✓ Test insert successful\n";
    
    $pdo->rollBack();
    echo "✓ Test transaction rolled back\n";
    
} catch (PDOException $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    if (isset($pdo)) {
        $pdo->rollBack();
    }
}
?>
