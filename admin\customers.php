<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

// Handle customer suspend/unsuspend actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['suspend_customer'])) {
        $customer_id = (int)$_POST['customer_id'];

        // Get customer name for confirmation message
        $stmt = $pdo->prepare("SELECT cp.first_name, cp.last_name FROM customer_profiles cp JOIN users u ON cp.user_id = u.id WHERE u.id = ?");
        $stmt->execute([$customer_id]);
        $customer_info = $stmt->fetch();
        $customer_name = $customer_info ? $customer_info['first_name'] . ' ' . $customer_info['last_name'] : 'Customer';

        try {
            $stmt = $pdo->prepare("UPDATE users SET status = 'suspended' WHERE id = ? AND user_type = 'customer'");
            $result = $stmt->execute([$customer_id]);
            $affected_rows = $stmt->rowCount();

            if ($affected_rows > 0) {
                $_SESSION['success'] = $customer_name . ' has been suspended successfully!';
            } else {
                $_SESSION['error'] = 'No customer found with ID: ' . $customer_id;
            }
        } catch (PDOException $e) {
            $_SESSION['error'] = 'Error suspending customer: ' . $e->getMessage();
        }
    } elseif (isset($_POST['unsuspend_customer'])) {
        $customer_id = (int)$_POST['customer_id'];

        // Get customer name for confirmation message
        $stmt = $pdo->prepare("SELECT cp.first_name, cp.last_name FROM customer_profiles cp JOIN users u ON cp.user_id = u.id WHERE u.id = ?");
        $stmt->execute([$customer_id]);
        $customer_info = $stmt->fetch();
        $customer_name = $customer_info ? $customer_info['first_name'] . ' ' . $customer_info['last_name'] : 'Customer';

        try {
            $stmt = $pdo->prepare("UPDATE users SET status = 'approved' WHERE id = ? AND user_type = 'customer'");
            $result = $stmt->execute([$customer_id]);
            $affected_rows = $stmt->rowCount();

            if ($affected_rows > 0) {
                $_SESSION['success'] = $customer_name . ' has been unsuspended successfully!';
            } else {
                $_SESSION['error'] = 'No customer found with ID: ' . $customer_id;
            }
        } catch (PDOException $e) {
            $_SESSION['error'] = 'Error unsuspending customer: ' . $e->getMessage();
        }
    }

    // Redirect to prevent form resubmission
    $redirect_params = [];
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $redirect_params[] = 'search=' . urlencode($_GET['search']);
    }
    if (isset($_GET['status']) && !empty($_GET['status'])) {
        $redirect_params[] = 'status=' . urlencode($_GET['status']);
    }
    $redirect_url = 'customers.php' . (!empty($redirect_params) ? '?' . implode('&', $redirect_params) : '');
    header('Location: ' . $redirect_url);
    exit();
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : 'all';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Build query
$where_conditions = ["u.user_type = 'customer'"];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "u.status = ?";
    $params[] = $status_filter;
}

if (!empty($search)) {
    $where_conditions[] = "(COALESCE(cp.first_name, '') LIKE ? OR COALESCE(cp.last_name, '') LIKE ? OR u.email LIKE ? OR COALESCE(cp.district, '') LIKE ? OR CONCAT(COALESCE(cp.first_name, ''), ' ', COALESCE(cp.last_name, '')) LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}



$where_clause = implode(' AND ', $where_conditions);

// Get customers
try {
    $stmt = $pdo->prepare("
        SELECT u.id, u.email, u.user_type, u.status, u.created_at, u.updated_at,
               cp.first_name, cp.last_name, cp.phone, cp.district, cp.address, cp.profile_image, cp.language_preference,
               (SELECT COUNT(*) FROM quote_requests qr WHERE qr.customer_id = u.id) as quote_count,
               (SELECT COUNT(*) FROM reviews r WHERE r.customer_id = u.id) as review_count,
               (SELECT COUNT(*) FROM customer_favorites cf WHERE cf.customer_id = u.id) as favorite_count
        FROM users u
        LEFT JOIN customer_profiles cp ON u.id = cp.user_id
        WHERE $where_clause
        ORDER BY u.created_at DESC
    ");
    $stmt->execute($params);
    $customers = $stmt->fetchAll();
} catch (PDOException $e) {
    $customers = [];
    $error = 'Database error: ' . $e->getMessage();
}

// Get customer statistics
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE user_type = 'customer'");
    $total_customers = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) as active FROM users WHERE user_type = 'customer' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
    $active_customers = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) as suspended FROM users WHERE user_type = 'customer' AND status = 'suspended'");
    $suspended_customers = $stmt->fetchColumn();
} catch (PDOException $e) {
    $total_customers = 0;
    $active_customers = 0;
    $suspended_customers = 0;
}

// Get status counts for filter tabs
try {
    $stmt = $pdo->query("
        SELECT status, COUNT(*) as count
        FROM users
        WHERE user_type = 'customer'
        GROUP BY status
    ");
    $status_counts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
} catch (PDOException $e) {
    $status_counts = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Customers Management - Admin Panel</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF8C00;
            --accent-yellow: #FFD700;
            --light-gray: #F8F9FA;
            --medium-gray: #6C757D;
            --success-green: #28A745;
            --warning-orange: #FFC107;
            --info-blue: #17A2B8;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f5f6fa;
            color: var(--primary-dark);
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-header h3 {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }
        
        .sidebar-header p {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 15px rgba(197, 23, 46, 0.3);
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--info-blue), #20c997);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(23, 162, 184, 0.3);
        }
        
        .page-title {
            font-weight: 700;
            margin: 0;
            font-size: 2.5rem;
        }
        
        .page-subtitle {
            margin: 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--info-blue);
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: var(--medium-gray);
            font-weight: 500;
        }
        
        .filters-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .status-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .status-tab {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .status-tab.all {
            background: linear-gradient(135deg, var(--medium-gray), #5a6268);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .status-tab.pending {
            background: linear-gradient(135deg, var(--warning-orange), #e0a800);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
        }

        .status-tab.approved {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
        }

        .status-tab.suspended {
            background: linear-gradient(135deg,rgb(175, 7, 15),rgb(160, 10, 93));
            color: white;
            box-shadow: 0 4px 15px rgba(156, 17, 71, 0.3);
        }

        .status-tab.active {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
        }

        .status-tab:hover {
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        
        .customer-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .customer-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .customer-header {
            display: flex;
            justify-content: between;
            align-items: start;
            margin-bottom: 1.5rem;
        }
        
        .customer-info h5 {
            color: var(--primary-dark);
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .customer-info p {
            color: var(--medium-gray);
            margin: 0;
        }
        
        .customer-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            color: var(--medium-gray);
        }
        
        .detail-item i {
            width: 20px;
            margin-right: 0.5rem;
            color: var(--accent-orange);
        }
        
        .activity-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .activity-badge.high {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }
        
        .activity-badge.medium {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
        }
        
        .activity-badge.low {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .empty-state i {
            font-size: 4rem;
            color: var(--medium-gray);
            margin-bottom: 1rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.approved {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }

        .status-badge.pending {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
        }

        .status-badge.suspended {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }

        .status-badge.rejected {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            color: #0c5460;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .btn-suspend {
            background: linear-gradient(135deg, var(--primary-red), #a01426);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-suspend:hover {
            background: linear-gradient(135deg, #a01426, var(--primary-red));
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(197, 23, 46, 0.3);
            color: white;
        }

        .btn-unsuspend {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-unsuspend:hover {
            background: linear-gradient(135deg, #1e7e34, var(--success-green));
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-shield-alt me-2"></i>Admin Panel</h3>
            <p>Brick & Click Management</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="contractors.php" class="nav-link">
                    <i class="fas fa-hard-hat"></i>Contractors
                </a>
            </div>
            <div class="nav-item">
                <a href="customers.php" class="nav-link active">
                    <i class="fas fa-users"></i>Customers
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews
                </a>
            </div>
            <div class="nav-item">
                <a href="payments.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payments
                </a>
            </div>
            <div class="nav-item">
                <a href="reports.php" class="nav-link">
                    <i class="fas fa-chart-bar"></i>Reports
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Customers Management</h1>
            <p class="page-subtitle">Monitor customer activity and engagement</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($total_customers); ?></div>
                <div class="stat-label">Total Customers</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($active_customers); ?></div>
                <div class="stat-label">New This Month</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: var(--primary-red);"><?php echo number_format($suspended_customers); ?></div>
                <div class="stat-label">Suspended Customers</div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <!-- Filters -->
        <div class="filters-card">
            <div class="status-tabs">
                <a href="customers.php" class="status-tab all <?php echo $status_filter === 'all' ? 'active' : ''; ?>">
                    All Customers (<?php echo !empty($status_counts) ? array_sum($status_counts) : 0; ?>)
                </a>
                <a href="customers.php?status=approved" class="status-tab approved <?php echo $status_filter === 'approved' ? 'active' : ''; ?>">
                    Approved (<?php echo $status_counts['approved'] ?? 0; ?>)
                </a>
                <a href="customers.php?status=suspended" class="status-tab suspended <?php echo $status_filter === 'suspended' ? 'active' : ''; ?>">
                    Suspended (<?php echo $status_counts['suspended'] ?? 0; ?>)
                </a>
            </div>

            <form method="GET" class="row g-3">
                <input type="hidden" name="status" value="<?php echo htmlspecialchars($status_filter); ?>">
                <div class="col-md-10">
                    <input type="text" class="form-control" name="search" placeholder="Search by name, email, or district..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                </div>
            </form>
        </div>

        <!-- Customers List -->
        <?php if (empty($customers)): ?>
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <h3>No customers found</h3>
                <p class="text-muted">No customers match your search criteria.</p>
            </div>
        <?php else: ?>
            <?php foreach ($customers as $customer): ?>
                <div class="customer-card">
                    <div class="customer-header">
                        <div class="customer-info">
                            <h5><?php echo htmlspecialchars(($customer['first_name'] ?? '') . ' ' . ($customer['last_name'] ?? '')); ?></h5>
                            <p><?php echo htmlspecialchars($customer['email']); ?></p>
                        </div>
                        <div>
                            <span class="status-badge <?php echo $customer['status']; ?>">
                                <?php echo ucfirst($customer['status']); ?>
                            </span>
                            <?php
                            $activity_level = 'low';
                            if ($customer['quote_count'] > 5) $activity_level = 'high';
                            elseif ($customer['quote_count'] > 2) $activity_level = 'medium';
                            ?>
                            <span class="activity-badge <?php echo $activity_level; ?> ms-2">
                                <?php echo ucfirst($activity_level); ?> Activity
                            </span>
                        </div>
                    </div>

                    <div class="customer-details">
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span><?php echo htmlspecialchars($customer['district'] ?? 'N/A'); ?></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-phone"></i>
                            <span><?php echo htmlspecialchars($customer['phone'] ?? 'N/A'); ?></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-file-invoice"></i>
                            <span><?php echo $customer['quote_count']; ?> quote requests</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-star"></i>
                            <span><?php echo $customer['review_count']; ?> reviews written</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-heart"></i>
                            <span><?php echo $customer['favorite_count']; ?> favorites</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-calendar"></i>
                            <span>Joined <?php echo date('M j, Y', strtotime($customer['created_at'])); ?></span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <?php if ($customer['status'] === 'suspended'): ?>
                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to unsuspend <?php echo htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']); ?>?')">
                                <input type="hidden" name="customer_id" value="<?php echo htmlspecialchars($customer['id']); ?>">
                                <button type="submit" name="unsuspend_customer" class="btn btn-unsuspend">
                                    <i class="fas fa-user-check me-1"></i>Unsuspend Customer
                                </button>
                            </form>
                        <?php elseif ($customer['status'] === 'approved'): ?>
                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to suspend <?php echo htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']); ?>? They will not be able to access their account.')">
                                <input type="hidden" name="customer_id" value="<?php echo htmlspecialchars($customer['id']); ?>">
                                <button type="submit" name="suspend_customer" class="btn btn-suspend">
                                    <i class="fas fa-user-times me-1"></i>Suspend Customer
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
