<?php
// Include authentication and authorization check
require_once 'includes/auth_check.php';

// Contractor data is now available in $contractor variable

// Handle delete portfolio item
if (isset($_POST['delete_portfolio']) && isset($_POST['portfolio_id'])) {
    $portfolio_id = (int)$_POST['portfolio_id'];
    
    try {
        // Get portfolio item to delete images
        $stmt = $pdo->prepare("SELECT project_images FROM contractor_portfolios WHERE id = ? AND contractor_id = ?");
        $stmt->execute([$portfolio_id, $_SESSION['user_id']]);
        $portfolio = $stmt->fetch();
        
        if ($portfolio) {
            // Delete the portfolio item
            $stmt = $pdo->prepare("DELETE FROM contractor_portfolios WHERE id = ? AND contractor_id = ?");
            $stmt->execute([$portfolio_id, $_SESSION['user_id']]);
            
            // TODO: Delete image files from server
            
            $_SESSION['success'] = 'Portfolio item deleted successfully!';
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Error deleting portfolio item.';
    }
    
    header('Location: portfolio.php');
    exit();
}

// Handle toggle featured status
if (isset($_POST['toggle_featured']) && isset($_POST['portfolio_id'])) {
    $portfolio_id = (int)$_POST['portfolio_id'];
    
    try {
        $stmt = $pdo->prepare("UPDATE contractor_portfolios SET is_featured = NOT is_featured WHERE id = ? AND contractor_id = ?");
        $stmt->execute([$portfolio_id, $_SESSION['user_id']]);
        $_SESSION['success'] = 'Portfolio item updated successfully!';
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Error updating portfolio item.';
    }
    
    header('Location: portfolio.php');
    exit();
}

// Get portfolio items
try {
    $stmt = $pdo->prepare("
        SELECT * FROM contractor_portfolios 
        WHERE contractor_id = ? 
        ORDER BY is_featured DESC, completion_date DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $portfolio_items = $stmt->fetchAll();
} catch (PDOException $e) {
    $portfolio_items = [];
}

// Get portfolio statistics
try {
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_projects,
            SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured_projects,
            SUM(project_value) as total_value,
            AVG(project_value) as avg_value
        FROM contractor_portfolios 
        WHERE contractor_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch();
} catch (PDOException $e) {
    $stats = ['total_projects' => 0, 'featured_projects' => 0, 'total_value' => 0, 'avg_value' => 0];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Portfolio - BrickClick Contractor</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet"> 
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF6B35;
            --accent-yellow: #F7931E;
            --success-green: #28a745;
            --info-blue: #17a2b8;
            --warning-orange: #ffc107;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --dark-gray: #343a40;
        }
        
        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e9ecef 100%);
            font-family: 'Open Sans', sans-serif;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            z-index: 1000;
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            flex-shrink: 0;
        }

        .sidebar-header h3 {
            color: var(--accent-orange);
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }

        .sidebar-header p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }

        .sidebar-nav {
            flex: 1;
            padding: 0.5rem 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .nav-item {
            margin: 0.15rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,107,53,0.1);
            color: var(--accent-orange);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--accent-orange), var(--primary-red));
            color: white;
            box-shadow: 0 3px 10px rgba(255,107,53,0.3);
        }

        .nav-link i {
            margin-right: 0.8rem;
            width: 18px;
            text-align: center;
            font-size: 1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--accent-yellow), var(--accent-orange));
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .page-title {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 2rem;
        }
        
        .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: white;
            margin: 0 auto 1rem;
        }
        
        .stat-icon.projects {
            background: linear-gradient(135deg, var(--info-blue), #138496);
        }
        
        .stat-icon.featured {
            background: linear-gradient(135deg, var(--accent-yellow), #e0a800);
        }
        
        .stat-icon.value {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
        }
        
        .stat-icon.average {
            background: linear-gradient(135deg, var(--accent-orange), #e55a2b);
        }
        
        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-dark);
            margin: 0;
        }
        
        .stat-label {
            color: var(--medium-gray);
            font-weight: 600;
            margin: 0;
            font-size: 0.9rem;
        }
        
        .portfolio-header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            color: var(--primary-dark);
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }
        
        .btn-add {
            background: linear-gradient(135deg, var(--accent-orange), #e55a2b);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .btn-add:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
        }
        
        .portfolio-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .portfolio-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .portfolio-image {
            height: 200px;
            background: var(--light-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--medium-gray);
            font-size: 3rem;
            position: relative;
        }
        
        .featured-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: linear-gradient(135deg, var(--accent-yellow), #e0a800);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.8rem;
        }
        
        .portfolio-content {
            padding: 1.5rem;
        }
        
        .portfolio-title {
            color: var(--primary-dark);
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }
        
        .portfolio-location {
            color: var(--medium-gray);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .portfolio-description {
            color: var(--dark-gray);
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .portfolio-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }
        
        .portfolio-value {
            color: var(--accent-orange);
            font-weight: 700;
            font-size: 1.1rem;
        }
        
        .portfolio-date {
            color: var(--medium-gray);
            font-size: 0.9rem;
        }
        
        .portfolio-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-action {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-edit {
            background: linear-gradient(135deg, var(--info-blue), #138496);
            color: white;
        }
        
        .btn-feature {
            background: linear-gradient(135deg, var(--accent-yellow), #e0a800);
            color: white;
        }
        
        .btn-delete {
            background: linear-gradient(135deg, var(--primary-red), #bd2130);
            color: white;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--medium-gray);
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .empty-state h3 {
            margin-bottom: 1rem;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>BrickClick</h3>
            <p>Contractor Portal</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice-dollar"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="portfolio.php" class="nav-link active">
                    <i class="fas fa-images"></i>Portfolio
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews & Ratings
                </a>
            </div>
            <div class="nav-item">
                <a href="profile.php" class="nav-link">
                    <i class="fas fa-user-edit"></i>Profile & Services
                </a>
            </div>
            <div class="nav-item">
                <a href="payment_history.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payment History
                </a>
            </div>
            <div class="nav-item">
                <a href="notifications.php" class="nav-link">
                    <i class="fas fa-bell"></i>Notifications
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Portfolio</h1>
            <p class="page-subtitle">Showcase your completed projects and achievements</p>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon projects">
                    <i class="fas fa-building"></i>
                </div>
                <h3 class="stat-number"><?php echo $stats['total_projects']; ?></h3>
                <p class="stat-label">Total Projects</p>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon featured">
                    <i class="fas fa-star"></i>
                </div>
                <h3 class="stat-number"><?php echo $stats['featured_projects']; ?></h3>
                <p class="stat-label">Featured Projects</p>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon value">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <h3 class="stat-number">Rs. <?php echo number_format($stats['total_value'] ?? 0); ?></h3>
                <p class="stat-label">Total Project Value</p>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon average">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="stat-number">Rs. <?php echo number_format($stats['avg_value'] ?? 0); ?></h3>
                <p class="stat-label">Average Project Value</p>
            </div>
        </div>

        <!-- Portfolio Header -->
        <div class="portfolio-header">
            <h2 class="section-title">My Projects</h2>
            <a href="add_portfolio.php" class="btn-add">
                <i class="fas fa-plus me-2"></i>Add New Project
            </a>
        </div>

        <!-- Portfolio Grid -->
        <?php if (empty($portfolio_items)): ?>
            <div class="empty-state">
                <i class="fas fa-images"></i>
                <h3>No portfolio projects yet</h3>
                <p>Start building your portfolio by adding your completed projects. This will help customers see your work quality and experience.</p>
                <a href="add_portfolio.php" class="btn-add mt-3">
                    <i class="fas fa-plus me-2"></i>Add Your First Project
                </a>
            </div>
        <?php else: ?>
            <div class="portfolio-grid">
                <?php foreach ($portfolio_items as $item): ?>
                    <div class="portfolio-card">
                        <div class="portfolio-image">
                            <?php if ($item['is_featured']): ?>
                                <div class="featured-badge">
                                    <i class="fas fa-star me-1"></i>Featured
                                </div>
                            <?php endif; ?>
                            
                            <?php
                            $images = json_decode($item['project_images'], true);
                            if (!empty($images)):
                            ?>
                                <img src="../uploads/portfolio/<?php echo htmlspecialchars($images[0]); ?>"
                                     alt="<?php echo htmlspecialchars($item['project_name']); ?>"
                                     style="width: 100%; height: 100%; object-fit: cover;"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center;">
                                    <i class="fas fa-image"></i>
                                </div>
                            <?php else: ?>
                                <i class="fas fa-image"></i>
                            <?php endif; ?>
                        </div>
                        
                        <div class="portfolio-content">
                            <h4 class="portfolio-title"><?php echo htmlspecialchars($item['project_name']); ?></h4>
                            
                            <div class="portfolio-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span><?php echo htmlspecialchars($item['project_location']); ?></span>
                            </div>
                            
                            <p class="portfolio-description">
                                <?php echo htmlspecialchars(substr($item['project_description'], 0, 120)) . (strlen($item['project_description']) > 120 ? '...' : ''); ?>
                            </p>
                            
                            <div class="portfolio-details">
                                <div class="portfolio-value">
                                    Rs. <?php echo number_format($item['project_value']); ?>
                                </div>
                                <div class="portfolio-date">
                                    <?php echo date('M Y', strtotime($item['completion_date'])); ?>
                                </div>
                            </div>
                            
                            <div class="portfolio-actions">
                                <a href="edit_portfolio.php?id=<?php echo $item['id']; ?>" class="btn-action btn-edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="portfolio_id" value="<?php echo $item['id']; ?>">
                                    <button type="submit" name="toggle_featured" class="btn-action btn-feature" 
                                            title="<?php echo $item['is_featured'] ? 'Remove from featured' : 'Mark as featured'; ?>">
                                        <i class="fas fa-star"></i>
                                    </button>
                                </form>
                                
                                <form method="POST" style="display: inline;" 
                                      onsubmit="return confirm('Are you sure you want to delete this portfolio item?');">
                                    <input type="hidden" name="portfolio_id" value="<?php echo $item['id']; ?>">
                                    <button type="submit" name="delete_portfolio" class="btn-action btn-delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
