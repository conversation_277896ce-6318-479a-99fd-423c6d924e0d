<?php
require_once 'config/database.php';

echo "<h2>🔧 Create Test General Quotes</h2>";

try {
    // Step 1: Ensure specific_contractor_id column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        echo "<p>Adding specific_contractor_id column...</p>";
        $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
    }
    
    // Step 2: Get a test customer
    $stmt = $pdo->query("
        SELECT u.id, cp.first_name, cp.last_name 
        FROM users u 
        JOIN customer_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'customer' 
        LIMIT 1
    ");
    $customer = $stmt->fetch();
    
    if (!$customer) {
        echo "<p style='color: red;'>❌ No customers found. Creating test customer...</p>";
        
        // Create test customer
        $pdo->exec("INSERT INTO users (email, password, user_type, status) VALUES ('<EMAIL>', 'password', 'customer', 'approved')");
        $customer_id = $pdo->lastInsertId();
        $pdo->exec("INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district, address) VALUES ($customer_id, 'Test', 'Customer', '0771234567', 'Colombo', 'Test Address')");
        
        $customer = ['id' => $customer_id, 'first_name' => 'Test', 'last_name' => 'Customer'];
        echo "<p style='color: green;'>✅ Created test customer</p>";
    }
    
    echo "<p>Using customer: " . htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']) . "</p>";
    
    // Step 3: Create test general quotes for different services and areas
    $test_quotes = [
        [
            'service_id' => 1, // House Construction
            'title' => 'General Quote - House Construction in Colombo',
            'description' => 'Need a contractor for house construction in Colombo area.',
            'district' => 'Colombo'
        ],
        [
            'service_id' => 2, // Building Renovation
            'title' => 'General Quote - Building Renovation in Gampaha',
            'description' => 'Looking for renovation services in Gampaha district.',
            'district' => 'Gampaha'
        ],
        [
            'service_id' => 8, // Landscaping & Gardening
            'title' => 'General Quote - Landscaping in Kandy',
            'description' => 'Need landscaping and gardening services in Kandy.',
            'district' => 'Kandy'
        ],
        [
            'service_id' => 5, // Roofing & Waterproofing
            'title' => 'General Quote - Roofing in Colombo',
            'description' => 'Roofing and waterproofing services needed in Colombo.',
            'district' => 'Colombo'
        ]
    ];
    
    echo "<h3>Creating Test General Quotes</h3>";
    
    foreach ($test_quotes as $quote) {
        $stmt = $pdo->prepare("
            INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, specific_contractor_id, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NULL, 'open')
        ");
        $stmt->execute([
            $customer['id'],
            $quote['service_id'],
            $quote['title'],
            $quote['description'],
            'Test Location, ' . $quote['district'],
            $quote['district'],
            1000000,
            '3 months'
        ]);
        
        $quote_id = $pdo->lastInsertId();
        echo "<p>✅ Created quote ID $quote_id: " . htmlspecialchars($quote['title']) . "</p>";
    }
    
    // Step 4: Check contractors and their services/areas
    echo "<h3>Checking Contractors</h3>";
    
    $stmt = $pdo->query("
        SELECT u.id, cp.business_name, cp.service_types, cp.service_areas
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        ORDER BY cp.business_name
    ");
    $contractors = $stmt->fetchAll();
    
    echo "<p>Found " . count($contractors) . " approved contractors:</p>";
    
    foreach ($contractors as $contractor) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo "<h4>" . htmlspecialchars($contractor['business_name']) . "</h4>";
        
        $services = json_decode($contractor['service_types'], true) ?: [];
        $areas = json_decode($contractor['service_areas'], true) ?: [];
        
        echo "<p><strong>Services:</strong> " . implode(', ', $services) . "</p>";
        echo "<p><strong>Areas:</strong> " . implode(', ', $areas) . "</p>";
        
        // Test dashboard query for this contractor
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, cp.district,
                   (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            WHERE qr.status = 'open'
            AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
            ORDER BY qr.created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$contractor['id'], $contractor['id']]);
        $dashboard_quotes = $stmt->fetchAll();
        
        echo "<p><strong>Dashboard query returned:</strong> " . count($dashboard_quotes) . " quotes</p>";
        
        // Filter quotes using the same logic as dashboard.php
        $filtered_quotes = [];
        foreach ($dashboard_quotes as $quote) {
            // Always include direct quotes for this contractor
            if ($quote['specific_contractor_id'] == $contractor['id']) {
                $filtered_quotes[] = $quote;
                continue;
            }
            
            // For general quotes, check service and area match
            if ($quote['specific_contractor_id'] === null) {
                $contractor_services = json_decode($contractor['service_types'], true) ?: [];
                $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];
                
                // Check if contractor provides this service
                $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                              in_array((string)$quote['service_category_id'], $contractor_services);
                
                // Check if contractor serves this area
                $has_area = in_array($quote['district'], $contractor_areas);
                
                if ($has_service && $has_area) {
                    $filtered_quotes[] = $quote;
                }
            }
        }
        
        echo "<p><strong>After filtering:</strong> " . count($filtered_quotes) . " quotes</p>";
        
        if (count($filtered_quotes) > 0) {
            echo "<ul>";
            foreach ($filtered_quotes as $quote) {
                $type = $quote['specific_contractor_id'] ? 'Specific' : 'General';
                echo "<li>ID {$quote['id']}: " . htmlspecialchars($quote['title']) . " ({$type})</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠️ No matching quotes for this contractor</p>";
        }
        
        echo "</div>";
    }
    
    echo "<h3>Summary</h3>";
    echo "<p>✅ Test general quotes created successfully!</p>";
    echo "<p>Now check the contractor dashboard to see if quotes appear.</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>
