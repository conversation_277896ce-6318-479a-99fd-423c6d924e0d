<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

echo "<h2>Database Connection Test</h2>";

try {
    // Test database connection
    echo "<p>✅ Database connection successful</p>";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ Users table exists</p>";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll();
        echo "<p>✅ Users table structure:</p>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
        
        // Check if admin user exists
        $stmt = $pdo->prepare("SELECT id, username, email, user_type FROM users WHERE user_type = 'admin'");
        $stmt->execute();
        $admins = $stmt->fetchAll();
        
        if (count($admins) > 0) {
            echo "<p>✅ Admin users found:</p>";
            echo "<ul>";
            foreach ($admins as $admin) {
                echo "<li>ID: {$admin['id']}, Username: {$admin['username']}, Email: {$admin['email']}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>❌ No admin users found</p>";
            echo "<p>Creating default admin user...</p>";
            
            // Create default admin user
            $default_password = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password, user_type, status) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute(['admin', '<EMAIL>', $default_password, 'admin', 'approved']);
            echo "<p>✅ Default admin user created (username: admin, password: admin123)</p>";
        }
        
    } else {
        echo "<p>❌ Users table does not exist</p>";
        echo "<p>Creating users table...</p>";
        
        // Create users table
        $sql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            user_type ENUM('customer', 'contractor', 'admin') NOT NULL,
            status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql);
        echo "<p>✅ Users table created</p>";
        
        // Create default admin user
        $default_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, user_type, status) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $default_password, 'admin', 'approved']);
        echo "<p>✅ Default admin user created (username: admin, password: admin123)</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<br><a href='settings.php'>← Back to Settings</a>";
?>
