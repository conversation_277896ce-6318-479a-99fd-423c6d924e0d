<?php
require_once 'config/database.php';

echo "<h2>🔧 Fix Database Structure for General Quotes</h2>";

try {
    // Step 1: Check and fix specific_contractor_id column
    echo "<h3>Step 1: Check specific_contractor_id Column</h3>";
    
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        echo "<p style='color: orange;'>⚠️ specific_contractor_id column missing - adding it...</p>";
        
        $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        $pdo->exec("ALTER TABLE quote_requests ADD INDEX idx_specific_contractor_id (specific_contractor_id)");
        $pdo->exec("ALTER TABLE quote_requests ADD FOREIGN KEY (specific_contractor_id) REFERENCES users(id) ON DELETE SET NULL");
        
        echo "<p style='color: green;'>✅ Added specific_contractor_id column with proper indexing</p>";
    } else {
        echo "<p style='color: green;'>✅ specific_contractor_id column already exists</p>";
    }
    
    // Step 2: Update existing quote requests to have NULL for general quotes
    echo "<h3>Step 2: Update Existing Quote Requests</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM quote_requests WHERE specific_contractor_id IS NULL");
    $null_count = $stmt->fetchColumn();
    
    echo "<p>Quote requests with NULL specific_contractor_id (general quotes): $null_count</p>";
    
    // Step 3: Check service categories
    echo "<h3>Step 3: Check Service Categories</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM service_categories");
    $service_count = $stmt->fetchColumn();
    
    if ($service_count == 0) {
        echo "<p style='color: orange;'>⚠️ No service categories found - creating default ones...</p>";
        
        $services = [
            ['House Construction', 'Complete house construction services'],
            ['Building Renovation', 'Building renovation and remodeling'],
            ['Commercial Construction', 'Commercial building construction'],
            ['Interior Design & Finishing', 'Interior design and finishing work'],
            ['Roofing & Waterproofing', 'Roofing and waterproofing services'],
            ['Electrical Work', 'Electrical installation and maintenance'],
            ['Plumbing & Sanitation', 'Plumbing and sanitation services'],
            ['Landscaping & Gardening', 'Landscaping and garden design'],
            ['Swimming Pool Construction', 'Swimming pool construction and maintenance'],
            ['Road & Infrastructure', 'Road and infrastructure development']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO service_categories (name_en, description_en) VALUES (?, ?)");
        foreach ($services as $service) {
            $stmt->execute($service);
        }
        
        echo "<p style='color: green;'>✅ Created " . count($services) . " service categories</p>";
    } else {
        echo "<p style='color: green;'>✅ Found $service_count service categories</p>";
        
        // Show existing categories
        $stmt = $pdo->query("SELECT id, name_en FROM service_categories ORDER BY id");
        $categories = $stmt->fetchAll();
        
        echo "<ul>";
        foreach ($categories as $category) {
            echo "<li>ID: " . $category['id'] . " - " . htmlspecialchars($category['name_en']) . "</li>";
        }
        echo "</ul>";
    }
    
    // Step 4: Check contractor profiles have proper service data
    echo "<h3>Step 4: Check Contractor Service Data</h3>";
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as total,
               SUM(CASE WHEN service_areas IS NOT NULL AND service_areas != '' THEN 1 ELSE 0 END) as has_areas,
               SUM(CASE WHEN service_types IS NOT NULL AND service_types != '' THEN 1 ELSE 0 END) as has_types
        FROM contractor_profiles
    ");
    $contractor_stats = $stmt->fetch();
    
    echo "<p>Contractor profiles:</p>";
    echo "<ul>";
    echo "<li>Total: " . $contractor_stats['total'] . "</li>";
    echo "<li>With service areas: " . $contractor_stats['has_areas'] . "</li>";
    echo "<li>With service types: " . $contractor_stats['has_types'] . "</li>";
    echo "</ul>";
    
    if ($contractor_stats['has_areas'] == 0 || $contractor_stats['has_types'] == 0) {
        echo "<p style='color: orange;'>⚠️ Some contractors missing service data - this will affect matching</p>";
        
        // Show contractors without proper data
        $stmt = $pdo->query("
            SELECT u.id, u.email, cp.business_name, cp.service_areas, cp.service_types
            FROM users u
            JOIN contractor_profiles cp ON u.id = cp.user_id
            WHERE u.user_type = 'contractor'
            AND (cp.service_areas IS NULL OR cp.service_areas = '' OR cp.service_types IS NULL OR cp.service_types = '')
        ");
        $incomplete_contractors = $stmt->fetchAll();
        
        if (count($incomplete_contractors) > 0) {
            echo "<p>Contractors with incomplete service data:</p>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Business Name</th><th>Email</th><th>Areas</th><th>Types</th></tr>";
            foreach ($incomplete_contractors as $contractor) {
                echo "<tr>";
                echo "<td>" . $contractor['id'] . "</td>";
                echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
                echo "<td>" . htmlspecialchars($contractor['email']) . "</td>";
                echo "<td>" . ($contractor['service_areas'] ? 'Yes' : 'No') . "</td>";
                echo "<td>" . ($contractor['service_types'] ? 'Yes' : 'No') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: green;'>✅ All contractors have service data</p>";
    }
    
    // Step 5: Test data integrity
    echo "<h3>Step 5: Test Data Integrity</h3>";
    
    // Check for valid JSON in contractor profiles
    $stmt = $pdo->query("
        SELECT id, business_name, service_areas, service_types
        FROM contractor_profiles
        WHERE service_areas IS NOT NULL AND service_types IS NOT NULL
    ");
    $contractors = $stmt->fetchAll();
    
    $valid_json_count = 0;
    $invalid_contractors = [];
    
    foreach ($contractors as $contractor) {
        $areas_valid = json_decode($contractor['service_areas']) !== null;
        $types_valid = json_decode($contractor['service_types']) !== null;
        
        if ($areas_valid && $types_valid) {
            $valid_json_count++;
        } else {
            $invalid_contractors[] = $contractor;
        }
    }
    
    echo "<p>JSON validation:</p>";
    echo "<ul>";
    echo "<li>Total contractors checked: " . count($contractors) . "</li>";
    echo "<li>Valid JSON data: $valid_json_count</li>";
    echo "<li>Invalid JSON data: " . count($invalid_contractors) . "</li>";
    echo "</ul>";
    
    if (count($invalid_contractors) > 0) {
        echo "<p style='color: red;'>❌ Found contractors with invalid JSON data:</p>";
        foreach ($invalid_contractors as $contractor) {
            echo "<p>- " . htmlspecialchars($contractor['business_name']) . " (ID: " . $contractor['id'] . ")</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ All contractor service data has valid JSON format</p>";
    }
    
    echo "<h3>✅ Database Structure Check Complete</h3>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>specific_contractor_id column: ✅ Ready</li>";
    echo "<li>Service categories: ✅ Ready ($service_count categories)</li>";
    echo "<li>Contractor service data: " . ($valid_json_count == count($contractors) ? '✅ Ready' : '⚠️ Needs attention') . "</li>";
    echo "</ul>";
    
    if ($valid_json_count == count($contractors) && $service_count > 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 Database structure is ready for general quotes!</p>";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠️ Some issues need to be resolved before general quotes will work properly.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
