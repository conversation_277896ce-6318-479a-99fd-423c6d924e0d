<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔍 Check Latest Quote Request</h2>";

try {
    // Get the most recent quote request
    $stmt = $pdo->query("
        SELECT qr.*, sc.name_en as service_name, cp.first_name, cp.last_name
        FROM quote_requests qr
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        LEFT JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        ORDER BY qr.created_at DESC
        LIMIT 1
    ");
    $latest_quote = $stmt->fetch();
    
    if (!$latest_quote) {
        echo "<p style='color: red;'>❌ No quote requests found!</p>";
        exit;
    }
    
    echo "<h3>Latest Quote Request Details</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>ID</td><td>" . $latest_quote['id'] . "</td></tr>";
    echo "<tr><td>Title</td><td>" . htmlspecialchars($latest_quote['title']) . "</td></tr>";
    echo "<tr><td>Service Category ID</td><td>" . $latest_quote['service_category_id'] . "</td></tr>";
    echo "<tr><td>Service Name</td><td>" . htmlspecialchars($latest_quote['service_name'] ?? 'Unknown') . "</td></tr>";
    echo "<tr><td>District</td><td>" . htmlspecialchars($latest_quote['district']) . "</td></tr>";
    echo "<tr><td>Location</td><td>" . htmlspecialchars($latest_quote['location']) . "</td></tr>";
    echo "<tr><td>Budget</td><td>Rs. " . number_format($latest_quote['estimated_budget']) . "</td></tr>";
    echo "<tr><td>Timeline</td><td>" . htmlspecialchars($latest_quote['project_timeline']) . "</td></tr>";
    echo "<tr><td>Specific Contractor</td><td>" . ($latest_quote['specific_contractor_id'] ? $latest_quote['specific_contractor_id'] : 'None (General Quote)') . "</td></tr>";
    echo "<tr><td>Status</td><td>" . $latest_quote['status'] . "</td></tr>";
    echo "<tr><td>Created</td><td>" . $latest_quote['created_at'] . "</td></tr>";
    echo "</table>";
    
    // Check if this is a general quote
    if (!$latest_quote['specific_contractor_id']) {
        echo "<h3>This is a General Quote - Testing Contractor Matching</h3>";
        
        // Get all approved contractors
        $stmt = $pdo->query("
            SELECT u.id, u.email, cp.business_name, cp.service_types, cp.service_areas, u.status
            FROM users u 
            JOIN contractor_profiles cp ON u.id = cp.user_id 
            WHERE u.user_type = 'contractor' AND u.status = 'approved'
            ORDER BY u.id
        ");
        $contractors = $stmt->fetchAll();
        
        echo "<p><strong>Found " . count($contractors) . " approved contractors</strong></p>";
        
        $matching_contractors = 0;
        
        foreach ($contractors as $contractor) {
            echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; background: #f9f9f9;'>";
            echo "<h4>" . htmlspecialchars($contractor['business_name']) . " (ID: " . $contractor['id'] . ")</h4>";
            echo "<p><strong>Email:</strong> " . htmlspecialchars($contractor['email']) . "</p>";
            echo "<p><strong>Service Types:</strong> " . htmlspecialchars($contractor['service_types']) . "</p>";
            echo "<p><strong>Service Areas:</strong> " . htmlspecialchars($contractor['service_areas']) . "</p>";
            
            $service_areas = json_decode($contractor['service_areas'], true) ?: [];
            $service_types = json_decode($contractor['service_types'], true) ?: [];
            
            echo "<p><strong>Parsed Service Types:</strong> " . implode(', ', $service_types) . "</p>";
            echo "<p><strong>Parsed Service Areas:</strong> " . implode(', ', $service_areas) . "</p>";
            
            $has_service = in_array((int)$latest_quote['service_category_id'], $service_types) ||
                          in_array((string)$latest_quote['service_category_id'], $service_types);
            $has_area = in_array($latest_quote['district'], $service_areas);
            
            echo "<p><strong>Service Match (looking for " . $latest_quote['service_category_id'] . "):</strong> " . ($has_service ? '✅ YES' : '❌ NO') . "</p>";
            echo "<p><strong>Area Match (looking for '" . $latest_quote['district'] . "'):</strong> " . ($has_area ? '✅ YES' : '❌ NO') . "</p>";
            
            if ($has_service && $has_area) {
                echo "<p style='color: green; font-weight: bold;'>✅ THIS CONTRACTOR SHOULD SEE THE QUOTE</p>";
                $matching_contractors++;
            } else {
                echo "<p style='color: red; font-weight: bold;'>❌ This contractor should NOT see the quote</p>";
            }
            echo "</div>";
        }
        
        echo "<h3>Summary</h3>";
        echo "<p><strong>Total contractors that should see this quote:</strong> $matching_contractors</p>";
        
        // Check notifications
        echo "<h3>Checking Notifications</h3>";
        $stmt = $pdo->prepare("
            SELECT n.*, u.email, cp.business_name
            FROM notifications n
            JOIN users u ON n.user_id = u.id
            LEFT JOIN contractor_profiles cp ON u.id = cp.user_id
            WHERE n.related_id = ? AND n.type = 'quote_received'
            ORDER BY n.created_at DESC
        ");
        $stmt->execute([$latest_quote['id']]);
        $notifications = $stmt->fetchAll();
        
        echo "<p><strong>Notifications created:</strong> " . count($notifications) . "</p>";
        
        if (count($notifications) != $matching_contractors) {
            echo "<p style='color: red; font-weight: bold;'>❌ MISMATCH! Expected $matching_contractors notifications but found " . count($notifications) . "</p>";
        } else {
            echo "<p style='color: green; font-weight: bold;'>✅ Notification count matches expected contractors</p>";
        }
        
        if (!empty($notifications)) {
            echo "<h4>Notification Details:</h4>";
            foreach ($notifications as $notif) {
                echo "<p>• " . htmlspecialchars($notif['business_name']) . " (" . htmlspecialchars($notif['email']) . ") - " . htmlspecialchars($notif['message']) . "</p>";
            }
        }
        
        // Test if contractors can actually see the quote
        echo "<h3>Testing Contractor Quote Visibility</h3>";
        
        foreach ($contractors as $contractor) {
            $service_areas = json_decode($contractor['service_areas'], true) ?: [];
            $service_types = json_decode($contractor['service_types'], true) ?: [];
            
            $has_service = in_array((int)$latest_quote['service_category_id'], $service_types) ||
                          in_array((string)$latest_quote['service_category_id'], $service_types);
            $has_area = in_array($latest_quote['district'], $service_areas);
            
            if ($has_service && $has_area) {
                echo "<h4>Testing " . htmlspecialchars($contractor['business_name']) . "</h4>";
                
                // Test dashboard query
                $stmt = $pdo->prepare("
                    SELECT qr.*, cp_contractor.service_types, cp_contractor.service_areas
                    FROM quote_requests qr
                    JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
                    WHERE qr.id = ?
                    AND qr.status = 'open'
                    AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
                    AND qr.id NOT IN (
                        SELECT COALESCE(quote_request_id, 0)
                        FROM quote_responses
                        WHERE contractor_id = ?
                    )
                ");
                $stmt->execute([$contractor['id'], $latest_quote['id'], $contractor['id'], $contractor['id']]);
                $dashboard_result = $stmt->fetch();
                
                if ($dashboard_result) {
                    echo "<p>✅ Dashboard query finds the quote</p>";
                } else {
                    echo "<p>❌ Dashboard query does NOT find the quote</p>";
                }
                
                // Test quotes page query
                $stmt = $pdo->prepare("
                    SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
                           (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
                           CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
                           cp_contractor.service_types, cp_contractor.service_areas
                    FROM quote_requests qr
                    JOIN customer_profiles cp ON qr.customer_id = cp.user_id
                    LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
                    JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
                    WHERE qr.id = ?
                    AND qr.status = 'open' 
                    AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)
                    AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
                ");
                $stmt->execute([$contractor['id'], $contractor['id'], $contractor['id'], $latest_quote['id'], $contractor['id'], $contractor['id']]);
                $quotes_result = $stmt->fetch();
                
                if ($quotes_result) {
                    echo "<p>✅ Quotes page query finds the quote</p>";
                    
                    // Test PHP filtering
                    if ($quotes_result['specific_contractor_id'] === null) {
                        $contractor_services = json_decode($quotes_result['service_types'], true) ?: [];
                        $contractor_areas = json_decode($quotes_result['service_areas'], true) ?: [];
                        
                        $has_service_php = in_array((int)$quotes_result['service_category_id'], $contractor_services) ||
                                          in_array((string)$quotes_result['service_category_id'], $contractor_services);
                        $has_area_php = in_array($quotes_result['district'], $contractor_areas);
                        
                        if ($has_service_php && $has_area_php) {
                            echo "<p>✅ PHP filtering passes - quote should be visible</p>";
                        } else {
                            echo "<p>❌ PHP filtering fails - quote will be hidden</p>";
                            echo "<p>Service match: " . ($has_service_php ? 'Yes' : 'No') . ", Area match: " . ($has_area_php ? 'Yes' : 'No') . "</p>";
                        }
                    }
                } else {
                    echo "<p>❌ Quotes page query does NOT find the quote</p>";
                }
                
                echo "<hr>";
            }
        }
        
    } else {
        echo "<h3>This is a Direct Quote (sent to specific contractor)</h3>";
        echo "<p>Contractor ID: " . $latest_quote['specific_contractor_id'] . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
