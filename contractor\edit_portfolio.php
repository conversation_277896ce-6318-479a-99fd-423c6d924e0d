<?php
// Include authentication and authorization check
require_once 'includes/auth_check.php';

// Contractor data is now available in $contractor variable

$portfolio_id = (int)($_GET['id'] ?? 0);

if (!$portfolio_id) {
    $_SESSION['error'] = 'Invalid portfolio item.';
    header('Location: portfolio.php');
    exit();
}

// Get portfolio item
try {
    $stmt = $pdo->prepare("SELECT * FROM contractor_portfolios WHERE id = ? AND contractor_id = ?");
    $stmt->execute([$portfolio_id, $_SESSION['user_id']]);
    $portfolio_item = $stmt->fetch();
    
    if (!$portfolio_item) {
        $_SESSION['error'] = 'Portfolio item not found.';
        header('Location: portfolio.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: portfolio.php');
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $project_name = trim($_POST['project_name']);
    $project_description = trim($_POST['project_description']);
    $project_location = trim($_POST['project_location']);
    $completion_date = $_POST['completion_date'];
    $project_value = (float)$_POST['project_value'];
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
    
    $errors = [];
    
    // Validation
    if (empty($project_name)) $errors[] = 'Project name is required.';
    if (empty($project_description)) $errors[] = 'Project description is required.';
    if (empty($project_location)) $errors[] = 'Project location is required.';
    if (empty($completion_date)) $errors[] = 'Completion date is required.';
    if ($project_value <= 0) $errors[] = 'Project value must be greater than 0.';
    
    // Handle image uploads
    $existing_images = json_decode($portfolio_item['project_images'], true) ?: [];
    $project_images = $existing_images;
    
    if (isset($_FILES['project_images']) && !empty($_FILES['project_images']['name'][0])) {
        $upload_dir = '../uploads/portfolio/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $max_file_size = 5 * 1024 * 1024; // 5MB
        
        foreach ($_FILES['project_images']['tmp_name'] as $key => $tmp_name) {
            if (!empty($tmp_name)) {
                $file_name = $_FILES['project_images']['name'][$key];
                $file_size = $_FILES['project_images']['size'][$key];
                $file_type = $_FILES['project_images']['type'][$key];
                
                if (!in_array($file_type, $allowed_types)) {
                    $errors[] = "Invalid file type for $file_name. Only JPG, PNG, and GIF are allowed.";
                    continue;
                }
                
                if ($file_size > $max_file_size) {
                    $errors[] = "File $file_name is too large. Maximum size is 5MB.";
                    continue;
                }
                
                $file_extension = pathinfo($file_name, PATHINFO_EXTENSION);
                $new_file_name = 'portfolio_' . $portfolio_id . '_' . time() . '_' . $key . '.' . $file_extension;
                $upload_path = $upload_dir . $new_file_name;
                
                if (move_uploaded_file($tmp_name, $upload_path)) {
                    $project_images[] = 'uploads/portfolio/' . $new_file_name;
                } else {
                    $errors[] = "Failed to upload $file_name.";
                }
            }
        }
    }
    
    // Handle image removal
    if (isset($_POST['remove_images'])) {
        foreach ($_POST['remove_images'] as $remove_image) {
            $key = array_search($remove_image, $project_images);
            if ($key !== false) {
                unset($project_images[$key]);
                // Delete file from server
                if (file_exists('../' . $remove_image)) {
                    unlink('../' . $remove_image);
                }
            }
        }
        $project_images = array_values($project_images); // Re-index array
    }
    
    if (empty($errors)) {
        try {
            $stmt = $pdo->prepare("
                UPDATE contractor_portfolios SET 
                    project_name = ?, project_description = ?, project_location = ?,
                    completion_date = ?, project_value = ?, project_images = ?, 
                    is_featured = ?, updated_at = NOW()
                WHERE id = ? AND contractor_id = ?
            ");
            
            $stmt->execute([
                $project_name,
                $project_description,
                $project_location,
                $completion_date,
                $project_value,
                json_encode($project_images),
                $is_featured,
                $portfolio_id,
                $_SESSION['user_id']
            ]);
            
            $_SESSION['success'] = 'Portfolio item updated successfully!';
            header('Location: portfolio.php');
            exit();
            
        } catch (PDOException $e) {
            $errors[] = 'Database error. Please try again.';
        }
    }
}

// Decode existing images
$existing_images = json_decode($portfolio_item['project_images'], true) ?: [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Portfolio - <?php echo htmlspecialchars($contractor['business_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF6B35;
            --accent-yellow: #F7931E;
            --light-gray: #F8F9FA;
            --medium-gray: #6C757D;
            --success-green: #28a745;
            --danger-red: #dc3545;
            --gradient-primary: linear-gradient(135deg, #C5172E, #FF6B35);
            --gradient-secondary: linear-gradient(135deg, #06202B, #2C3E50);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: var(--gradient-secondary);
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            line-height: 1.6;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(197, 23, 46, 0.1);
            border-bottom: 1px solid rgba(197, 23, 46, 0.2);
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-red) !important;
            font-size: 1.5rem;
        }

        .nav-link {
            color: var(--primary-dark) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-red) !important;
            transform: translateY(-1px);
        }

        .main-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 3rem 2rem;
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-title {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            letter-spacing: -0.02em;
        }

        .page-subtitle {
            color: var(--medium-gray);
            font-size: 1.2rem;
            font-weight: 400;
        }

        .form-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 3rem;
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-section {
            margin-bottom: 2.5rem;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary-dark);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-title i {
            color: var(--primary-red);
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-label {
            font-weight: 600;
            color: var(--primary-dark);
            margin-bottom: 0.8rem;
            font-size: 1rem;
            display: block;
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1rem 1.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            width: 100%;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-red);
            box-shadow: 0 0 0 0.25rem rgba(197, 23, 46, 0.15);
            background: white;
            outline: none;
        }

        .form-control::placeholder {
            color: #adb5bd;
        }

        textarea.form-control {
            resize: vertical;
            min-height: 120px;
        }

        .btn {
            padding: 1rem 2.5rem;
            border-radius: 15px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(197, 23, 46, 0.4);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--medium-gray), #5a6268);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(108, 117, 125, 0.3);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-red), #c82333);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(220, 53, 69, 0.3);
            color: white;
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            font-weight: 500;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }

        .image-upload-area {
            border: 3px dashed #dee2e6;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: rgba(248, 249, 250, 0.5);
        }

        .image-upload-area:hover {
            border-color: var(--primary-orange);
            background: rgba(255, 140, 0, 0.05);
        }

        .image-upload-area.dragover {
            border-color: var(--primary-orange);
            background: rgba(255, 140, 0, 0.1);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--primary-orange);
            margin-bottom: 1rem;
        }

        .image-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .image-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            aspect-ratio: 1;
        }

        .image-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .image-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .remove-image {
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--danger-red);
            color: white;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .remove-image:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        .required {
            color: var(--danger-red);
        }

        .form-check {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            padding: 1rem;
            background: rgba(255, 140, 0, 0.05);
            border-radius: 15px;
            border: 2px solid rgba(255, 140, 0, 0.1);
        }

        .form-check-input {
            width: 20px;
            height: 20px;
            border-radius: 6px;
            border: 2px solid var(--primary-orange);
        }

        .form-check-input:checked {
            background-color: var(--primary-orange);
            border-color: var(--primary-orange);
        }

        .form-check-label {
            font-weight: 500;
            color: var(--primary-dark);
            cursor: pointer;
        }

        .button-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 3rem;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 0 0.5rem;
            }

            .form-container {
                padding: 2rem 1.5rem;
            }

            .page-header {
                padding: 2rem 1.5rem;
            }

            .page-title {
                font-size: 2rem;
            }

            .button-group {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }

        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-hard-hat me-2"></i>BrickClick Pro
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="portfolio.php">
                    <i class="fas fa-arrow-left me-1"></i>Back to Portfolio
                </a>
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="main-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Edit Portfolio Item</h1>
            <p class="page-subtitle">Transform your project showcase with stunning details and visuals</p>
        </div>

        <!-- Error Messages -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- Edit Form -->
        <div class="form-container">
            <form method="POST" enctype="multipart/form-data" id="portfolioForm">
                <!-- Project Information Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-info-circle"></i>
                        Project Information
                    </h3>

                    <div class="row">
                        <div class="col-lg-8">
                            <div class="form-group">
                                <label class="form-label">Project Name <span class="required">*</span></label>
                                <input type="text" class="form-control" name="project_name"
                                       placeholder="Enter your project name..."
                                       value="<?php echo htmlspecialchars($portfolio_item['project_name']); ?>" required>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="form-group">
                                <label class="form-label">Project Value (LKR) <span class="required">*</span></label>
                                <input type="number" class="form-control" name="project_value"
                                       placeholder="0.00"
                                       value="<?php echo $portfolio_item['project_value']; ?>" step="0.01" min="0" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-8">
                            <div class="form-group">
                                <label class="form-label">Project Location <span class="required">*</span></label>
                                <input type="text" class="form-control" name="project_location"
                                       placeholder="Enter project location..."
                                       value="<?php echo htmlspecialchars($portfolio_item['project_location']); ?>" required>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="form-group">
                                <label class="form-label">Completion Date <span class="required">*</span></label>
                                <input type="date" class="form-control" name="completion_date"
                                       value="<?php echo $portfolio_item['completion_date']; ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Project Description <span class="required">*</span></label>
                        <textarea class="form-control" name="project_description"
                                  placeholder="Describe your project in detail..." required><?php echo htmlspecialchars($portfolio_item['project_description']); ?></textarea>
                    </div>
                </div>

                <!-- Project Images Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-images"></i>
                        Project Images
                    </h3>

                    <div class="form-group">
                        <label class="form-label">Upload New Images</label>
                        <div class="image-upload-area" id="imageUploadArea">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h5>Drag & Drop Images Here</h5>
                            <p class="text-muted mb-3">or click to browse files</p>
                            <input type="file" class="form-control" name="project_images[]" multiple accept="image/*" id="imageInput" style="display: none;">
                            <button type="button" class="btn btn-secondary" onclick="document.getElementById('imageInput').click()">
                                <i class="fas fa-folder-open me-2"></i>Choose Files
                            </button>
                            <small class="d-block mt-2 text-muted">JPG, PNG, GIF up to 5MB each</small>
                        </div>
                    </div>

                    <?php if (!empty($existing_images)): ?>
                        <div class="form-group">
                            <label class="form-label">Current Images</label>
                            <div class="image-preview">
                                <?php foreach ($existing_images as $image): ?>
                                    <div class="image-item" data-image="<?php echo htmlspecialchars($image); ?>">
                                        <img src="../<?php echo htmlspecialchars($image); ?>" alt="Project Image">
                                        <button type="button" class="remove-image" onclick="removeImage('<?php echo htmlspecialchars($image); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <input type="hidden" name="keep_images[]" value="<?php echo htmlspecialchars($image); ?>">
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Project Settings Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-cog"></i>
                        Project Settings
                    </h3>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_featured" id="is_featured"
                                   <?php echo $portfolio_item['is_featured'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_featured">
                                <i class="fas fa-star me-2"></i>Feature this project in your portfolio showcase
                            </label>
                        </div>
                    </div>
                </div>

                <div class="button-group">
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-save me-2"></i>Update Portfolio Item
                    </button>
                    <a href="portfolio.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Portfolio
                    </a>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash me-2"></i>Delete Project
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Enhanced image upload functionality
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('imageUploadArea');
            const imageInput = document.getElementById('imageInput');
            const form = document.getElementById('portfolioForm');
            const submitBtn = document.getElementById('submitBtn');

            // Drag and drop functionality
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                handleFiles(files);
            });

            uploadArea.addEventListener('click', function() {
                imageInput.click();
            });

            imageInput.addEventListener('change', function() {
                handleFiles(this.files);
            });

            // Form submission with loading state
            form.addEventListener('submit', function() {
                submitBtn.innerHTML = '<span class="spinner"></span> Updating...';
                submitBtn.disabled = true;
                form.classList.add('loading');
            });

            function handleFiles(files) {
                if (files.length > 0) {
                    const fileList = Array.from(files).map(file => file.name).join(', ');
                    uploadArea.querySelector('h5').textContent = `${files.length} file(s) selected`;
                    uploadArea.querySelector('p').textContent = fileList;
                }
            }
        });

        function removeImage(imagePath) {
            if (confirm('🗑️ Are you sure you want to remove this image?\n\nThis action cannot be undone.')) {
                const imageItems = document.querySelectorAll('.image-item');
                imageItems.forEach(item => {
                    if (item.dataset.image === imagePath) {
                        // Add smooth removal animation
                        item.style.transform = 'scale(0)';
                        item.style.opacity = '0';

                        setTimeout(() => {
                            item.style.display = 'none';

                            // Add hidden input to mark for removal
                            const removeInput = document.createElement('input');
                            removeInput.type = 'hidden';
                            removeInput.name = 'remove_images[]';
                            removeInput.value = imagePath;
                            item.appendChild(removeInput);

                            // Remove the keep input
                            const keepInput = item.querySelector('input[name="keep_images[]"]');
                            if (keepInput) {
                                keepInput.remove();
                            }
                        }, 300);
                    }
                });
            }
        }

        function confirmDelete() {
            if (confirm('⚠️ Delete Portfolio Item\n\nAre you sure you want to permanently delete this portfolio item?\n\nThis action cannot be undone and will remove all associated images.')) {
                window.location.href = 'portfolio.php?delete=<?php echo $portfolio_id; ?>';
            }
        }

        // Auto-save draft functionality (optional enhancement)
        let autoSaveTimer;
        const formInputs = document.querySelectorAll('input, textarea');

        formInputs.forEach(input => {
            input.addEventListener('input', function() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(saveDraft, 2000);
            });
        });

        function saveDraft() {
            // This could save form data to localStorage for recovery
            const formData = new FormData(document.getElementById('portfolioForm'));
            const draftData = {};
            for (let [key, value] of formData.entries()) {
                if (key !== 'project_images[]') {
                    draftData[key] = value;
                }
            }
            localStorage.setItem('portfolio_draft_<?php echo $portfolio_id; ?>', JSON.stringify(draftData));
        }

        // Form validation enhancement
        function validateForm() {
            const requiredFields = document.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.style.borderColor = '#dc3545';
                    isValid = false;
                } else {
                    field.style.borderColor = '#28a745';
                }
            });

            return isValid;
        }

        // Real-time validation
        document.querySelectorAll('[required]').forEach(field => {
            field.addEventListener('blur', validateForm);
        });
    </script>
</body>
</html>
