-- Add customer notification preferences table
-- Run this script if you already have an existing database

CREATE TABLE IF NOT EXISTS customer_notification_preferences (
    id INT AUTO_INCREMENT,
    user_id INT NOT NULL,
    quote_notifications TINYINT(1) DEFAULT 1,
    message_notifications TINYINT(1) DEFAULT 1,
    update_notifications TINYINT(1) DEFAULT 1,
    email_notifications TINYINT(1) DEFAULT 1,
    sms_notifications TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON>IMAR<PERSON> KEY (id),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_customer_notification_prefs (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
