<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔧 Debug Plumbing Quote Issue</h2>";

// Get the latest quote request
$stmt = $pdo->query("
    SELECT qr.*, sc.name_en as service_name, cp.first_name, cp.last_name
    FROM quote_requests qr
    LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
    LEFT JOIN customer_profiles cp ON qr.customer_id = cp.user_id
    ORDER BY qr.created_at DESC
    LIMIT 1
");
$latest_quote = $stmt->fetch();

if (!$latest_quote) {
    echo "<p>❌ No quote requests found</p>";
    exit;
}

echo "<h3>📋 Latest Quote Request:</h3>";
echo "<ul>";
echo "<li><strong>ID:</strong> {$latest_quote['id']}</li>";
echo "<li><strong>Title:</strong> " . htmlspecialchars($latest_quote['title']) . "</li>";
echo "<li><strong>Service:</strong> {$latest_quote['service_name']} (ID: {$latest_quote['service_category_id']})</li>";
echo "<li><strong>District:</strong> {$latest_quote['district']}</li>";
echo "<li><strong>Specific Contractor ID:</strong> " . ($latest_quote['specific_contractor_id'] ?: 'NULL (General Quote)') . "</li>";
echo "<li><strong>Status:</strong> {$latest_quote['status']}</li>";
echo "<li><strong>Created:</strong> {$latest_quote['created_at']}</li>";
echo "</ul>";

// Find the contractor <NAME_EMAIL>
$stmt = $pdo->prepare("
    SELECT u.id, u.email, cp.business_name, cp.service_types, cp.service_areas
    FROM users u
    JOIN contractor_profiles cp ON u.id = cp.user_id
    WHERE u.email = ? AND u.status = 'approved'
");
$stmt->execute(['<EMAIL>']);
$target_contractor = $stmt->fetch();

if (!$target_contractor) {
    echo "<p>❌ Contractor <EMAIL> not found or not approved</p>";
    exit;
}

echo "<h3>👷 Target Contractor Details:</h3>";
echo "<ul>";
echo "<li><strong>ID:</strong> {$target_contractor['id']}</li>";
echo "<li><strong>Email:</strong> {$target_contractor['email']}</li>";
echo "<li><strong>Business:</strong> {$target_contractor['business_name']}</li>";
echo "<li><strong>Service Types:</strong> {$target_contractor['service_types']}</li>";
echo "<li><strong>Service Areas:</strong> {$target_contractor['service_areas']}</li>";
echo "</ul>";

$contractor_services = json_decode($target_contractor['service_types'], true) ?: [];
$contractor_areas = json_decode($target_contractor['service_areas'], true) ?: [];

echo "<p><strong>Parsed Services:</strong> " . implode(', ', $contractor_services) . "</p>";
echo "<p><strong>Parsed Areas:</strong> " . implode(', ', $contractor_areas) . "</p>";

// Check if this contractor should see the latest quote
echo "<h3>🔍 Matching Logic Test:</h3>";

$should_see_quote = false;
$reason = "";

if ($latest_quote['specific_contractor_id'] == $target_contractor['id']) {
    $should_see_quote = true;
    $reason = "Direct quote to this contractor";
} elseif ($latest_quote['specific_contractor_id'] === null) {
    // General quote - check service and area match
    $has_service = in_array((int)$latest_quote['service_category_id'], $contractor_services) ||
                  in_array((string)$latest_quote['service_category_id'], $contractor_services);
    $has_area = in_array($latest_quote['district'], $contractor_areas);
    
    echo "<p><strong>Service Match:</strong> " . ($has_service ? '✅ YES' : '❌ NO') . 
         " (Looking for service ID {$latest_quote['service_category_id']} in [" . implode(', ', $contractor_services) . "])</p>";
    echo "<p><strong>Area Match:</strong> " . ($has_area ? '✅ YES' : '❌ NO') . 
         " (Looking for district '{$latest_quote['district']}' in [" . implode(', ', $contractor_areas) . "])</p>";
    
    if ($has_service && $has_area) {
        $should_see_quote = true;
        $reason = "Service and area match";
    } else {
        $reason = "Service match: " . ($has_service ? 'YES' : 'NO') . ", Area match: " . ($has_area ? 'YES' : 'NO');
    }
} else {
    $reason = "Quote is for specific contractor ID: {$latest_quote['specific_contractor_id']}";
}

echo "<p><strong>Should see quote:</strong> " . ($should_see_quote ? '✅ YES' : '❌ NO') . " - {$reason}</p>";

// Test the actual dashboard query for this contractor
echo "<h3>🧪 Testing Dashboard Query:</h3>";

$stmt = $pdo->prepare("
    SELECT qr.*, cp.first_name, cp.last_name, cp.district,
           (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
    FROM quote_requests qr
    JOIN customer_profiles cp ON qr.customer_id = cp.user_id
    WHERE qr.status = 'open'
    AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
    ORDER BY qr.created_at DESC
    LIMIT 10
");
$stmt->execute([$target_contractor['id'], $target_contractor['id']]);
$all_recent_quotes = $stmt->fetchAll();

echo "<p><strong>Raw quotes from database:</strong> " . count($all_recent_quotes) . "</p>";

// Apply the filtering logic
$recent_quotes = [];
foreach ($all_recent_quotes as $quote) {
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo "<h4>Quote ID: {$quote['id']} - {$quote['title']}</h4>";
    
    // Always show direct quotes for this contractor
    if ($quote['specific_contractor_id'] == $target_contractor['id']) {
        $recent_quotes[] = $quote;
        echo "<p>✅ INCLUDED: Direct quote for this contractor</p>";
        continue;
    }

    // For general quotes (no specific contractor), check service and area match
    if ($quote['specific_contractor_id'] === null) {
        $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                      in_array((string)$quote['service_category_id'], $contractor_services);
        $has_area = in_array($quote['district'], $contractor_areas);

        echo "<p>Service ID: {$quote['service_category_id']}, District: {$quote['district']}</p>";
        echo "<p>Service match: " . ($has_service ? 'YES' : 'NO') . ", Area match: " . ($has_area ? 'YES' : 'NO') . "</p>";

        if ($has_service && $has_area) {
            $recent_quotes[] = $quote;
            echo "<p>✅ INCLUDED: General quote with service and area match</p>";
        } else {
            echo "<p>❌ EXCLUDED: No service/area match</p>";
        }
    } else {
        echo "<p>❌ EXCLUDED: Direct quote for contractor ID {$quote['specific_contractor_id']}</p>";
    }
    
    echo "</div>";

    // Limit to 5 quotes for dashboard
    if (count($recent_quotes) >= 5) {
        break;
    }
}

echo "<h3>📊 Final Results:</h3>";
echo "<p><strong>Quotes that should appear in dashboard:</strong> " . count($recent_quotes) . "</p>";

if (count($recent_quotes) > 0) {
    echo "<ul>";
    foreach ($recent_quotes as $quote) {
        echo "<li>ID {$quote['id']}: {$quote['title']} - {$quote['first_name']} {$quote['last_name']} ({$quote['district']})</li>";
    }
    echo "</ul>";
} else {
    echo "<p>❌ No quotes should appear - this explains why the dashboard is empty!</p>";
}

// Check if contractor has responded to any quotes
echo "<h3>📝 Response Check:</h3>";
$stmt = $pdo->prepare("SELECT COUNT(*) FROM quote_responses WHERE contractor_id = ?");
$stmt->execute([$target_contractor['id']]);
$response_count = $stmt->fetchColumn();
echo "<p>Total responses by this contractor: {$response_count}</p>";

if ($response_count > 0) {
    $stmt = $pdo->prepare("
        SELECT qr.quote_request_id, qreq.title, qr.status, qr.created_at
        FROM quote_responses qr
        JOIN quote_requests qreq ON qr.quote_request_id = qreq.id
        WHERE qr.contractor_id = ?
        ORDER BY qr.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$target_contractor['id']]);
    $responses = $stmt->fetchAll();
    
    echo "<p>Recent responses:</p>";
    echo "<ul>";
    foreach ($responses as $response) {
        echo "<li>Quote {$response['quote_request_id']}: {$response['title']} - Status: {$response['status']}</li>";
    }
    echo "</ul>";
}
?>
