<?php
session_start();
require_once 'config/database.php';

echo "<h2>🧪 Review Submission Test</h2>";

// Set up test session
$_SESSION['user_id'] = 1; // Assuming customer ID 1 exists
$_SESSION['user_type'] = 'customer';

if ($_POST) {
    echo "<h3>📝 Processing Review Submission</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    
    echo "<h4>Submitted Data:</h4>";
    foreach ($_POST as $key => $value) {
        echo "<p><strong>$key:</strong> " . htmlspecialchars($value) . "</p>";
    }
    echo "</div>";
    
    // Test the submission
    try {
        $contractor_id = (int)$_POST['contractor_id'];
        $payment_id = (int)$_POST['payment_id'];
        $quote_response_id = (int)$_POST['quote_response_id'];
        $rating = (int)$_POST['rating'];
        $review_text = trim($_POST['review_text']);
        $quality_rating = !empty($_POST['quality_rating']) ? (int)$_POST['quality_rating'] : null;
        $communication_rating = !empty($_POST['communication_rating']) ? (int)$_POST['communication_rating'] : null;
        $timeliness_rating = !empty($_POST['timeliness_rating']) ? (int)$_POST['timeliness_rating'] : null;
        $value_rating = !empty($_POST['value_rating']) ? (int)$_POST['value_rating'] : null;
        $recommend = isset($_POST['recommend']) ? (int)$_POST['recommend'] : 0;
        
        echo "<h4>Processed Values:</h4>";
        echo "<p><strong>Customer ID:</strong> " . $_SESSION['user_id'] . "</p>";
        echo "<p><strong>Contractor ID:</strong> $contractor_id</p>";
        echo "<p><strong>Payment ID:</strong> $payment_id</p>";
        echo "<p><strong>Quote Response ID:</strong> $quote_response_id</p>";
        echo "<p><strong>Rating:</strong> $rating</p>";
        echo "<p><strong>Review Text:</strong> " . htmlspecialchars($review_text) . "</p>";
        echo "<p><strong>Quality Rating:</strong> " . ($quality_rating ?: 'NULL') . "</p>";
        echo "<p><strong>Communication Rating:</strong> " . ($communication_rating ?: 'NULL') . "</p>";
        echo "<p><strong>Timeliness Rating:</strong> " . ($timeliness_rating ?: 'NULL') . "</p>";
        echo "<p><strong>Value Rating:</strong> " . ($value_rating ?: 'NULL') . "</p>";
        echo "<p><strong>Recommend:</strong> " . ($recommend ? 'Yes' : 'No') . "</p>";
        
        // Get quote_request_id from quote_response
        $stmt = $pdo->prepare("SELECT quote_request_id FROM quote_responses WHERE id = ?");
        $stmt->execute([$quote_response_id]);
        $quote_request_data = $stmt->fetch();
        $quote_request_id = $quote_request_data ? $quote_request_data['quote_request_id'] : null;

        echo "<p><strong>Quote Request ID:</strong> " . ($quote_request_id ?: 'NULL') . "</p>";

        // Test database insertion
        $pdo->beginTransaction();

        $stmt = $pdo->prepare("
            INSERT INTO reviews (
                customer_id, contractor_id, payment_id, quote_response_id, quote_request_id,
                rating, review_text, quality_rating, communication_rating,
                timeliness_rating, value_rating, recommend, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'approved')
        ");

        $result = $stmt->execute([
            $_SESSION['user_id'],
            $contractor_id,
            $payment_id,
            $quote_response_id,
            $quote_request_id,
            $rating,
            $review_text,
            $quality_rating,
            $communication_rating,
            $timeliness_rating,
            $value_rating,
            $recommend
        ]);
        
        if ($result) {
            $review_id = $pdo->lastInsertId();
            $pdo->commit();
            
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "<h4>✅ SUCCESS!</h4>";
            echo "<p>Review submitted successfully with ID: $review_id</p>";
            echo "</div>";
            
            // Show the inserted review
            $stmt = $pdo->prepare("SELECT * FROM reviews WHERE id = ?");
            $stmt->execute([$review_id]);
            $review = $stmt->fetch();
            
            echo "<h4>📋 Inserted Review Data:</h4>";
            echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            foreach ($review as $key => $value) {
                echo "<p><strong>$key:</strong> " . htmlspecialchars($value) . "</p>";
            }
            echo "</div>";
            
        } else {
            $pdo->rollBack();
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "<h4>❌ FAILED!</h4>";
            echo "<p>Review submission failed</p>";
            echo "</div>";
        }
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h4>❌ DATABASE ERROR!</h4>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

// Get test data
try {
    $stmt = $pdo->query("
        SELECT pp.*, qr.title, cp.business_name
        FROM project_payments pp
        JOIN quote_responses qres ON pp.quote_response_id = qres.id
        JOIN quote_requests qr ON qres.quote_request_id = qr.id
        JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
        WHERE pp.payment_status = 'completed'
        ORDER BY pp.created_at DESC
        LIMIT 1
    ");
    $payment = $stmt->fetch();
    
    if ($payment) {
        echo "<h3>🎯 Test Review Form</h3>";
        echo "<form method='POST' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        
        echo "<div style='margin: 10px 0;'>";
        echo "<label><strong>Overall Rating (1-5) *:</strong></label><br>";
        echo "<select name='rating' required style='padding: 8px; margin: 5px 0;'>";
        echo "<option value=''>Select rating</option>";
        echo "<option value='5'>5 - Excellent</option>";
        echo "<option value='4'>4 - Very Good</option>";
        echo "<option value='3'>3 - Good</option>";
        echo "<option value='2'>2 - Fair</option>";
        echo "<option value='1'>1 - Poor</option>";
        echo "</select>";
        echo "</div>";
        
        echo "<div style='margin: 10px 0;'>";
        echo "<label><strong>Review Text:</strong></label><br>";
        echo "<textarea name='review_text' rows='3' style='width: 100%; padding: 8px; margin: 5px 0;' placeholder='Great work! Professional and on time.'>Excellent work! Very professional and completed on time. Highly recommended!</textarea>";
        echo "</div>";
        
        echo "<div style='margin: 10px 0;'>";
        echo "<label><strong>Quality Rating:</strong></label><br>";
        echo "<select name='quality_rating' style='padding: 8px; margin: 5px 0;'>";
        echo "<option value=''>Optional</option>";
        echo "<option value='5' selected>5 - Excellent</option>";
        echo "<option value='4'>4 - Very Good</option>";
        echo "<option value='3'>3 - Good</option>";
        echo "<option value='2'>2 - Fair</option>";
        echo "<option value='1'>1 - Poor</option>";
        echo "</select>";
        echo "</div>";
        
        echo "<div style='margin: 10px 0;'>";
        echo "<label><strong>Communication Rating:</strong></label><br>";
        echo "<select name='communication_rating' style='padding: 8px; margin: 5px 0;'>";
        echo "<option value=''>Optional</option>";
        echo "<option value='5'>5 - Excellent</option>";
        echo "<option value='4' selected>4 - Very Good</option>";
        echo "<option value='3'>3 - Good</option>";
        echo "<option value='2'>2 - Fair</option>";
        echo "<option value='1'>1 - Poor</option>";
        echo "</select>";
        echo "</div>";
        
        echo "<div style='margin: 10px 0;'>";
        echo "<label><strong>Timeliness Rating:</strong></label><br>";
        echo "<select name='timeliness_rating' style='padding: 8px; margin: 5px 0;'>";
        echo "<option value=''>Optional</option>";
        echo "<option value='5' selected>5 - Excellent</option>";
        echo "<option value='4'>4 - Very Good</option>";
        echo "<option value='3'>3 - Good</option>";
        echo "<option value='2'>2 - Fair</option>";
        echo "<option value='1'>1 - Poor</option>";
        echo "</select>";
        echo "</div>";
        
        echo "<div style='margin: 10px 0;'>";
        echo "<label><strong>Value Rating:</strong></label><br>";
        echo "<select name='value_rating' style='padding: 8px; margin: 5px 0;'>";
        echo "<option value=''>Optional</option>";
        echo "<option value='5'>5 - Excellent</option>";
        echo "<option value='4' selected>4 - Very Good</option>";
        echo "<option value='3'>3 - Good</option>";
        echo "<option value='2'>2 - Fair</option>";
        echo "<option value='1'>1 - Poor</option>";
        echo "</select>";
        echo "</div>";
        
        echo "<div style='margin: 10px 0;'>";
        echo "<label><strong>Recommend:</strong></label><br>";
        echo "<label><input type='radio' name='recommend' value='1' checked> Yes, I would recommend</label><br>";
        echo "<label><input type='radio' name='recommend' value='0'> No, I would not recommend</label>";
        echo "</div>";
        
        echo "<input type='hidden' name='contractor_id' value='" . $payment['contractor_id'] . "'>";
        echo "<input type='hidden' name='payment_id' value='" . $payment['id'] . "'>";
        echo "<input type='hidden' name='quote_response_id' value='" . $payment['quote_response_id'] . "'>";
        
        echo "<div style='margin: 20px 0;'>";
        echo "<button type='submit' style='background: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>🚀 Test Review Submission</button>";
        echo "</div>";
        echo "</form>";
        
        echo "<p><strong>Test Data:</strong></p>";
        echo "<p>Payment ID: " . $payment['id'] . "</p>";
        echo "<p>Contractor ID: " . $payment['contractor_id'] . "</p>";
        echo "<p>Quote Response ID: " . $payment['quote_response_id'] . "</p>";
        echo "<p>Project: " . htmlspecialchars($payment['title']) . "</p>";
        
    } else {
        echo "<p style='color: red;'>No completed payments found for testing.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h2, h3, h4 {
    color: #333;
}
</style>
