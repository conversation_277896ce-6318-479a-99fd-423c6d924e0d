<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔧 Comprehensive Quote Issue Fix</h2>";

// Step 1: Check the latest quote request
echo "<h3>📋 Step 1: Latest Quote Request</h3>";
$stmt = $pdo->query("
    SELECT qr.*, sc.name_en as service_name, cp.first_name, cp.last_name
    FROM quote_requests qr
    LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
    LEFT JOIN customer_profiles cp ON qr.customer_id = cp.user_id
    ORDER BY qr.created_at DESC
    LIMIT 1
");
$latest_quote = $stmt->fetch();

if (!$latest_quote) {
    echo "<p>❌ No quote requests found! You need to create a quote first.</p>";
    exit;
}

echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0;'>";
echo "<h4>Latest Quote Details:</h4>";
echo "<p><strong>ID:</strong> {$latest_quote['id']}</p>";
echo "<p><strong>Title:</strong> " . htmlspecialchars($latest_quote['title']) . "</p>";
echo "<p><strong>Service:</strong> {$latest_quote['service_name']} (ID: {$latest_quote['service_category_id']})</p>";
echo "<p><strong>District:</strong> {$latest_quote['district']}</p>";
echo "<p><strong>Status:</strong> {$latest_quote['status']}</p>";
echo "<p><strong>Specific Contractor:</strong> " . ($latest_quote['specific_contractor_id'] ?: 'NULL (General Quote)') . "</p>";
echo "<p><strong>Created:</strong> {$latest_quote['created_at']}</p>";
echo "</div>";

// Step 2: Check the contractor
echo "<h3>👷 Step 2: Contractor Check</h3>";
$stmt = $pdo->prepare("
    SELECT u.id, u.email, u.status, u.user_type, cp.*
    FROM users u
    LEFT JOIN contractor_profiles cp ON u.id = cp.user_id
    WHERE u.email = ?
");
$stmt->execute(['<EMAIL>']);
$contractor = $stmt->fetch();

if (!$contractor) {
    echo "<p>❌ Contractor <EMAIL> not found!</p>";
    exit;
}

echo "<div style='background: #f0f0f0; padding: 15px; margin: 10px 0;'>";
echo "<h4>Contractor Details:</h4>";
echo "<p><strong>ID:</strong> {$contractor['id']}</p>";
echo "<p><strong>Email:</strong> {$contractor['email']}</p>";
echo "<p><strong>User Type:</strong> {$contractor['user_type']}</p>";
echo "<p><strong>Status:</strong> {$contractor['status']}</p>";
echo "<p><strong>Business Name:</strong> " . ($contractor['business_name'] ?: 'NULL') . "</p>";
echo "<p><strong>Service Types:</strong> " . ($contractor['service_types'] ?: 'NULL') . "</p>";
echo "<p><strong>Service Areas:</strong> " . ($contractor['service_areas'] ?: 'NULL') . "</p>";
echo "</div>";

// Step 3: Fix contractor issues
echo "<h3>🔧 Step 3: Fix Contractor Issues</h3>";

$fixes_needed = [];

// Check user type
if ($contractor['user_type'] !== 'contractor') {
    $fixes_needed[] = "User type is '{$contractor['user_type']}', should be 'contractor'";
}

// Check status
if ($contractor['status'] !== 'approved') {
    $fixes_needed[] = "Status is '{$contractor['status']}', should be 'approved'";
}

// Check profile completeness
if (!$contractor['business_name']) {
    $fixes_needed[] = "Missing business name";
}

if (!$contractor['service_types']) {
    $fixes_needed[] = "Missing service types";
}

if (!$contractor['service_areas']) {
    $fixes_needed[] = "Missing service areas";
}

if (count($fixes_needed) > 0) {
    echo "<p>❌ <strong>Issues found:</strong></p>";
    echo "<ul>";
    foreach ($fixes_needed as $fix) {
        echo "<li>{$fix}</li>";
    }
    echo "</ul>";
    
    // Apply fixes
    echo "<p>🔧 <strong>Applying fixes...</strong></p>";
    
    // Fix user type and status
    if ($contractor['user_type'] !== 'contractor' || $contractor['status'] !== 'approved') {
        $stmt = $pdo->prepare("UPDATE users SET user_type = 'contractor', status = 'approved' WHERE id = ?");
        $stmt->execute([$contractor['id']]);
        echo "<p>✅ Fixed user type and status</p>";
    }
    
    // Fix profile
    if (!$contractor['business_name'] || !$contractor['service_types'] || !$contractor['service_areas']) {
        // Get all service categories
        $stmt = $pdo->query("SELECT id, name_en FROM service_categories ORDER BY id");
        $categories = $stmt->fetchAll();
        
        // Find plumbing service (or use a general one)
        $service_ids = [];
        foreach ($categories as $category) {
            if (stripos($category['name_en'], 'plumb') !== false || 
                stripos($category['name_en'], 'sanit') !== false ||
                stripos($category['name_en'], 'electrical') !== false ||
                stripos($category['name_en'], 'construction') !== false) {
                $service_ids[] = $category['id'];
            }
        }
        
        // If no specific services found, use first few categories
        if (empty($service_ids)) {
            $service_ids = [1, 2, 3]; // Use first 3 categories
        }
        
        $business_name = $contractor['business_name'] ?: 'K Constructions';
        $service_types = json_encode($service_ids);
        $service_areas = json_encode(['Colombo', 'Gampaha', 'Kalutara']); // Include Kalutara
        
        if (!$contractor['business_name']) {
            $stmt = $pdo->prepare("UPDATE contractor_profiles SET business_name = ? WHERE user_id = ?");
            $stmt->execute([$business_name, $contractor['id']]);
            echo "<p>✅ Set business name to: {$business_name}</p>";
        }
        
        $stmt = $pdo->prepare("UPDATE contractor_profiles SET service_types = ?, service_areas = ? WHERE user_id = ?");
        $stmt->execute([$service_types, $service_areas, $contractor['id']]);
        echo "<p>✅ Updated service types: {$service_types}</p>";
        echo "<p>✅ Updated service areas: {$service_areas}</p>";
    }
    
    // Refresh contractor data
    $stmt = $pdo->prepare("
        SELECT u.id, u.email, u.status, u.user_type, cp.*
        FROM users u
        LEFT JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.email = ?
    ");
    $stmt->execute(['<EMAIL>']);
    $contractor = $stmt->fetch();
} else {
    echo "<p>✅ Contractor profile looks good!</p>";
}

// Step 4: Test quote matching
echo "<h3>🧪 Step 4: Test Quote Matching</h3>";

$contractor_services = json_decode($contractor['service_types'], true) ?: [];
$contractor_areas = json_decode($contractor['service_areas'], true) ?: [];

echo "<p><strong>Contractor Services:</strong> [" . implode(', ', $contractor_services) . "]</p>";
echo "<p><strong>Contractor Areas:</strong> [" . implode(', ', $contractor_areas) . "]</p>";

// Check if latest quote matches
$should_see_quote = false;
$reason = "";

if ($latest_quote['specific_contractor_id'] == $contractor['id']) {
    $should_see_quote = true;
    $reason = "Direct quote to this contractor";
} elseif ($latest_quote['specific_contractor_id'] === null && $latest_quote['status'] === 'open') {
    $has_service = in_array((int)$latest_quote['service_category_id'], $contractor_services) ||
                  in_array((string)$latest_quote['service_category_id'], $contractor_services);
    $has_area = in_array($latest_quote['district'], $contractor_areas);
    
    echo "<p><strong>Service Match:</strong> " . ($has_service ? '✅ YES' : '❌ NO') . 
         " (Looking for service ID {$latest_quote['service_category_id']} in contractor services)</p>";
    echo "<p><strong>Area Match:</strong> " . ($has_area ? '✅ YES' : '❌ NO') . 
         " (Looking for district '{$latest_quote['district']}' in contractor areas)</p>";
    
    if ($has_service && $has_area) {
        $should_see_quote = true;
        $reason = "Service and area match for general quote";
    } else {
        $reason = "Service match: " . ($has_service ? 'YES' : 'NO') . ", Area match: " . ($has_area ? 'YES' : 'NO');
        
        // Fix the mismatch
        if (!$has_service) {
            echo "<p>🔧 <strong>Fixing service mismatch...</strong></p>";
            $contractor_services[] = (int)$latest_quote['service_category_id'];
            $new_services = json_encode($contractor_services);
            $stmt = $pdo->prepare("UPDATE contractor_profiles SET service_types = ? WHERE user_id = ?");
            $stmt->execute([$new_services, $contractor['id']]);
            echo "<p>✅ Added service ID {$latest_quote['service_category_id']} to contractor profile</p>";
            $has_service = true;
        }
        
        if (!$has_area) {
            echo "<p>🔧 <strong>Fixing area mismatch...</strong></p>";
            $contractor_areas[] = $latest_quote['district'];
            $new_areas = json_encode($contractor_areas);
            $stmt = $pdo->prepare("UPDATE contractor_profiles SET service_areas = ? WHERE user_id = ?");
            $stmt->execute([$new_areas, $contractor['id']]);
            echo "<p>✅ Added area '{$latest_quote['district']}' to contractor profile</p>";
            $has_area = true;
        }
        
        if ($has_service && $has_area) {
            $should_see_quote = true;
            $reason = "Service and area match (after fixes)";
        }
    }
} else {
    $reason = "Quote status: {$latest_quote['status']}, Specific contractor: {$latest_quote['specific_contractor_id']}";
}

echo "<div style='background: " . ($should_see_quote ? '#d4edda' : '#f8d7da') . "; padding: 15px; margin: 10px 0;'>";
echo "<h4>Final Result:</h4>";
echo "<p><strong>Should contractor see this quote:</strong> " . ($should_see_quote ? '✅ YES' : '❌ NO') . "</p>";
echo "<p><strong>Reason:</strong> {$reason}</p>";
echo "</div>";

if ($should_see_quote) {
    echo "<h3>🎉 Success!</h3>";
    echo "<p>The contractor should now be able to see the quote. Please:</p>";
    echo "<ol>";
    echo "<li>Log <NAME_EMAIL></li>";
    echo "<li>Go to the contractor dashboard</li>";
    echo "<li>Check the 'Quote Requests' section</li>";
    echo "<li>The quote should appear there</li>";
    echo "</ol>";
} else {
    echo "<h3>❌ Still Issues</h3>";
    echo "<p>There are still issues preventing the contractor from seeing the quote. Please check:</p>";
    echo "<ul>";
    echo "<li>Quote status should be 'open'</li>";
    echo "<li>Contractor should be 'approved'</li>";
    echo "<li>Service and area should match</li>";
    echo "</ul>";
}
?>
