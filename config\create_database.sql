-- Create database
CREATE DATABASE IF NOT EXISTS brick_click CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE brick_click;

-- Users table (for both customers and contractors)
CREATE TABLE users (
    id INT NOT NULL AUTO_INCREMENT,
    email VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    user_type <PERSON>NU<PERSON>('customer', 'contractor', 'admin') NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Customer profiles
CREATE TABLE customer_profiles (
    id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    district VARCHAR(50),
    address TEXT,
    profile_image VARCHAR(255),
    language_preference ENUM('en', 'si', 'ta') DEFAULT 'en',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_district (district),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Contractor profiles
CREATE TABLE contractor_profiles (
    id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    business_name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    business_address TEXT,
    service_areas JSON, -- JSON array of districts
    service_types JSON, -- JSON array of service categories
    cida_registration VARCHAR(100),
    cida_grade ENUM('C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10'),
    cida_document VARCHAR(255), -- file path
    license_document VARCHAR(255), -- file path
    business_description TEXT,
    website VARCHAR(255),
    profile_image VARCHAR(255),
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    total_projects INT DEFAULT 0,
    language_preference ENUM('en', 'si', 'ta') DEFAULT 'en',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_cida_grade (cida_grade),
    INDEX idx_average_rating (average_rating),
    INDEX idx_business_name (business_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Service categories
CREATE TABLE service_categories (
    id INT NOT NULL AUTO_INCREMENT,
    name_en VARCHAR(100) NOT NULL,
    name_si VARCHAR(100),
    name_ta VARCHAR(100),
    description_en TEXT,
    description_si TEXT,
    description_ta TEXT,
    icon VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    INDEX idx_is_active (is_active),
    INDEX idx_name_en (name_en)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Quote requests
CREATE TABLE quote_requests (
    id INT NOT NULL AUTO_INCREMENT,
    customer_id INT NOT NULL,
    service_category_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    location VARCHAR(255) NOT NULL,
    district VARCHAR(50) NOT NULL,
    estimated_budget DECIMAL(12,2),
    project_timeline VARCHAR(100),
    status ENUM('open', 'closed', 'cancelled') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_category_id) REFERENCES service_categories(id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_service_category_id (service_category_id),
    INDEX idx_district (district),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Quote responses from contractors
CREATE TABLE quote_responses (
    id INT NOT NULL AUTO_INCREMENT,
    quote_request_id INT NOT NULL,
    contractor_id INT NOT NULL,
    quoted_amount DECIMAL(12,2) NOT NULL,
    estimated_timeline VARCHAR(100),
    description TEXT,
    terms_conditions TEXT,
    status ENUM('pending', 'accepted', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (quote_request_id) REFERENCES quote_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (contractor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_quote_request_id (quote_request_id),
    INDEX idx_contractor_id (contractor_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Customer favorites
CREATE TABLE customer_favorites (
    id INT NOT NULL AUTO_INCREMENT,
    customer_id INT NOT NULL,
    contractor_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (contractor_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_favorite (customer_id, contractor_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_contractor_id (contractor_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Reviews and ratings
CREATE TABLE reviews (
    id INT NOT NULL AUTO_INCREMENT,
    customer_id INT NOT NULL,
    contractor_id INT NOT NULL,
    quote_request_id INT NOT NULL,
    rating INT NOT NULL,
    review_text TEXT,
    is_verified BOOLEAN DEFAULT TRUE,
    is_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (contractor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (quote_request_id) REFERENCES quote_requests(id) ON DELETE CASCADE,
    UNIQUE KEY unique_review (customer_id, contractor_id, quote_request_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_contractor_id (contractor_id),
    INDEX idx_rating (rating),
    INDEX idx_is_approved (is_approved),
    CONSTRAINT chk_rating CHECK (rating >= 1 AND rating <= 5)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Contractor portfolios
CREATE TABLE contractor_portfolios (
    id INT NOT NULL AUTO_INCREMENT,
    contractor_id INT NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    project_description TEXT,
    project_location VARCHAR(255),
    completion_date DATE,
    project_value DECIMAL(12,2),
    project_images JSON, -- JSON array of image paths
    is_featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (contractor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_contractor_id (contractor_id),
    INDEX idx_is_featured (is_featured),
    INDEX idx_completion_date (completion_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Notifications
CREATE TABLE notifications (
    id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('quote_received', 'quote_response', 'approval_status', 'new_review', 'verification_update', 'payment_received', 'payment_success', 'review_received', 'general') NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    related_id INT, -- ID of related record (quote_request_id, quote_response_id, etc.)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Reviews and ratings table
CREATE TABLE reviews (
    id INT NOT NULL AUTO_INCREMENT,
    customer_id INT NOT NULL,
    contractor_id INT NOT NULL,
    payment_id INT NOT NULL,
    quote_response_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    quality_rating INT CHECK (quality_rating >= 1 AND quality_rating <= 5),
    communication_rating INT CHECK (communication_rating >= 1 AND communication_rating <= 5),
    timeliness_rating INT CHECK (timeliness_rating >= 1 AND timeliness_rating <= 5),
    value_rating INT CHECK (value_rating >= 1 AND value_rating <= 5),
    recommend BOOLEAN,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (contractor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (payment_id) REFERENCES project_payments(id) ON DELETE CASCADE,
    FOREIGN KEY (quote_response_id) REFERENCES quote_responses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_customer_contractor_payment (customer_id, contractor_id, payment_id),
    INDEX idx_contractor_id (contractor_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_rating (rating),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Admin settings
CREATE TABLE admin_settings (
    id INT NOT NULL AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Project payments and transactions
CREATE TABLE project_payments (
    id INT NOT NULL AUTO_INCREMENT,
    quote_response_id INT NOT NULL,
    customer_id INT NOT NULL,
    contractor_id INT NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    payment_type ENUM('down_payment', 'milestone', 'final_payment') NOT NULL,
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    transaction_id VARCHAR(255),
    payment_date TIMESTAMP NULL,
    payment_details JSON, -- Store payment method specific details
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (quote_response_id) REFERENCES quote_responses(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (contractor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_quote_response_id (quote_response_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_contractor_id (contractor_id),
    INDEX idx_payment_status (payment_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Cost estimator data
CREATE TABLE cost_estimator_data (
    id INT NOT NULL AUTO_INCREMENT,
    service_category_id INT NOT NULL,
    area_range VARCHAR(50) NOT NULL, -- e.g., '0-500', '500-1000', '1000-2000'
    building_size ENUM('small', 'medium', 'large', 'extra_large') NOT NULL,
    min_cost DECIMAL(12,2) NOT NULL,
    max_cost DECIMAL(12,2) NOT NULL,
    cost_per_sqft DECIMAL(8,2),
    description TEXT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (service_category_id) REFERENCES service_categories(id),
    INDEX idx_service_category_id (service_category_id),
    INDEX idx_building_size (building_size)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Language translations
CREATE TABLE translations (
    id INT NOT NULL AUTO_INCREMENT,
    translation_key VARCHAR(255) NOT NULL,
    language_code ENUM('en', 'si', 'ta') NOT NULL,
    translation_value TEXT NOT NULL,
    context VARCHAR(100), -- page or section context
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_translation (translation_key, language_code),
    INDEX idx_language_code (language_code),
    INDEX idx_context (context)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User sessions for remember me functionality
CREATE TABLE user_sessions (
    id INT NOT NULL AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_session_token (session_token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Admin activity logs
CREATE TABLE admin_logs (
    id INT NOT NULL AUTO_INCREMENT,
    admin_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50), -- 'user', 'contractor', 'review', etc.
    target_id INT,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_admin_id (admin_id),
    INDEX idx_action (action),
    INDEX idx_target_type (target_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System reports and analytics
CREATE TABLE system_reports (
    id INT NOT NULL AUTO_INCREMENT,
    report_type VARCHAR(50) NOT NULL,
    report_data JSON NOT NULL,
    generated_by INT,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    report_period_start DATE,
    report_period_end DATE,
    PRIMARY KEY (id),
    FOREIGN KEY (generated_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_report_type (report_type),
    INDEX idx_generated_at (generated_at),
    INDEX idx_report_period (report_period_start, report_period_end)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert construction-focused service categories
INSERT INTO service_categories (name_en, name_si, name_ta, description_en, icon) VALUES
('House Construction', 'ගෘහ ඉදිකිරීම', 'வீடு கட்டுமானம்', 'Complete house construction from foundation to finishing', 'fas fa-home'),
('Building Renovation', 'ගොඩනැගිලි අලුත්වැඩියා', 'கட்டிட புதுப்பித்தல்', 'Renovation and modernization of existing buildings', 'fas fa-tools'),
('Commercial Construction', 'වාණිජ ඉදිකිරීම්', 'வணிக கட்டுமானம்', 'Office buildings, shops, and commercial complexes', 'fas fa-building'),
('Interior Design & Finishing', 'අභ්‍යන්තර සැලසුම් සහ නිම කිරීම', 'உள்ளக வடிவமைப்பு மற்றும் முடித்தல்', 'Interior design, painting, and finishing work', 'fas fa-paint-roller'),
('Roofing & Waterproofing', 'වහල සහ ජල ආරක්ෂණ', 'கூரை மற்றும் நீர்ப்புகாமை', 'Roof construction, repair, and waterproofing services', 'fas fa-warehouse'),
('Electrical Work', 'විදුලි කාර්ය', 'மின் வேலை', 'Electrical installations and maintenance', 'fas fa-bolt'),
('Plumbing & Sanitation', 'ජල සම්බන්ධතා සහ සනීපාරක්ෂක', 'குழாய் மற்றும் சுகாதாரம்', 'Plumbing installations and sanitary work', 'fas fa-wrench'),
('Landscaping & Gardening', 'භූමි අලංකරණය සහ උද්‍යාන', 'நிலத்தை அலங்கரித்தல் மற்றும் தோட்டக்கலை', 'Garden design and landscaping services', 'fas fa-seedling'),
('Swimming Pool Construction', 'පිහිනුම් තටාක ඉදිකිරීම', 'நீச்சல் குளம் கட்டுமானம்', 'Swimming pool design and construction', 'fas fa-swimming-pool'),
('Road & Infrastructure', 'මාර්ග සහ යටිතල පහසුකම්', 'சாலை மற்றும் உள்கட்டமைப்பு', 'Road construction and infrastructure development', 'fas fa-road');

-- Insert default admin user (password: admin123)
INSERT INTO users (email, password, user_type, status) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'approved');

-- Insert admin settings
INSERT INTO admin_settings (setting_key, setting_value, description) VALUES
('site_name', 'Brick & Click', 'Website name'),
('site_email', '<EMAIL>', 'Contact email'),
('site_phone', '+94 77 123 4567', 'Contact phone'),
('contractor_approval_required', '1', 'Whether contractor registration requires admin approval'),
('review_moderation_required', '1', 'Whether reviews require admin approval before showing'),
('max_file_upload_size', '5242880', 'Maximum file upload size in bytes (5MB)'),
('allowed_file_types', 'pdf,jpg,jpeg,png', 'Allowed file types for uploads'),
('site_maintenance_mode', '0', 'Whether site is in maintenance mode'),
('default_language', 'en', 'Default site language'),
('currency_symbol', 'LKR', 'Currency symbol'),
('timezone', 'Asia/Colombo', 'Site timezone');

-- Insert comprehensive cost estimator data
INSERT INTO cost_estimator_data (service_category_id, area_range, building_size, min_cost, max_cost, cost_per_sqft, description) VALUES
-- House Construction
(1, '0-500', 'small', 1000000, 1500000, 2000, 'Small house construction up to 500 sqft'),
(1, '500-1000', 'medium', 1500000, 3000000, 2500, 'Medium house construction 500-1000 sqft'),
(1, '1000-2000', 'large', 3000000, 6000000, 3000, 'Large house construction 1000-2000 sqft'),
(1, '2000+', 'extra_large', 6000000, 12000000, 4000, 'Extra large house construction 2000+ sqft'),

-- Building Renovation
(2, '0-500', 'small', 300000, 600000, 800, 'Small building renovation projects'),
(2, '500-1000', 'medium', 600000, 1200000, 1000, 'Medium building renovation projects'),
(2, '1000-2000', 'large', 1200000, 2500000, 1200, 'Large building renovation projects'),
(2, '2000+', 'extra_large', 2500000, 5000000, 1500, 'Extra large building renovation projects'),

-- Commercial Construction
(3, '0-1000', 'small', 2000000, 4000000, 3000, 'Small commercial buildings'),
(3, '1000-3000', 'medium', 4000000, 10000000, 3500, 'Medium commercial buildings'),
(3, '3000-5000', 'large', 10000000, 20000000, 4000, 'Large commercial buildings'),
(3, '5000+', 'extra_large', 20000000, 50000000, 5000, 'Extra large commercial complexes'),

-- Interior Design & Finishing
(4, '0-500', 'small', 200000, 400000, 600, 'Small interior design projects'),
(4, '500-1000', 'medium', 400000, 800000, 700, 'Medium interior design projects'),
(4, '1000-2000', 'large', 800000, 1600000, 800, 'Large interior design projects'),
(4, '2000+', 'extra_large', 1600000, 3500000, 1000, 'Extra large interior design projects'),

-- Roofing & Waterproofing
(5, '0-500', 'small', 150000, 300000, 400, 'Small roofing & waterproofing projects'),
(5, '500-1000', 'medium', 300000, 600000, 500, 'Medium roofing & waterproofing projects'),
(5, '1000-2000', 'large', 600000, 1200000, 600, 'Large roofing & waterproofing projects'),
(5, '2000+', 'extra_large', 1200000, 2500000, 700, 'Extra large roofing & waterproofing projects'),

-- Electrical Work
(6, '0-500', 'small', 100000, 200000, 300, 'Small electrical installation projects'),
(6, '500-1000', 'medium', 200000, 400000, 350, 'Medium electrical installation projects'),
(6, '1000-2000', 'large', 400000, 800000, 400, 'Large electrical installation projects'),
(6, '2000+', 'extra_large', 800000, 1600000, 500, 'Extra large electrical installation projects'),

-- Plumbing & Sanitation
(7, '0-500', 'small', 80000, 160000, 250, 'Small plumbing & sanitation projects'),
(7, '500-1000', 'medium', 160000, 320000, 300, 'Medium plumbing & sanitation projects'),
(7, '1000-2000', 'large', 320000, 640000, 350, 'Large plumbing & sanitation projects'),
(7, '2000+', 'extra_large', 640000, 1300000, 400, 'Extra large plumbing & sanitation projects'),

-- Landscaping & Gardening
(8, '0-1000', 'small', 50000, 150000, 100, 'Small landscaping projects'),
(8, '1000-3000', 'medium', 150000, 400000, 150, 'Medium landscaping projects'),
(8, '3000-5000', 'large', 400000, 800000, 200, 'Large landscaping projects'),
(8, '5000+', 'extra_large', 800000, 2000000, 250, 'Extra large landscaping projects'),

-- Swimming Pool Construction
(9, 'small', 'small', 800000, 1500000, 0, 'Small swimming pool (up to 200 sqft)'),
(9, 'medium', 'medium', 1500000, 2500000, 0, 'Medium swimming pool (200-400 sqft)'),
(9, 'large', 'large', 2500000, 4000000, 0, 'Large swimming pool (400-600 sqft)'),
(9, 'extra_large', 'extra_large', 4000000, 8000000, 0, 'Extra large swimming pool (600+ sqft)'),

-- Road & Infrastructure
(10, '0-1000', 'small', 2000000, 5000000, 0, 'Small road construction projects'),
(10, '1000-5000', 'medium', 5000000, 15000000, 0, 'Medium road construction projects'),
(10, '5000-10000', 'large', 15000000, 40000000, 0, 'Large road construction projects'),
(10, '10000+', 'extra_large', 40000000, 100000000, 0, 'Extra large infrastructure projects');

-- Insert basic translations for multilingual support
INSERT INTO translations (translation_key, language_code, translation_value, context) VALUES
-- Common terms
('welcome', 'en', 'Welcome', 'common'),
('welcome', 'si', 'ආයුබෝවන්', 'common'),
('welcome', 'ta', 'வரவேற்கிறோம்', 'common'),

('home', 'en', 'Home', 'navigation'),
('home', 'si', 'මුල් පිටුව', 'navigation'),
('home', 'ta', 'முகப்பு', 'navigation'),

('about', 'en', 'About', 'navigation'),
('about', 'si', 'අප ගැන', 'navigation'),
('about', 'ta', 'எங்களை பற்றி', 'navigation'),

('services', 'en', 'Services', 'navigation'),
('services', 'si', 'සේවා', 'navigation'),
('services', 'ta', 'சேவைகள்', 'navigation'),

('contact', 'en', 'Contact', 'navigation'),
('contact', 'si', 'සම්බන්ධ වන්න', 'navigation'),
('contact', 'ta', 'தொடர்பு', 'navigation'),

('login', 'en', 'Login', 'auth'),
('login', 'si', 'ප්‍රවේශ වන්න', 'auth'),
('login', 'ta', 'உள்நுழைய', 'auth'),

('signup', 'en', 'Sign Up', 'auth'),
('signup', 'si', 'ලියාපදිංචි වන්න', 'auth'),
('signup', 'ta', 'பதிவு செய்யுங்கள்', 'auth'),

('find_contractors', 'en', 'Find Contractors', 'customer'),
('find_contractors', 'si', 'කොන්ත්‍රාත්කරුවන් සොයන්න', 'customer'),
('find_contractors', 'ta', 'ஒப்பந்தக்காரர்களைக் கண்டறியுங்கள்', 'customer'),

('request_quote', 'en', 'Request Quote', 'customer'),
('request_quote', 'si', 'මිල ගණන් ඉල්ලීම', 'customer'),
('request_quote', 'ta', 'மேற்கோள் கோரிக்கை', 'customer'),

('my_quotes', 'en', 'My Quotes', 'customer'),
('my_quotes', 'si', 'මගේ මිල ගණන්', 'customer'),
('my_quotes', 'ta', 'எனது மேற்கோள்கள்', 'customer'),

('favorites', 'en', 'Favorites', 'customer'),
('favorites', 'si', 'ප්‍රියතම', 'customer'),
('favorites', 'ta', 'பிடித்தவை', 'customer'),

('profile', 'en', 'Profile', 'common'),
('profile', 'si', 'පැතිකඩ', 'common'),
('profile', 'ta', 'சுயவிவரம்', 'common');

-- Create customer_notification_preferences table
CREATE TABLE customer_notification_preferences (
    id INT AUTO_INCREMENT,
    user_id INT NOT NULL,
    quote_notifications TINYINT(1) DEFAULT 1,
    message_notifications TINYINT(1) DEFAULT 1,
    update_notifications TINYINT(1) DEFAULT 1,
    email_notifications TINYINT(1) DEFAULT 1,
    sms_notifications TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_customer_notification_prefs (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
