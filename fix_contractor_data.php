<?php
require_once 'config/database.php';

echo "<h2>🔧 Fix Contractor Data for General Quotes</h2>";

try {
    // Step 1: Check current contractor data
    echo "<h3>Step 1: Current Contractor Data</h3>";
    $stmt = $pdo->query("
        SELECT u.id, u.email, u.status, cp.business_name, cp.service_areas, cp.service_types
        FROM users u 
        LEFT JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor'
        ORDER BY u.status, u.id
    ");
    $contractors = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Email</th><th>Status</th><th>Business Name</th><th>Service Areas</th><th>Service Types</th><th>Issues</th></tr>";
    
    $contractors_to_fix = [];
    
    foreach ($contractors as $contractor) {
        $issues = [];
        
        if (!$contractor['business_name']) {
            $issues[] = "No business name";
        }
        
        $areas_valid = false;
        if (!$contractor['service_areas'] || $contractor['service_areas'] === '[]' || $contractor['service_areas'] === '') {
            $issues[] = "No service areas";
        } else {
            $areas = json_decode($contractor['service_areas'], true);
            if (!is_array($areas) || empty($areas)) {
                $issues[] = "Invalid service areas JSON";
            } else {
                $areas_valid = true;
            }
        }
        
        $types_valid = false;
        if (!$contractor['service_types'] || $contractor['service_types'] === '[]' || $contractor['service_types'] === '') {
            $issues[] = "No service types";
        } else {
            $types = json_decode($contractor['service_types'], true);
            if (!is_array($types) || empty($types)) {
                $issues[] = "Invalid service types JSON";
            } else {
                $types_valid = true;
            }
        }
        
        if ($contractor['status'] === 'approved' && (!$areas_valid || !$types_valid)) {
            $contractors_to_fix[] = $contractor;
        }
        
        $issues_text = empty($issues) ? "✅ OK" : "❌ " . implode(", ", $issues);
        $row_color = empty($issues) && $contractor['status'] === 'approved' ? 'background-color: #d4edda;' : '';
        
        echo "<tr style='$row_color'>";
        echo "<td>" . $contractor['id'] . "</td>";
        echo "<td>" . htmlspecialchars($contractor['email']) . "</td>";
        echo "<td>" . $contractor['status'] . "</td>";
        echo "<td>" . htmlspecialchars($contractor['business_name'] ?: 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars(substr($contractor['service_areas'] ?: 'N/A', 0, 50)) . "</td>";
        echo "<td>" . htmlspecialchars(substr($contractor['service_types'] ?: 'N/A', 0, 50)) . "</td>";
        echo "<td>$issues_text</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 2: Fix contractors with missing data
    if (count($contractors_to_fix) > 0) {
        echo "<h3>Step 2: Fix Contractors with Missing Data</h3>";
        
        foreach ($contractors_to_fix as $contractor) {
            echo "<p>Fixing contractor: " . htmlspecialchars($contractor['business_name'] ?: $contractor['email']) . " (ID: {$contractor['id']})</p>";
            
            $updates = [];
            $params = [];
            
            // Fix service areas if missing or invalid
            $areas = json_decode($contractor['service_areas'], true);
            if (!is_array($areas) || empty($areas)) {
                $default_areas = ['Colombo', 'Gampaha', 'Kalutara']; // Default areas
                $updates[] = "service_areas = ?";
                $params[] = json_encode($default_areas);
                echo "  - Setting default service areas: " . implode(', ', $default_areas) . "<br>";
            }
            
            // Fix service types if missing or invalid
            $types = json_decode($contractor['service_types'], true);
            if (!is_array($types) || empty($types)) {
                $default_types = [1, 2, 3]; // Default service types (House Construction, Building Renovation, Commercial Construction)
                $updates[] = "service_types = ?";
                $params[] = json_encode($default_types);
                echo "  - Setting default service types: " . implode(', ', $default_types) . "<br>";
            }
            
            if (!empty($updates)) {
                $params[] = $contractor['id'];
                $sql = "UPDATE contractor_profiles SET " . implode(', ', $updates) . " WHERE user_id = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                echo "  - ✅ Updated contractor data<br>";
            }
        }
    }
    
    // Step 3: Create test contractors if none exist
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        AND cp.service_areas IS NOT NULL AND cp.service_areas != '' AND cp.service_areas != '[]'
        AND cp.service_types IS NOT NULL AND cp.service_types != '' AND cp.service_types != '[]'
    ");
    $valid_contractors = $stmt->fetchColumn();
    
    if ($valid_contractors == 0) {
        echo "<h3>Step 3: Create Test Contractors</h3>";
        
        $test_contractors = [
            [
                'email' => '<EMAIL>',
                'business_name' => 'ABC Construction',
                'contact_person' => 'John Doe',
                'phone' => '0771234567',
                'areas' => ['Colombo', 'Gampaha'],
                'services' => [1, 2, 3] // House Construction, Building Renovation, Commercial Construction
            ],
            [
                'email' => '<EMAIL>',
                'business_name' => 'XYZ Builders',
                'contact_person' => 'Jane Smith',
                'phone' => '0771234568',
                'areas' => ['Gampaha', 'Kalutara'],
                'services' => [1, 4, 5] // House Construction, Interior Design, Roofing
            ],
            [
                'email' => '<EMAIL>',
                'business_name' => 'Best Renovations',
                'contact_person' => 'Mike Johnson',
                'phone' => '0771234569',
                'areas' => ['Colombo', 'Kalutara'],
                'services' => [2, 6, 7] // Building Renovation, Electrical, Plumbing
            ]
        ];
        
        foreach ($test_contractors as $contractor_data) {
            // Check if contractor already exists
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$contractor_data['email']]);
            $existing_user = $stmt->fetch();
            
            if (!$existing_user) {
                // Create user
                $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, 'contractor', 'approved')");
                $stmt->execute([$contractor_data['email'], password_hash('password', PASSWORD_DEFAULT)]);
                $user_id = $pdo->lastInsertId();
                
                // Create contractor profile
                $stmt = $pdo->prepare("
                    INSERT INTO contractor_profiles (
                        user_id, business_name, contact_person, phone, 
                        service_areas, service_types, cida_grade, 
                        business_registration, years_experience
                    ) VALUES (?, ?, ?, ?, ?, ?, 'A', 'REG123456', 5)
                ");
                $stmt->execute([
                    $user_id,
                    $contractor_data['business_name'],
                    $contractor_data['contact_person'],
                    $contractor_data['phone'],
                    json_encode($contractor_data['areas']),
                    json_encode($contractor_data['services'])
                ]);
                
                echo "<p>✅ Created test contractor: " . htmlspecialchars($contractor_data['business_name']) . " (ID: $user_id)</p>";
                echo "  - Areas: " . implode(', ', $contractor_data['areas']) . "<br>";
                echo "  - Services: " . implode(', ', $contractor_data['services']) . "<br>";
            } else {
                echo "<p>⚠️ Contractor already exists: " . htmlspecialchars($contractor_data['email']) . "</p>";
            }
        }
    }
    
    // Step 4: Final verification
    echo "<h3>Step 4: Final Verification</h3>";
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        AND cp.service_areas IS NOT NULL AND cp.service_areas != '' AND cp.service_areas != '[]'
        AND cp.service_types IS NOT NULL AND cp.service_types != '' AND cp.service_types != '[]'
    ");
    $final_count = $stmt->fetchColumn();
    
    echo "<p style='color: green; font-size: 18px;'><strong>✅ {$final_count} contractors are now ready for general quotes!</strong></p>";
    
    if ($final_count > 0) {
        echo "<h4>Next Steps:</h4>";
        echo "<ul>";
        echo "<li><a href='test_general_quote_submission.php'>Test general quote submission</a></li>";
        echo "<li><a href='customer/request_quote.php'>Submit a real general quote</a></li>";
        echo "<li><a href='contractor/quotes.php'>Check contractor quotes page</a></li>";
        echo "</ul>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
