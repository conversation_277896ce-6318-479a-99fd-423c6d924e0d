<?php
require_once 'config/database.php';

echo "<h2>🗄️ Database Schema Check</h2>";

try {
    // Check all tables
    echo "<h3>All Tables in Database</h3>";
    $stmt = $pdo->prepare("SHOW TABLES");
    $stmt->execute();
    $tables = $stmt->fetchAll();
    
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>" . $table[0] . "</li>";
    }
    echo "</ul>";
    
    // Check reviews table structure
    echo "<h3>Reviews Table Structure</h3>";
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'reviews'");
    $stmt->execute();
    $reviews_exists = $stmt->fetch();
    
    if ($reviews_exists) {
        echo "<p>✅ Reviews table exists</p>";
        
        $stmt = $pdo->prepare("DESCRIBE reviews");
        $stmt->execute();
        $columns = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check data in reviews table
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM reviews");
        $stmt->execute();
        $review_count = $stmt->fetchColumn();
        echo "<p>Total reviews in table: $review_count</p>";
        
        if ($review_count > 0) {
            $stmt = $pdo->prepare("SELECT * FROM reviews LIMIT 5");
            $stmt->execute();
            $sample_reviews = $stmt->fetchAll();
            echo "<h4>Sample Reviews:</h4>";
            echo "<pre>" . print_r($sample_reviews, true) . "</pre>";
        }
    } else {
        echo "<p>❌ Reviews table does not exist</p>";
    }
    
    // Check quote_responses table
    echo "<h3>Quote Responses Table</h3>";
    $stmt = $pdo->prepare("DESCRIBE quote_responses");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM quote_responses");
    $stmt->execute();
    $response_count = $stmt->fetchColumn();
    echo "<p>Total quote responses: $response_count</p>";
    
    // Check quote_requests table
    echo "<h3>Quote Requests Table</h3>";
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM quote_requests");
    $stmt->execute();
    $request_count = $stmt->fetchColumn();
    echo "<p>Total quote requests: $request_count</p>";
    
    // Check contractor_portfolios table
    echo "<h3>Contractor Portfolios Table</h3>";
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM contractor_portfolios");
    $stmt->execute();
    $portfolio_count = $stmt->fetchColumn();
    echo "<p>Total portfolio items: $portfolio_count</p>";
    
    // Check service_categories table
    echo "<h3>Service Categories Table</h3>";
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'service_categories'");
    $stmt->execute();
    $service_categories_exists = $stmt->fetch();
    
    if ($service_categories_exists) {
        echo "<p>✅ Service categories table exists</p>";
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM service_categories");
        $stmt->execute();
        $category_count = $stmt->fetchColumn();
        echo "<p>Total service categories: $category_count</p>";
    } else {
        echo "<p>❌ Service categories table does not exist</p>";
    }
    
    // Check customer_profiles table
    echo "<h3>Customer Profiles Table</h3>";
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM customer_profiles");
    $stmt->execute();
    $customer_count = $stmt->fetchColumn();
    echo "<p>Total customer profiles: $customer_count</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Database Schema Check</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        table { margin: 20px 0; width: 100%; }
        th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
</body>
</html>
