<?php
session_start();
require_once 'config/database.php';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: login.php');
    exit();
}

$email = trim($_POST['email']);
$password = $_POST['password'];
$remember_me = isset($_POST['remember_me']);

// Basic validation
if (empty($email) || empty($password)) {
    $_SESSION['error'] = 'Please enter both email and password.';
    header('Location: login.php');
    exit();
}

try {
    // Get user from database
    $stmt = $pdo->prepare("SELECT id, email, password, user_type, status FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if (!$user || !password_verify($password, $user['password'])) {
        $_SESSION['error'] = 'Invalid email or password.';
        header('Location: login.php');
        exit();
    }
    
    // Check user status
    if ($user['status'] === 'pending') {
        $_SESSION['error'] = 'Your account is pending approval. Please wait for admin verification.';
        header('Location: login.php');
        exit();
    }
    
    if ($user['status'] === 'rejected') {
        $_SESSION['error'] = 'Your account has been rejected. Please contact support for more information.';
        header('Location: login.php');
        exit();
    }
    
    if ($user['status'] === 'suspended') {
        $_SESSION['error'] = 'Your account has been suspended. Please contact support.';
        header('Location: login.php');
        exit();
    }
    
    // Set session variables
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_type'] = $user['user_type'];
    $_SESSION['user_email'] = $user['email'];
    
    // Set remember me cookie if requested
    if ($remember_me) {
        $token = bin2hex(random_bytes(32));
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 days
        
        // Store token in database (you might want to create a remember_tokens table)
        // For now, we'll skip this implementation
    }
    
    // Redirect based on user type
    switch ($user['user_type']) {
        case 'customer':
            header('Location: customer/dashboard.php');
            break;
        case 'contractor':
            header('Location: contractor/dashboard.php');
            break;
        case 'admin':
            header('Location: admin/dashboard.php');
            break;
        default:
            $_SESSION['error'] = 'Invalid user type.';
            header('Location: login.php');
            break;
    }
    exit();
    
} catch (PDOException $e) {
    $_SESSION['error'] = 'Login failed. Please try again.';
    header('Location: login.php');
    exit();
}
?>
