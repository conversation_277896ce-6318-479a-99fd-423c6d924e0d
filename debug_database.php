<?php
require_once 'config/database.php';

echo "<h2>Database Debug Information</h2>";

try {
    // Check service_categories table
    echo "<h3>Service Categories Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE service_categories");
    $columns = $stmt->fetchAll();
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Check service categories data
    echo "<h3>Service Categories Data:</h3>";
    $stmt = $pdo->query("SELECT * FROM service_categories");
    $categories = $stmt->fetchAll();
    echo "Total categories: " . count($categories) . "<br>";
    if (!empty($categories)) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Name EN</th><th>Name SI</th><th>Name TA</th></tr>";
        foreach ($categories as $cat) {
            echo "<tr>";
            echo "<td>" . $cat['id'] . "</td>";
            echo "<td>" . ($cat['name_en'] ?? 'NULL') . "</td>";
            echo "<td>" . ($cat['name_si'] ?? 'NULL') . "</td>";
            echo "<td>" . ($cat['name_ta'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Test the quote request query that's failing
    echo "<h3>Testing Quote Request Query:</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM quote_requests");
    $count = $stmt->fetch();
    echo "Total quote requests: " . $count['total'] . "<br>";

    if ($count['total'] > 0) {
        // Get a sample quote request
        $stmt = $pdo->query("SELECT * FROM quote_requests ORDER BY created_at DESC LIMIT 1");
        $sample_quote = $stmt->fetch();
        echo "Sample quote ID: " . $sample_quote['id'] . "<br>";
        echo "Service category ID: " . $sample_quote['service_category_id'] . "<br>";

        // Test the JOIN that's failing
        try {
            $stmt = $pdo->prepare("
                SELECT qr.*, sc.name_en as service_name
                FROM quote_requests qr
                JOIN service_categories sc ON qr.service_category_id = sc.id
                WHERE qr.id = ?
            ");
            $stmt->execute([$sample_quote['id']]);
            $result = $stmt->fetch();
            if ($result) {
                echo "Quote request JOIN successful<br>";
                echo "Service name: " . $result['service_name'] . "<br>";
            } else {
                echo "Quote request JOIN returned no results<br>";
            }
        } catch (PDOException $e) {
            echo "Quote request JOIN failed: " . $e->getMessage() . "<br>";
        }
    }
    // Check if quote_responses table exists and its structure
    echo "<h3>Quote Responses Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE quote_responses");
    $columns = $stmt->fetchAll();
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Check contractor_profiles table structure
    echo "<h3>Contractor Profiles Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE contractor_profiles");
    $columns = $stmt->fetchAll();
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Count total quote responses
    echo "<h3>Quote Responses Count:</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM quote_responses");
    $count = $stmt->fetch();
    echo "Total quote responses: " . $count['total'] . "<br>";

    // Show all quote responses
    echo "<h3>All Quote Responses:</h3>";
    $stmt = $pdo->query("SELECT * FROM quote_responses ORDER BY created_at DESC");
    $responses = $stmt->fetchAll();
    if (empty($responses)) {
        echo "No quote responses found.<br>";
    } else {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Quote Request ID</th><th>Contractor ID</th><th>Amount</th><th>Status</th><th>Created</th></tr>";
        foreach ($responses as $response) {
            echo "<tr>";
            echo "<td>" . $response['id'] . "</td>";
            echo "<td>" . $response['quote_request_id'] . "</td>";
            echo "<td>" . $response['contractor_id'] . "</td>";
            echo "<td>" . $response['quoted_amount'] . "</td>";
            echo "<td>" . $response['status'] . "</td>";
            echo "<td>" . $response['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Check if there are any contractor profiles
    echo "<h3>Contractor Profiles Count:</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM contractor_profiles");
    $count = $stmt->fetch();
    echo "Total contractor profiles: " . $count['total'] . "<br>";

    // Test the JOIN query that's failing
    echo "<h3>Testing JOIN Query:</h3>";
    try {
        $stmt = $pdo->prepare("
            SELECT qres.*, cp.business_name, cp.contact_person, cp.phone, cp.profile_image,
                   cp.average_rating, cp.total_reviews, cp.cida_grade
            FROM quote_responses qres
            JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
            ORDER BY qres.created_at DESC
        ");
        $stmt->execute();
        $results = $stmt->fetchAll();
        echo "JOIN query successful. Results: " . count($results) . "<br>";
        
        if (!empty($results)) {
            echo "<table border='1'>";
            echo "<tr><th>Response ID</th><th>Business Name</th><th>Contact Person</th><th>Amount</th></tr>";
            foreach ($results as $result) {
                echo "<tr>";
                echo "<td>" . $result['id'] . "</td>";
                echo "<td>" . ($result['business_name'] ?? 'NULL') . "</td>";
                echo "<td>" . ($result['contact_person'] ?? 'NULL') . "</td>";
                echo "<td>" . $result['quoted_amount'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (PDOException $e) {
        echo "JOIN query failed: " . $e->getMessage() . "<br>";
    }

} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage();
}
?>
