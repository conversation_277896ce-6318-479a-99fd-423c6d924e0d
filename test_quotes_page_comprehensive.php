<?php
session_start();
require_once 'config/database.php';

echo "<h2>🧪 Comprehensive Quotes Page Test</h2>";

try {
    // Step 1: Get test contractor
    $stmt = $pdo->query("
        SELECT u.id, u.email, cp.business_name, cp.service_areas, cp.service_types, u.status
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved' 
        LIMIT 1
    ");
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p style='color: red;'>❌ No approved contractors found.</p>";
        exit;
    }
    
    $contractor_id = $contractor['id'];
    echo "<p><strong>Testing with contractor:</strong> " . htmlspecialchars($contractor['business_name']) . " (ID: $contractor_id)</p>";
    
    // Step 2: Create test quotes if none exist
    echo "<h3>Step 2: Ensure Test Data Exists</h3>";
    
    // Check if we have quotes
    $stmt = $pdo->query("SELECT COUNT(*) FROM quote_requests");
    $total_quotes = $stmt->fetchColumn();
    
    if ($total_quotes == 0) {
        echo "<p>No quotes found. Creating test quotes...</p>";
        
        // Get test customer
        $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' AND status = 'approved' LIMIT 1");
        $customer_id = $stmt->fetchColumn();
        
        if (!$customer_id) {
            echo "<p style='color: red;'>❌ No customers found to create test quotes</p>";
            exit;
        }
        
        // Get service category
        $stmt = $pdo->query("SELECT id FROM service_categories LIMIT 1");
        $service_id = $stmt->fetchColumn();
        
        if (!$service_id) {
            echo "<p style='color: red;'>❌ No service categories found</p>";
            exit;
        }
        
        // Create test quotes
        $test_quotes = [
            [
                'title' => 'Test General Quote 1',
                'description' => 'Test description for general quote',
                'district' => 'Colombo',
                'specific_contractor_id' => null
            ],
            [
                'title' => 'Test Specific Quote',
                'description' => 'Test description for specific quote',
                'district' => 'Colombo',
                'specific_contractor_id' => $contractor_id
            ],
            [
                'title' => 'Test General Quote 2',
                'description' => 'Another test description',
                'district' => 'Gampaha',
                'specific_contractor_id' => null
            ]
        ];
        
        foreach ($test_quotes as $quote) {
            $stmt = $pdo->prepare("
                INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, specific_contractor_id, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'open')
            ");
            $stmt->execute([
                $customer_id, 
                $service_id, 
                $quote['title'], 
                $quote['description'], 
                'Test Location', 
                $quote['district'], 
                1000000, 
                '3 months', 
                $quote['specific_contractor_id']
            ]);
            echo "<p>✅ Created: " . $quote['title'] . "</p>";
        }
    } else {
        echo "<p>✅ Found $total_quotes existing quotes</p>";
    }
    
    // Step 3: Test quotes page functionality
    echo "<h3>Step 3: Test Quotes Page Functionality</h3>";
    
    // Simulate session
    $_SESSION['user_id'] = $contractor_id;
    $_SESSION['user_type'] = 'contractor';
    
    // Test each status filter
    $status_filters = ['all', 'pending', 'responded', 'completed', 'cancelled'];
    $results = [];
    
    foreach ($status_filters as $status_filter) {
        echo "<h4>Testing '$status_filter' filter</h4>";
        
        // Simulate the exact logic from quotes.php
        $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
        $params = [$contractor_id];
        
        if ($status_filter !== 'all') {
            if ($status_filter === 'responded') {
                $where_conditions[] = "qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?)";
                $params[] = $contractor_id;
            } elseif ($status_filter === 'pending') {
                $where_conditions[] = "qr.status = 'open' AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)";
                $params[] = $contractor_id;
            } elseif ($status_filter === 'completed') {
                $where_conditions[] = "qr.id IN (
                    SELECT qres.quote_request_id
                    FROM quote_responses qres
                    JOIN project_payments pp ON qres.id = pp.quote_response_id
                    WHERE qres.contractor_id = ?
                    AND pp.payment_type = 'down_payment'
                    AND pp.payment_status = 'completed'
                )";
                $params[] = $contractor_id;
            } elseif ($status_filter === 'cancelled') {
                $where_conditions[] = "qr.id IN (
                    SELECT quote_request_id
                    FROM quote_responses
                    WHERE contractor_id = ?
                    AND status = 'rejected'
                )";
                $params[] = $contractor_id;
            }
        } else {
            $where_conditions[] = "(qr.status IN ('open', 'completed', 'cancelled') OR qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?))";
            $params[] = $contractor_id;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        // Execute the query
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
                   (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
                   (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
                   (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
                   CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
                   cp_contractor.service_types, cp_contractor.service_areas
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
            JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
            WHERE $where_clause
            ORDER BY qr.created_at DESC
        ");
        
        $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
        $stmt->execute($query_params);
        $all_quotes = $stmt->fetchAll();
        
        // Filter in PHP
        $filtered_quotes = [];
        foreach ($all_quotes as $quote) {
            if ($quote['specific_contractor_id'] == $contractor_id) {
                $filtered_quotes[] = $quote;
            } elseif ($quote['specific_contractor_id'] === null) {
                $contractor_services = json_decode($quote['service_types'], true) ?: [];
                $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
                
                $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                              in_array((string)$quote['service_category_id'], $contractor_services);
                $has_area = in_array($quote['district'], $contractor_areas);
                
                if ($has_service && $has_area) {
                    $filtered_quotes[] = $quote;
                }
            }
        }
        
        $results[$status_filter] = count($filtered_quotes);
        echo "<p>SQL returned: " . count($all_quotes) . " quotes</p>";
        echo "<p>After filtering: " . count($filtered_quotes) . " quotes</p>";
        
        // Show sample quotes
        if (count($filtered_quotes) > 0) {
            echo "<p><strong>Sample quotes:</strong></p>";
            foreach (array_slice($filtered_quotes, 0, 3) as $quote) {
                echo "<div style='border: 1px solid green; padding: 5px; margin: 2px;'>";
                echo "ID: " . $quote['id'] . " | ";
                echo "Title: " . htmlspecialchars($quote['title']) . " | ";
                echo "Type: " . $quote['quote_type'] . " | ";
                echo "Status: " . $quote['status'];
                echo "</div>";
            }
        }
        echo "<hr>";
    }
    
    // Step 4: Test search functionality
    echo "<h3>Step 4: Test Search Functionality</h3>";
    
    $search_term = "Test";
    echo "<p>Testing search for: '$search_term'</p>";
    
    $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
    $params = [$contractor_id];
    
    // Add search condition (fixed version)
    $where_conditions[] = "(qr.title LIKE ? OR qr.description LIKE ? OR cp.first_name LIKE ? OR cp.last_name LIKE ? OR sc.name_en LIKE ?)";
    $search_param = "%$search_term%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
               cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");
    
    $search_query_params = array_merge([$contractor_id, $contractor_id, $contractor_id], $params);
    $stmt->execute($search_query_params);
    $search_results = $stmt->fetchAll();
    
    echo "<p>Search returned: " . count($search_results) . " quotes</p>";
    
    // Step 5: Summary
    echo "<h3>🎯 Test Results Summary</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Filter</th><th>Quote Count</th><th>Status</th></tr>";
    
    foreach ($results as $filter => $count) {
        $status = $count > 0 ? "✅ Working" : "⚠️ No quotes";
        echo "<tr><td>$filter</td><td>$count</td><td>$status</td></tr>";
    }
    echo "</table>";
    
    echo "<p><strong>Search functionality:</strong> " . (count($search_results) > 0 ? "✅ Working" : "⚠️ No results") . "</p>";
    
    if (array_sum($results) > 0) {
        echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 Quotes page is working! Contractor should see quotes.</p>";
    } else {
        echo "<p style='color: red; font-weight: bold; font-size: 18px;'>❌ No quotes visible to contractor. Check service/area matching.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
