<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔍 Debug Dashboard vs Quotes Page Mismatch</h2>";

try {
    // Get test contractor
    $stmt = $pdo->query("
        SELECT u.id, u.email, cp.business_name 
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved' 
        LIMIT 1
    ");
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p style='color: red;'>❌ No contractors found.</p>";
        exit;
    }
    
    $contractor_id = $contractor['id'];
    echo "<p><strong>Testing with contractor:</strong> " . htmlspecialchars($contractor['business_name']) . " (ID: $contractor_id)</p>";
    
    // Test 1: Dashboard query (EXACT COPY)
    echo "<h3>Test 1: Dashboard Query (Pending Quotes Only)</h3>";
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.id NOT IN (
            SELECT COALESCE(quote_request_id, 0)
            FROM quote_responses
            WHERE contractor_id = ?
        )
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
    $dashboard_quotes = $stmt->fetchAll();
    
    echo "<p>Dashboard SQL returned: " . count($dashboard_quotes) . " quotes</p>";
    
    // Apply dashboard filtering
    $dashboard_filtered = 0;
    foreach ($dashboard_quotes as $quote) {
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $dashboard_filtered++;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $dashboard_filtered++;
            }
        }
    }
    
    echo "<p><strong>Dashboard filtered count: $dashboard_filtered</strong></p>";
    
    // Test 2: Quotes page query (EXACT COPY - "all" filter)
    echo "<h3>Test 2: Quotes Page Query (All Quotes)</h3>";
    
    $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
    $params = [$contractor_id];
    $where_conditions[] = "(qr.status IN ('open', 'completed', 'cancelled') OR qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?))";
    $params[] = $contractor_id;
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
               cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");
    
    $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
    $stmt->execute($query_params);
    $quotes_page_quotes = $stmt->fetchAll();
    
    echo "<p>Quotes page SQL returned: " . count($quotes_page_quotes) . " quotes</p>";
    
    // Apply quotes page filtering
    $quotes_page_filtered = 0;
    foreach ($quotes_page_quotes as $quote) {
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $quotes_page_filtered++;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $quotes_page_filtered++;
            }
        }
    }
    
    echo "<p><strong>Quotes page filtered count: $quotes_page_filtered</strong></p>";
    
    // Test 3: Quotes page query (PENDING filter only)
    echo "<h3>Test 3: Quotes Page Query (Pending Filter Only)</h3>";
    
    $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
    $params = [$contractor_id];
    $where_conditions[] = "qr.status = 'open' AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)";
    $params[] = $contractor_id;
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
               cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");
    
    $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
    $stmt->execute($query_params);
    $quotes_pending_quotes = $stmt->fetchAll();
    
    echo "<p>Quotes page (pending) SQL returned: " . count($quotes_pending_quotes) . " quotes</p>";
    
    // Apply quotes page filtering
    $quotes_pending_filtered = 0;
    foreach ($quotes_pending_quotes as $quote) {
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $quotes_pending_filtered++;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $quotes_pending_filtered++;
            }
        }
    }
    
    echo "<p><strong>Quotes page (pending) filtered count: $quotes_pending_filtered</strong></p>";
    
    // Analysis
    echo "<h3>📊 Analysis</h3>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Query Type</th><th>SQL Count</th><th>Filtered Count</th><th>Status Filter</th></tr>";
    echo "<tr><td>Dashboard</td><td>" . count($dashboard_quotes) . "</td><td>$dashboard_filtered</td><td>open + not responded</td></tr>";
    echo "<tr><td>Quotes Page (All)</td><td>" . count($quotes_page_quotes) . "</td><td>$quotes_page_filtered</td><td>open/completed/cancelled</td></tr>";
    echo "<tr><td>Quotes Page (Pending)</td><td>" . count($quotes_pending_quotes) . "</td><td>$quotes_pending_filtered</td><td>open + not responded</td></tr>";
    echo "</table>";
    
    if ($dashboard_filtered == $quotes_pending_filtered) {
        echo "<p style='color: green;'>✅ Dashboard matches Quotes Page (Pending filter)!</p>";
    } else {
        echo "<p style='color: red;'>❌ Dashboard ($dashboard_filtered) doesn't match Quotes Page Pending ($quotes_pending_filtered)</p>";
    }
    
    if ($dashboard_filtered != $quotes_page_filtered) {
        echo "<p style='color: orange;'>⚠️ Dashboard ($dashboard_filtered) doesn't match Quotes Page All ($quotes_page_filtered) - this is expected because they use different status filters</p>";
    }
    
    // Show detailed breakdown
    echo "<h3>🔍 Detailed Breakdown</h3>";
    
    // Get all quotes and show their status
    $stmt = $pdo->prepare("
        SELECT qr.*, cp_contractor.service_types, cp_contractor.service_areas,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
        FROM quote_requests qr
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        ORDER BY qr.created_at DESC
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
    $all_quotes = $stmt->fetchAll();
    
    echo "<p><strong>All quotes breakdown:</strong></p>";
    
    $dashboard_count = 0;
    $quotes_all_count = 0;
    $quotes_pending_count = 0;
    
    foreach ($all_quotes as $quote) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
        echo "<strong>ID:</strong> " . $quote['id'] . " | ";
        echo "<strong>Status:</strong> " . $quote['status'] . " | ";
        echo "<strong>Has Responded:</strong> " . ($quote['has_responded'] > 0 ? 'Yes' : 'No') . " | ";
        echo "<strong>District:</strong> " . $quote['district'] . " | ";
        echo "<strong>Service ID:</strong> " . $quote['service_category_id'] . "<br>";
        
        // Check if this quote matches contractor's services/areas
        $matches = false;
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $matches = true;
            echo "<strong>Match Type:</strong> Direct quote<br>";
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $matches = true;
                echo "<strong>Match Type:</strong> General quote (service + area match)<br>";
            } else {
                echo "<strong>Match Type:</strong> No match (service: " . ($has_service ? 'Yes' : 'No') . ", area: " . ($has_area ? 'Yes' : 'No') . ")<br>";
            }
        } else {
            echo "<strong>Match Type:</strong> Direct quote for other contractor<br>";
        }
        
        if ($matches) {
            // Dashboard logic: open + not responded
            if ($quote['status'] == 'open' && $quote['has_responded'] == 0) {
                $dashboard_count++;
                echo "<strong>Dashboard:</strong> ✅ Included<br>";
            } else {
                echo "<strong>Dashboard:</strong> ❌ Excluded (status: " . $quote['status'] . ", responded: " . ($quote['has_responded'] > 0 ? 'Yes' : 'No') . ")<br>";
            }
            
            // Quotes page (all) logic: open/completed/cancelled OR responded
            if (in_array($quote['status'], ['open', 'completed', 'cancelled']) || $quote['has_responded'] > 0) {
                $quotes_all_count++;
                echo "<strong>Quotes (All):</strong> ✅ Included<br>";
            } else {
                echo "<strong>Quotes (All):</strong> ❌ Excluded<br>";
            }
            
            // Quotes page (pending) logic: open + not responded
            if ($quote['status'] == 'open' && $quote['has_responded'] == 0) {
                $quotes_pending_count++;
                echo "<strong>Quotes (Pending):</strong> ✅ Included<br>";
            } else {
                echo "<strong>Quotes (Pending):</strong> ❌ Excluded<br>";
            }
        } else {
            echo "<strong>Dashboard:</strong> ❌ No match<br>";
            echo "<strong>Quotes (All):</strong> ❌ No match<br>";
            echo "<strong>Quotes (Pending):</strong> ❌ No match<br>";
        }
        
        echo "</div>";
    }
    
    echo "<h3>✅ Final Counts</h3>";
    echo "<p><strong>Dashboard should show:</strong> $dashboard_count</p>";
    echo "<p><strong>Quotes page (All) should show:</strong> $quotes_all_count</p>";
    echo "<p><strong>Quotes page (Pending) should show:</strong> $quotes_pending_count</p>";
    
    if ($dashboard_count == $quotes_pending_count) {
        echo "<p style='color: green;'>✅ Dashboard and Quotes Page (Pending) match!</p>";
        echo "<p><strong>Solution:</strong> The dashboard should match the 'Pending' tab on the quotes page, not the 'All' tab.</p>";
    } else {
        echo "<p style='color: red;'>❌ There's still a logic difference that needs to be fixed.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
