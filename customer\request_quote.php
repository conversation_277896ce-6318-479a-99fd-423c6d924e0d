<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

// Get customer profile
try {
    $stmt = $pdo->prepare("SELECT cp.*, u.email FROM customer_profiles cp JOIN users u ON cp.user_id = u.id WHERE cp.user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $customer = $stmt->fetch();
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: ../login.php');
    exit();
}

// Get unread notifications count
$unread_count = 0;
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$_SESSION['user_id']]);
    $unread_count = $stmt->fetchColumn();
} catch (PDOException $e) {
    // Ignore error, keep count as 0
}

// Get service categories
try {
    $stmt = $pdo->prepare("SELECT * FROM service_categories WHERE is_active = 1 ORDER BY name_en");
    $stmt->execute();
    $service_categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $service_categories = [];
}

// Get specific contractor if provided
$selected_contractor = null;
if (isset($_GET['contractor'])) {
    try {
        $stmt = $pdo->prepare("
            SELECT cp.*, u.email 
            FROM contractor_profiles cp 
            JOIN users u ON cp.user_id = u.id 
            WHERE cp.user_id = ? AND u.status = 'approved'
        ");
        $stmt->execute([$_GET['contractor']]);
        $selected_contractor = $stmt->fetch();
    } catch (PDOException $e) {
        // Ignore error, just don't pre-select contractor
    }
}

// Districts for location
$districts = [
    'Colombo', 'Gampaha', 'Kalutara', 'Kandy', 'Matale', 'Nuwara Eliya',
    'Galle', 'Matara', 'Hambantota', 'Jaffna', 'Kilinochchi', 'Mannar',
    'Vavuniya', 'Mullaitivu', 'Batticaloa', 'Ampara', 'Trincomalee',
    'Kurunegala', 'Puttalam', 'Anuradhapura', 'Polonnaruwa', 'Badulla',
    'Moneragala', 'Ratnapura', 'Kegalle'
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Request Quote - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, request quote" name="keywords">
    <meta content="Request Quote - Brick & Click" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <style>
        .quote-header {
            background: linear-gradient(135deg, #212529 0%, #34495e 100%);
            color: white;
            padding: 3rem 0;
        }
        
        .quote-form-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-top: -2rem;
            position: relative;
            z-index: 10;
        }
        
        .form-step {
            display: none;
            padding: 2rem;
        }
        
        .form-step.active {
            display: block;
        }
        
        .step-indicator {
            background: #f8f9fa;
            padding: 2rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .step {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 1rem;
            font-weight: 600;
            color: #6c757d;
            position: relative;
        }
        
        .step.active {
            background: #fd7e14;
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .step-line {
            width: 80px;
            height: 2px;
            background: #e9ecef;
            margin-top: 25px;
        }
        
        .step-line.completed {
            background: #28a745;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        
        .form-control:focus {
            border-color: #fd7e14;
            box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
        }
        
        .btn-next, .btn-submit {
            background: linear-gradient(135deg, #fd7e14, #ff9500);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-next:hover, .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(253, 126, 20, 0.3);
            color: white;
        }
        
        .service-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .service-card:hover {
            border-color: #fd7e14;
            transform: translateY(-2px);
        }
        
        .service-card.selected {
            border-color: #fd7e14;
            background: linear-gradient(135deg, #fd7e14, #ff9500);
            color: white;
        }
        
        .service-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #fd7e14;
        }
        
        .service-card.selected .service-icon {
            color: white;
        }
        
        .contractor-preview {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .contractor-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #fd7e14, #ff9500);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }
        
        .budget-range {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .budget-option {
            flex: 1;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .budget-option:hover {
            border-color: #fd7e14;
        }
        
        .budget-option.selected {
            border-color: #fd7e14;
            background: #fff5f0;
        }
    </style>
</head>

<body>
    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
        <a href="../index.php" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="../img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="contractors.php" class="nav-item nav-link">Find Contractors</a>
                <a href="quotes.php" class="nav-item nav-link">My Quotes</a>
                <a href="cost_estimator.php" class="nav-item nav-link">Cost Estimator</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
            </div>
            <div class="dropdown">
                <a href="#" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($customer['first_name']); ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="notifications.php"><i class="fas fa-bell me-2"></i>Notifications <?php if ($unread_count > 0): ?><span class="badge bg-danger"><?php echo $unread_count; ?></span><?php endif; ?></a></li>
                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Quote Header Start -->
    <div class="quote-header">
        <div class="container text-center">
            <h1 class="display-4 mb-3">Request a Quote</h1>
            <p class="fs-5 mb-0">Get quotes from verified contractors for your construction project</p>
        </div>
    </div>
    <!-- Quote Header End -->

    <!-- Quote Form Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="quote-form-card">
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step active" id="step1">1</div>
                    <div class="step-line" id="line1"></div>
                    <div class="step" id="step2">2</div>
                    <div class="step-line" id="line2"></div>
                    <div class="step" id="step3">3</div>
                    <div class="step-line" id="line3"></div>
                    <div class="step" id="step4">4</div>
                </div>

                <form action="process_quote_request.php" method="POST" id="quoteForm">
                    <!-- Step 1: Service Type -->
                    <div class="form-step active" id="formStep1">
                        <h3 class="mb-4 text-center">What service do you need?</h3>
                        <div class="row g-3">
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="service-card" onclick="selectService(1, 'House Construction')">
                                    <div class="service-icon">
                                        <i class="fas fa-home"></i>
                                    </div>
                                    <h6>House Construction</h6>
                                    <p class="text-muted mb-0">Complete house construction services</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="service-card" onclick="selectService(2, 'Building Renovation')">
                                    <div class="service-icon">
                                        <i class="fas fa-tools"></i>
                                    </div>
                                    <h6>Renovation</h6>
                                    <p class="text-muted mb-0">Home and building renovation services</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="service-card" onclick="selectService(3, 'Commercial Construction')">
                                    <div class="service-icon">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <h6>Commercial Construction</h6>
                                    <p class="text-muted mb-0">Complete commercial construction services</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="service-card" onclick="selectService(4, 'Interior Design & Finishing')">
                                    <div class="service-icon">
                                        <i class="fas fa-paint-roller"></i>
                                    </div>
                                    <h6>Interior Design</h6>
                                    <p class="text-muted mb-0">Interior design and decoration</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="service-card" onclick="selectService(5, 'Roofing & Waterproofing')">
                                    <div class="service-icon">
                                        <i class="fas fa-warehouse"></i>
                                    </div>
                                    <h6>Roofing</h6>
                                    <p class="text-muted mb-0">Roofing installation and repair</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="service-card" onclick="selectService(6, 'Electrical Work')">
                                    <div class="service-icon">
                                        <i class="fas fa-bolt"></i>
                                    </div>
                                    <h6>Electrical</h6>
                                    <p class="text-muted mb-0">Electrical installation and repair</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="service-card" onclick="selectService(7, 'Plumbing & Sanitation')">
                                    <div class="service-icon">
                                        <i class="fas fa-wrench"></i>
                                    </div>
                                    <h6>Plumbing</h6>
                                    <p class="text-muted mb-0">Plumbing installation and maintenance</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="service-card" onclick="selectService(8, 'Landscaping & Gardening')">
                                    <div class="service-icon">
                                        <i class="fas fa-seedling"></i>
                                    </div>
                                    <h6>Landscaping</h6>
                                    <p class="text-muted mb-0">Garden and landscape design</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="service-card" onclick="selectService(9, 'Swimming Pool Construction')">
                                    <div class="service-icon">
                                        <i class="fas fa-swimming-pool"></i>
                                    </div>
                                    <h6>Swimming Pool</h6>
                                    <p class="text-muted mb-0">Swimming pool construction</p>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="service-card" onclick="selectService(10, 'Road & Infrastructure')">
                                    <div class="service-icon">
                                        <i class="fas fa-road"></i>
                                    </div>
                                    <h6>Infrastructure</h6>
                                    <p class="text-muted mb-0">Road and infrastructure development</p>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="service_category_id" id="selectedService" required>
                        
                        <div class="text-center mt-4">
                            <button type="button" class="btn btn-next" onclick="nextStep()" disabled id="step1Next">
                                Continue <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Project Details -->
                    <div class="form-step" id="formStep2">
                        <h3 class="mb-4 text-center">Tell us about your project</h3>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Project Title *</label>
                                <input type="text" class="form-control" name="title" required placeholder="e.g., New House Construction">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Location *</label>
                                <input type="text" class="form-control" name="location" required placeholder="e.g., Colombo 07">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">District *</label>
                                <select class="form-control" name="district" required>
                                    <option value="">Select District</option>
                                    <?php foreach ($districts as $district): ?>
                                    <option value="<?php echo $district; ?>"><?php echo $district; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Project Timeline *</label>
                                <select class="form-control" name="project_timeline" required>
                                    <option value="">Select Timeline</option>
                                    <option value="ASAP">As soon as possible</option>
                                    <option value="1-3 months">Within 1-3 months</option>
                                    <option value="3-6 months">Within 3-6 months</option>
                                    <option value="6+ months">More than 6 months</option>
                                    <option value="flexible">Flexible</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Project Description *</label>
                            <textarea class="form-control" name="description" rows="5" required placeholder="Describe your project requirements, specifications, and any special needs..."></textarea>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-secondary" onclick="prevStep()">
                                <i class="fas fa-arrow-left me-2"></i>Back
                            </button>
                            <button type="button" class="btn btn-next" onclick="nextStep()">
                                Continue <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 3: Budget -->
                    <div class="form-step" id="formStep3">
                        <h3 class="mb-4 text-center">What's your estimated budget?</h3>
                        
                        <div class="budget-range">
                            <div class="budget-option" onclick="selectBudget('under-500k', 'Under 500K')">
                                <h6>Under 500K</h6>
                                <p class="text-muted mb-0">Small projects</p>
                            </div>
                            <div class="budget-option" onclick="selectBudget('500k-1m', '500K - 1M')">
                                <h6>500K - 1M</h6>
                                <p class="text-muted mb-0">Medium projects</p>
                            </div>
                            <div class="budget-option" onclick="selectBudget('1m-5m', '1M - 5M')">
                                <h6>1M - 5M</h6>
                                <p class="text-muted mb-0">Large projects</p>
                            </div>
                            <div class="budget-option" onclick="selectBudget('5m+', '5M+')">
                                <h6>5M+</h6>
                                <p class="text-muted mb-0">Premium projects</p>
                            </div>
                        </div>
                        <input type="hidden" name="budget_range" id="selectedBudgetRange">
                        
                        <div class="mt-4">
                            <label class="form-label">Specific Budget Amount (Optional)</label>
                            <div class="input-group">
                                <span class="input-group-text">LKR</span>
                                <input type="number" class="form-control" name="estimated_budget" placeholder="Enter specific amount">
                            </div>
                            <small class="text-muted">This helps contractors provide more accurate quotes</small>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-outline-secondary" onclick="prevStep()">
                                <i class="fas fa-arrow-left me-2"></i>Back
                            </button>
                            <button type="button" class="btn btn-next" onclick="nextStep()">
                                Continue <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 4: Review & Submit -->
                    <div class="form-step" id="formStep4">
                        <h3 class="mb-4 text-center">Review & Submit</h3>
                        
                        <?php if ($selected_contractor): ?>
                        <div class="contractor-preview">
                            <h5 class="mb-3">Sending quote request to:</h5>
                            <div class="d-flex align-items-center">
                                <div class="contractor-avatar me-3">
                                    <i class="fas fa-hard-hat"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1"><?php echo htmlspecialchars($selected_contractor['business_name']); ?></h6>
                                    <p class="text-muted mb-0"><?php echo htmlspecialchars($selected_contractor['contact_person']); ?></p>
                                </div>
                            </div>
                            <input type="hidden" name="specific_contractor" value="<?php echo $selected_contractor['user_id']; ?>">
                        </div>
                        <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Your quote request will be sent to all contractors who provide the selected service in your area.
                        </div>
                        <?php endif; ?>
                        
                        <div class="bg-light p-4 rounded-3 mb-4">
                            <h6>Quote Request Summary:</h6>
                            <div id="quoteSummary">
                                <!-- Summary will be populated by JavaScript -->
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="terms" required>
                                <label class="form-check-label">
                                    I agree to the <a href="#" class="text-primary">Terms of Service</a> and understand that contractors will contact me with quotes
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-secondary" onclick="prevStep()">
                                <i class="fas fa-arrow-left me-2"></i>Back
                            </button>
                            <button type="submit" class="btn btn-submit">
                                <i class="fas fa-paper-plane me-2"></i>Submit Quote Request
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- Quote Form End -->

    <script>
        let currentStep = 1;
        const totalSteps = 4;
        let selectedServiceId = null;
        let selectedServiceName = '';

        function selectService(serviceId, serviceName) {
            // Remove selection from all cards
            document.querySelectorAll('.service-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Add selection to clicked card
            event.currentTarget.classList.add('selected');

            // Store selection
            selectedServiceId = serviceId;
            selectedServiceName = serviceName;
            document.getElementById('selectedService').value = serviceId;

            // Enable next button
            document.getElementById('step1Next').disabled = false;
        }

        let selectedBudgetRange = '';
        let selectedBudgetText = '';

        function selectBudget(range, text) {
            // Remove selection from all budget options
            document.querySelectorAll('.budget-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Add selection to clicked option
            event.currentTarget.classList.add('selected');

            // Store selection
            selectedBudgetRange = range;
            selectedBudgetText = text;
            document.getElementById('selectedBudgetRange').value = range;
        }

        function nextStep() {
            if (currentStep < totalSteps) {
                // Hide current step
                document.getElementById(`formStep${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.add('completed');
                
                if (currentStep < totalSteps - 1) {
                    document.getElementById(`line${currentStep}`).classList.add('completed');
                }
                
                // Show next step
                currentStep++;
                document.getElementById(`formStep${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.add('active');
                
                // Update summary if on last step
                if (currentStep === totalSteps) {
                    updateSummary();
                }
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                // Hide current step
                document.getElementById(`formStep${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');
                
                // Show previous step
                currentStep--;
                document.getElementById(`formStep${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.remove('completed');
                document.getElementById(`step${currentStep}`).classList.add('active');
                
                if (currentStep < totalSteps - 1) {
                    document.getElementById(`line${currentStep}`).classList.remove('completed');
                }
            }
        }

        function updateSummary() {
            const form = document.getElementById('quoteForm');
            const formData = new FormData(form);
            
            let summary = `
                <p><strong>Service:</strong> ${selectedServiceName}</p>
                <p><strong>Project:</strong> ${formData.get('title') || 'Not specified'}</p>
                <p><strong>Location:</strong> ${formData.get('location') || 'Not specified'}, ${formData.get('district') || 'Not specified'}</p>
                <p><strong>Timeline:</strong> ${formData.get('project_timeline') || 'Not specified'}</p>
            `;
            
            if (formData.get('estimated_budget')) {
                summary += `<p><strong>Budget:</strong> LKR ${parseInt(formData.get('estimated_budget')).toLocaleString()}</p>`;
            } else if (selectedBudgetText) {
                summary += `<p><strong>Budget Range:</strong> ${selectedBudgetText}</p>`;
            }
            
            document.getElementById('quoteSummary').innerHTML = summary;
        }

        // Form validation
        document.getElementById('quoteForm').addEventListener('submit', function(e) {
            if (!selectedServiceId) {
                e.preventDefault();
                alert('Please select a service type.');
                return false;
            }
        });
    </script>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
</body>
</html>
