<?php
// Include authentication and authorization check
require_once 'includes/auth_check.php';

// Contractor data is now available in $contractor variable

// Filter parameters
$rating_filter = $_GET['rating'] ?? 'all';
$search = $_GET['search'] ?? '';

// Build query conditions - check which approval field exists
$where_conditions = ["r.contractor_id = ?"];
$params = [$_SESSION['user_id']];

// Show all reviews except deleted ones
try {
    $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'status'");
    $has_status = $stmt->fetch();

    $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'is_approved'");
    $has_is_approved = $stmt->fetch();

    if ($has_status) {
        $where_conditions[] = "(r.status IS NULL OR r.status != 'deleted')";
    } elseif ($has_is_approved) {
        $where_conditions[] = "(r.is_approved IS NULL OR r.is_approved != 0)";
    }
    // If neither exists, show all reviews
} catch (PDOException $e) {
    // If there's an error checking columns, just show all reviews
}

if ($rating_filter !== 'all') {
    $where_conditions[] = "r.rating = ?";
    $params[] = (int)$rating_filter;
}

if (!empty($search)) {
    $where_conditions[] = "(r.review_text LIKE ? OR cp.first_name LIKE ? OR cp.last_name LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
}

$where_clause = implode(' AND ', $where_conditions);

// Get reviews
try {
    $stmt = $pdo->prepare("
        SELECT r.*, cp.first_name, cp.last_name, cp.district,
               COALESCE(sc.name_en, qr.title, 'Service') as service_category,
               qr.created_at as quote_date, qr.title as project_title
        FROM reviews r
        JOIN customer_profiles cp ON r.customer_id = cp.user_id
        LEFT JOIN quote_requests qr ON r.quote_request_id = qr.id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE $where_clause
        ORDER BY r.created_at DESC
    ");
    $stmt->execute($params);
    $reviews = $stmt->fetchAll();
} catch (PDOException $e) {
    $reviews = [];
    error_log("Reviews query error: " . $e->getMessage());
}

// Get review statistics
try {
    // Build the WHERE clause for statistics based on available approval field
    $stats_where = "contractor_id = ?";
    if ($has_status) {
        $stats_where .= " AND (status IS NULL OR status != 'deleted')";
    } elseif ($has_is_approved) {
        $stats_where .= " AND (is_approved IS NULL OR is_approved != 0)";
    }

    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as total_reviews,
            AVG(rating) as average_rating,
            SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
            SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
            SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
            SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
            SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star
        FROM reviews
        WHERE $stats_where
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats = $stmt->fetch();
} catch (PDOException $e) {
    $stats = ['total_reviews' => 0, 'average_rating' => 0, 'five_star' => 0, 'four_star' => 0, 'three_star' => 0, 'two_star' => 0, 'one_star' => 0];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Reviews & Ratings - BrickClick Contractor</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet"> 
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF6B35;
            --accent-yellow: #F7931E;
            --success-green: #28a745;
            --info-blue: #17a2b8;
            --warning-orange: #ffc107;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --dark-gray: #343a40;
        }
        
        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e9ecef 100%);
            font-family: 'Open Sans', sans-serif;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            z-index: 1000;
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            flex-shrink: 0;
        }

        .sidebar-header h3 {
            color: var(--accent-orange);
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }

        .sidebar-header p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }

        .sidebar-nav {
            flex: 1;
            padding: 0.5rem 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .nav-item {
            margin: 0.15rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,107,53,0.1);
            color: var(--accent-orange);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--accent-orange), var(--primary-red));
            color: white;
            box-shadow: 0 3px 10px rgba(255,107,53,0.3);
        }

        .nav-link i {
            margin-right: 0.8rem;
            width: 18px;
            text-align: center;
            font-size: 1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--accent-yellow), var(--accent-orange));
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .page-title {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 2rem;
        }
        
        .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }
        
        .stats-overview {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .rating-summary {
            display: flex;
            align-items: center;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .overall-rating {
            text-align: center;
        }
        
        .rating-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--accent-yellow);
            margin: 0;
        }
        
        .rating-stars {
            color: var(--accent-yellow);
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
        
        .rating-count {
            color: var(--medium-gray);
            font-size: 0.9rem;
        }
        
        .rating-breakdown {
            flex: 1;
        }
        
        .rating-row {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.5rem;
        }
        
        .rating-label {
            width: 60px;
            color: var(--dark-gray);
            font-weight: 600;
        }
        
        .rating-bar {
            flex: 1;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .rating-fill {
            height: 100%;
            background: linear-gradient(135deg, var(--accent-yellow), var(--accent-orange));
            transition: width 0.3s ease;
        }
        
        .rating-count-small {
            width: 40px;
            text-align: right;
            color: var(--medium-gray);
            font-size: 0.9rem;
        }
        
        .filters-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .rating-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .rating-filter {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .rating-filter.all {
            background: linear-gradient(135deg, var(--medium-gray), #5a6268);
            color: white;
        }
        
        .rating-filter.star-5 {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            color: white;
        }
        
        .rating-filter.star-4 {
            background: linear-gradient(135deg, var(--info-blue), #138496);
            color: white;
        }
        
        .rating-filter.star-3 {
            background: linear-gradient(135deg, var(--warning-orange), #e0a800);
            color: white;
        }
        
        .rating-filter.star-2 {
            background: linear-gradient(135deg, var(--accent-orange), #e55a2b);
            color: white;
        }
        
        .rating-filter.star-1 {
            background: linear-gradient(135deg, var(--primary-red), #bd2130);
            color: white;
        }
        
        .rating-filter.active {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .search-box {
            position: relative;
        }
        
        .search-input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem 0.75rem 3rem;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border-color: var(--accent-orange);
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }
        
        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--medium-gray);
        }
        
        .review-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .review-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 1rem;
        }
        
        .customer-info h5 {
            color: var(--primary-dark);
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .customer-details {
            color: var(--medium-gray);
            font-size: 0.9rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .review-rating {
            text-align: right;
        }
        
        .stars {
            color: var(--accent-yellow);
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }
        
        .review-date {
            color: var(--medium-gray);
            font-size: 0.9rem;
        }
        
        .review-content {
            margin-bottom: 1rem;
        }
        
        .service-category {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: var(--info-blue);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .review-text {
            color: var(--dark-gray);
            line-height: 1.6;
            font-style: italic;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--medium-gray);
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .empty-state h3 {
            margin-bottom: 1rem;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>BrickClick</h3>
            <p>Contractor Portal</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice-dollar"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="portfolio.php" class="nav-link">
                    <i class="fas fa-images"></i>Portfolio
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link active">
                    <i class="fas fa-star"></i>Reviews & Ratings
                </a>
            </div>
            <div class="nav-item">
                <a href="profile.php" class="nav-link">
                    <i class="fas fa-user-edit"></i>Profile & Services
                </a>
            </div>
            <div class="nav-item">
                <a href="payment_history.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payment History
                </a>
            </div>
            <div class="nav-item">
                <a href="notifications.php" class="nav-link">
                    <i class="fas fa-bell"></i>Notifications
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Reviews & Ratings</h1>
            <p class="page-subtitle">Monitor customer feedback and track your reputation</p>
        </div>

        <!-- Statistics Overview -->
        <div class="stats-overview">
            <div class="rating-summary">
                <div class="overall-rating">
                    <h2 class="rating-number"><?php echo number_format($stats['average_rating'], 1); ?></h2>
                    <div class="rating-stars">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                            <i class="fas fa-star<?php echo $i <= round($stats['average_rating']) ? '' : '-o'; ?>"></i>
                        <?php endfor; ?>
                    </div>
                    <div class="rating-count"><?php echo $stats['total_reviews']; ?> reviews</div>
                </div>
                
                <div class="rating-breakdown">
                    <?php for ($star = 5; $star >= 1; $star--): ?>
                        <?php
                        $star_names = [1 => 'one', 2 => 'two', 3 => 'three', 4 => 'four', 5 => 'five'];
                        $count = $stats[strtolower($star_names[$star]) . '_star'];
                        $percentage = $stats['total_reviews'] > 0 ? ($count / $stats['total_reviews']) * 100 : 0;
                        ?>
                        <div class="rating-row">
                            <div class="rating-label"><?php echo $star; ?> star</div>
                            <div class="rating-bar">
                                <div class="rating-fill" style="width: <?php echo $percentage; ?>%"></div>
                            </div>
                            <div class="rating-count-small"><?php echo $count; ?></div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <div class="rating-filters">
                <a href="?rating=all&search=<?php echo urlencode($search); ?>" 
                   class="rating-filter all <?php echo $rating_filter === 'all' ? 'active' : ''; ?>">
                    All Reviews (<?php echo $stats['total_reviews']; ?>)
                </a>
                <a href="?rating=5&search=<?php echo urlencode($search); ?>" 
                   class="rating-filter star-5 <?php echo $rating_filter === '5' ? 'active' : ''; ?>">
                    <i class="fas fa-star"></i> 5 Stars (<?php echo $stats['five_star']; ?>)
                </a>
                <a href="?rating=4&search=<?php echo urlencode($search); ?>" 
                   class="rating-filter star-4 <?php echo $rating_filter === '4' ? 'active' : ''; ?>">
                    <i class="fas fa-star"></i> 4 Stars (<?php echo $stats['four_star']; ?>)
                </a>
                <a href="?rating=3&search=<?php echo urlencode($search); ?>" 
                   class="rating-filter star-3 <?php echo $rating_filter === '3' ? 'active' : ''; ?>">
                    <i class="fas fa-star"></i> 3 Stars (<?php echo $stats['three_star']; ?>)
                </a>
                <a href="?rating=2&search=<?php echo urlencode($search); ?>" 
                   class="rating-filter star-2 <?php echo $rating_filter === '2' ? 'active' : ''; ?>">
                    <i class="fas fa-star"></i> 2 Stars (<?php echo $stats['two_star']; ?>)
                </a>
                <a href="?rating=1&search=<?php echo urlencode($search); ?>" 
                   class="rating-filter star-1 <?php echo $rating_filter === '1' ? 'active' : ''; ?>">
                    <i class="fas fa-star"></i> 1 Star (<?php echo $stats['one_star']; ?>)
                </a>
            </div>
            
            <div class="search-box">
                <i class="fas fa-search search-icon"></i>
                <form method="GET" action="">
                    <input type="hidden" name="rating" value="<?php echo htmlspecialchars($rating_filter); ?>">
                    <input type="text" class="search-input" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="Search reviews by customer name or review text...">
                </form>
            </div>
        </div>

        <!-- Reviews List -->
        <?php if (empty($reviews)): ?>
            <div class="empty-state">
                <i class="fas fa-star"></i>
                <h3>No reviews found</h3>
                <p>No reviews match your current filters. Customer reviews will appear here after you complete projects.</p>
            </div>
        <?php else: ?>
            <?php foreach ($reviews as $review): ?>
                <div class="review-card">
                    <div class="review-header">
                        <div class="customer-info">
                            <h5><?php echo htmlspecialchars($review['first_name'] . ' ' . $review['last_name']); ?></h5>
                            <div class="customer-details">
                                <span>
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo htmlspecialchars($review['district']); ?>
                                </span>
                                <?php if ($review['service_category']): ?>
                                    <span>
                                        <i class="fas fa-tools me-1"></i>
                                        <?php echo htmlspecialchars($review['service_category']); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="review-rating">
                            <div class="stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star<?php echo $i <= $review['rating'] ? '' : '-o'; ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <div class="review-date">
                                <?php echo date('M j, Y', strtotime($review['created_at'])); ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="review-content">
                        <?php if ($review['service_category']): ?>
                            <div class="service-category">
                                <i class="fas fa-tools me-1"></i>
                                <?php echo htmlspecialchars($review['service_category']); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($review['review_text']): ?>
                            <p class="review-text">
                                "<?php echo htmlspecialchars($review['review_text']); ?>"
                            </p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-submit search form on input
        document.querySelector('.search-input').addEventListener('input', function() {
            setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    </script>
</body>
</html>
