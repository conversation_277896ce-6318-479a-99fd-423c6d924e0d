<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: quotes.php');
    exit();
}

$quote_response_id = (int)$_POST['quote_response_id'];
$amount = (float)$_POST['amount'];
$payment_type = $_POST['payment_type'];
$payment_method = $_POST['payment_method'];

// Validate inputs
if (!$quote_response_id || !$amount || !$payment_type || !$payment_method) {
    $_SESSION['error'] = 'Invalid payment data.';
    header('Location: quotes.php');
    exit();
}

// Get quote response details to verify ownership
try {
    $stmt = $pdo->prepare("
        SELECT qres.*, qr.customer_id, qr.title
        FROM quote_responses qres
        JOIN quote_requests qr ON qres.quote_request_id = qr.id
        WHERE qres.id = ? AND qr.customer_id = ?
    ");
    $stmt->execute([$quote_response_id, $_SESSION['user_id']]);
    $quote_response = $stmt->fetch();
    
    if (!$quote_response) {
        $_SESSION['error'] = 'Quote response not found or not accessible.';
        header('Location: quotes.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: quotes.php');
    exit();
}

// Check if payment already exists
try {
    $stmt = $pdo->prepare("
        SELECT * FROM project_payments 
        WHERE quote_response_id = ? AND payment_type = ? AND payment_status = 'completed'
    ");
    $stmt->execute([$quote_response_id, $payment_type]);
    $existing_payment = $stmt->fetch();
    
    if ($existing_payment) {
        $_SESSION['error'] = 'Payment has already been completed for this project.';
        header('Location: quotes.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error checking existing payments.';
    header('Location: quotes.php');
    exit();
}

// Store payment data in session for payment portal
$_SESSION['payment_data'] = [
    'quote_response_id' => $quote_response_id,
    'amount' => $amount,
    'payment_type' => $payment_type,
    'payment_method' => $payment_method,
    'quote_response' => $quote_response
];

// Redirect to payment portal for card details entry
header("Location: payment_portal.php");
exit();
?>
