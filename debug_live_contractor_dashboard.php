<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔍 Live Contractor Dashboard Debug</h2>";

// Check if user is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'contractor') {
    echo "<p style='color: red;'>❌ Not logged in as contractor. Please login first.</p>";
    echo "<p><a href='login.php'>Login as Contractor</a></p>";
    exit;
}

$contractor_id = $_SESSION['user_id'];

echo "<p><strong>Logged in contractor ID:</strong> $contractor_id</p>";

try {
    // Step 1: Get contractor profile
    echo "<h3>Step 1: Contractor Profile</h3>";
    
    $stmt = $pdo->prepare("
        SELECT cp.*, u.email, u.status 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE cp.user_id = ?
    ");
    $stmt->execute([$contractor_id]);
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p style='color: red;'>❌ Contractor profile not found</p>";
        exit;
    }
    
    echo "<p><strong>Business Name:</strong> " . htmlspecialchars($contractor['business_name']) . "</p>";
    echo "<p><strong>Status:</strong> " . $contractor['status'] . "</p>";
    
    $services = json_decode($contractor['service_types'], true) ?: [];
    $areas = json_decode($contractor['service_areas'], true) ?: [];
    echo "<p><strong>Services:</strong> " . implode(', ', $services) . "</p>";
    echo "<p><strong>Areas:</strong> " . implode(', ', $areas) . "</p>";
    
    // Step 2: Check database column
    echo "<h3>Step 2: Database Column Check</h3>";
    
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $column_exists = $stmt->fetch();
    
    if ($column_exists) {
        echo "<p style='color: green;'>✅ specific_contractor_id column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ specific_contractor_id column missing - adding it...</p>";
        $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        echo "<p style='color: green;'>✅ Added specific_contractor_id column</p>";
    }
    
    // Step 3: Check all open quotes
    echo "<h3>Step 3: All Open Quotes</h3>";
    
    $stmt = $pdo->query("
        SELECT qr.*, sc.name_en as service_name
        FROM quote_requests qr
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE qr.status = 'open'
        ORDER BY qr.created_at DESC
    ");
    $all_open_quotes = $stmt->fetchAll();
    
    echo "<p>Found " . count($all_open_quotes) . " open quotes in database</p>";
    
    if (count($all_open_quotes) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Service</th><th>District</th><th>Type</th><th>Created</th></tr>";
        foreach ($all_open_quotes as $quote) {
            $type = $quote['specific_contractor_id'] ? 'Specific (' . $quote['specific_contractor_id'] . ')' : 'General';
            echo "<tr>";
            echo "<td>" . $quote['id'] . "</td>";
            echo "<td>" . htmlspecialchars(substr($quote['title'], 0, 30)) . "...</td>";
            echo "<td>" . htmlspecialchars($quote['service_name'] ?? 'Unknown') . " (ID: " . $quote['service_category_id'] . ")</td>";
            echo "<td>" . htmlspecialchars($quote['district']) . "</td>";
            echo "<td>" . $type . "</td>";
            echo "<td>" . $quote['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 4: Test exact dashboard query
    echo "<h3>Step 4: Dashboard Query Test</h3>";
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.id NOT IN (
            SELECT COALESCE(quote_request_id, 0)
            FROM quote_responses
            WHERE contractor_id = ?
        )
        ORDER BY qr.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
    $all_recent_quotes = $stmt->fetchAll();
    
    echo "<p>Dashboard query returned " . count($all_recent_quotes) . " quotes</p>";
    
    if (count($all_recent_quotes) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Service ID</th><th>District</th><th>Specific Contractor</th><th>Has Responded</th></tr>";
        foreach ($all_recent_quotes as $quote) {
            echo "<tr>";
            echo "<td>" . $quote['id'] . "</td>";
            echo "<td>" . htmlspecialchars(substr($quote['title'], 0, 30)) . "...</td>";
            echo "<td>" . $quote['service_category_id'] . "</td>";
            echo "<td>" . $quote['district'] . "</td>";
            echo "<td>" . ($quote['specific_contractor_id'] ?: 'NULL') . "</td>";
            echo "<td>" . $quote['has_responded'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 5: Apply filtering logic
    echo "<h3>Step 5: Filtering Logic</h3>";
    
    $recent_quotes = [];
    foreach ($all_recent_quotes as $quote) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0; background: #f9f9f9;'>";
        echo "<h4>Quote ID {$quote['id']}: " . htmlspecialchars($quote['title']) . "</h4>";
        
        $include_quote = false;
        $reason = "";
        
        // Always show direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $recent_quotes[] = $quote;
            $include_quote = true;
            $reason = "Direct quote for this contractor";
        }
        // For general quotes (no specific contractor), check service and area match
        elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($contractor['service_types'], true) ?: [];
            $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];
            
            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            
            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);
            
            echo "<p><strong>Service Match:</strong> " . ($has_service ? 'YES' : 'NO') . " (looking for {$quote['service_category_id']} in [" . implode(', ', $contractor_services) . "])</p>";
            echo "<p><strong>Area Match:</strong> " . ($has_area ? 'YES' : 'NO') . " (looking for {$quote['district']} in [" . implode(', ', $contractor_areas) . "])</p>";
            
            if ($has_service && $has_area) {
                $recent_quotes[] = $quote;
                $include_quote = true;
                $reason = "General quote - service and area match";
            } else {
                $reason = "General quote - no match (service: " . ($has_service ? 'yes' : 'no') . ", area: " . ($has_area ? 'yes' : 'no') . ")";
            }
        } else {
            $reason = "Direct quote for different contractor (ID: {$quote['specific_contractor_id']})";
        }
        
        echo "<p><strong>Result:</strong> " . ($include_quote ? '✅ INCLUDED' : '❌ EXCLUDED') . " - $reason</p>";
        echo "</div>";
        
        // Limit to 5 quotes for dashboard
        if (count($recent_quotes) >= 5) {
            break;
        }
    }
    
    // Step 6: Final results
    echo "<h3>Step 6: Final Results</h3>";
    echo "<p><strong>Total quotes to display on dashboard:</strong> " . count($recent_quotes) . "</p>";
    
    if (count($recent_quotes) > 0) {
        echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS! These quotes should appear on the dashboard:</p>";
        echo "<ul>";
        foreach ($recent_quotes as $quote) {
            echo "<li><strong>ID {$quote['id']}:</strong> " . htmlspecialchars($quote['title']) . " - {$quote['first_name']} {$quote['last_name']} ({$quote['district']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ NO QUOTES TO DISPLAY</p>";
        echo "<p>This explains why the dashboard is empty. Possible reasons:</p>";
        echo "<ul>";
        echo "<li>No general quotes match your services and areas</li>";
        echo "<li>No direct quotes sent to you</li>";
        echo "<li>You have already responded to all available quotes</li>";
        echo "</ul>";
    }
    
    // Step 7: Check quotes page logic
    echo "<h3>Step 7: Quotes Page Logic Test</h3>";
    
    // Test the quotes page query with pending filter
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.status = 'open' AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)
        ORDER BY qr.created_at DESC
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id]);
    $all_quotes_raw = $stmt->fetchAll();
    
    echo "<p>Quotes page query returned " . count($all_quotes_raw) . " quotes</p>";
    
    // Filter quotes using the same logic
    $all_quotes = [];
    foreach ($all_quotes_raw as $quote) {
        // Always include direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $all_quotes[] = $quote;
            continue;
        }
        
        // For general quotes (no specific contractor), check service and area match
        if ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($contractor['service_types'], true) ?: [];
            $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];
            
            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            
            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $all_quotes[] = $quote;
            }
        }
    }
    
    echo "<p><strong>After filtering for quotes page:</strong> " . count($all_quotes) . " quotes</p>";
    
    if (count($all_quotes) > 0) {
        echo "<p style='color: green; font-weight: bold;'>✅ These quotes should appear on the quotes page:</p>";
        echo "<ul>";
        foreach ($all_quotes as $quote) {
            echo "<li><strong>ID {$quote['id']}:</strong> " . htmlspecialchars($quote['title'] ?? 'Untitled') . " - " . htmlspecialchars($quote['service_category'] ?? 'Unknown Service') . " in {$quote['district']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ NO QUOTES FOR QUOTES PAGE</p>";
    }
    
    echo "<h3>Summary</h3>";
    echo "<p>If you're still not seeing quotes on the actual pages, the issue might be:</p>";
    echo "<ul>";
    echo "<li>Session issues - try logging out and back in</li>";
    echo "<li>Browser cache - try clearing cache or hard refresh</li>";
    echo "<li>PHP errors - check error logs</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>
