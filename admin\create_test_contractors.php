<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

echo "<h1>Create Test Contractors</h1>";

// Check if contractors already exist
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'contractor'");
    $contractor_count = $stmt->fetchColumn();
    
    if ($contractor_count > 0) {
        echo "<p style='color: orange;'>⚠️ There are already $contractor_count contractors in the database.</p>";
        echo "<p>Do you want to create additional test contractors?</p>";
    } else {
        echo "<p style='color: red;'>❌ No contractors found in database. Creating test contractors...</p>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error checking contractors: " . $e->getMessage() . "</p>";
    exit();
}

// Create test contractors if requested
if (isset($_POST['create_contractors'])) {
    $test_contractors = [
        [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'business_name' => 'Liyanage Constructions',
            'contact_person' => 'Priyantha Yapa',
            'phone' => '0815632912',
            'cida_grade' => 'C8',
            'status' => 'approved'
        ],
        [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'business_name' => 'Silva Building Solutions',
            'contact_person' => 'Kamal Silva',
            'phone' => '0771234567',
            'cida_grade' => 'C7',
            'status' => 'pending'
        ],
        [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'business_name' => 'Fernando Construction Co.',
            'contact_person' => 'Nimal Fernando',
            'phone' => '0779876543',
            'cida_grade' => 'C6',
            'status' => 'suspended'
        ],
        [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'business_name' => 'Perera Builders',
            'contact_person' => 'Sunil Perera',
            'phone' => '0765432109',
            'cida_grade' => 'C9',
            'status' => 'rejected'
        ]
    ];
    
    $created_count = 0;
    
    foreach ($test_contractors as $contractor_data) {
        try {
            // Check if email already exists
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$contractor_data['email']]);
            if ($stmt->fetch()) {
                echo "<p style='color: orange;'>⚠️ Contractor with email {$contractor_data['email']} already exists, skipping...</p>";
                continue;
            }
            
            // Create user
            $hashed_password = password_hash($contractor_data['password'], PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO users (email, password, user_type, status, created_at) 
                VALUES (?, ?, 'contractor', ?, NOW())
            ");
            $stmt->execute([
                $contractor_data['email'],
                $hashed_password,
                $contractor_data['status']
            ]);
            
            $user_id = $pdo->lastInsertId();
            
            // Create contractor profile
            $stmt = $pdo->prepare("
                INSERT INTO contractor_profiles (
                    user_id, business_name, contact_person, phone, cida_grade,
                    business_address, website, cida_registration, business_description,
                    service_areas, service_types, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $service_areas = json_encode(['Colombo', 'Gampaha', 'Kalutara']);
            $service_types = json_encode(['House Construction', 'Building Renovation', 'Interior Design & Finishing']);
            
            $stmt->execute([
                $user_id,
                $contractor_data['business_name'],
                $contractor_data['contact_person'],
                $contractor_data['phone'],
                $contractor_data['cida_grade'],
                'No. 123, Main Street, Colombo 07',
                'https://www.' . strtolower(str_replace(' ', '', $contractor_data['business_name'])) . '.lk',
                'CIDA/' . $contractor_data['cida_grade'] . '/2024/' . rand(1000, 9999),
                'Professional construction services with over 10 years of experience in the industry.',
                $service_areas,
                $service_types
            ]);
            
            echo "<p style='color: green;'>✅ Created contractor: {$contractor_data['business_name']} ({$contractor_data['email']})</p>";
            $created_count++;
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Error creating contractor {$contractor_data['email']}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>Summary</h3>";
    echo "<p style='color: green;'>✅ Successfully created $created_count test contractors</p>";
    echo "<p><a href='contractors.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Contractors</a></p>";
    
} else {
    // Show form to create contractors
    echo "<form method='POST'>";
    echo "<p>This will create 4 test contractors with different statuses:</p>";
    echo "<ul>";
    echo "<li><strong>Liyanage Constructions</strong> - Approved</li>";
    echo "<li><strong>Silva Building Solutions</strong> - Pending</li>";
    echo "<li><strong>Fernando Construction Co.</strong> - Suspended</li>";
    echo "<li><strong>Perera Builders</strong> - Rejected</li>";
    echo "</ul>";
    echo "<button type='submit' name='create_contractors' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Create Test Contractors</button>";
    echo "</form>";
}

echo "<br><br>";
echo "<a href='debug_contractors.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Debug Contractors</a>";
echo "<a href='contractors.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Contractors</a>";
?>
