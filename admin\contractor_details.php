<?php
session_start();
require_once '../config/database.php';

// Service type mapping function
function getServiceTypeName($id) {
    $service_types = [
        1 => 'House Construction',
        2 => 'Building Renovation',
        3 => 'Commercial Construction',
        4 => 'Interior Design & Finishing',
        5 => 'Roofing & Waterproofing',
        6 => 'Electrical Work',
        7 => 'Plumbing & Sanitation',
        8 => 'Landscaping & Gardening',
        9 => 'Swimming Pool Construction',
        10 => 'Road & Infrastructure'
    ];
    return $service_types[$id] ?? 'Unknown Service';
}

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

// Get contractor ID from URL
$contractor_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$contractor_id) {
    $_SESSION['error'] = 'Invalid contractor ID.';
    header('Location: contractors.php');
    exit();
}

// Get contractor details
try {
    // Check which review field structure we're using
    $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'status'");
    $has_status = $stmt->fetch();

    if ($has_status) {
        $review_condition = "(r.status IS NULL OR r.status != 'deleted')";
    } else {
        $review_condition = "(r.is_approved IS NULL OR r.is_approved != 0)";
    }

    $stmt = $pdo->prepare("
        SELECT cp.*, u.email, u.status, u.created_at as user_created_at,
               (SELECT COUNT(*) FROM reviews r WHERE r.contractor_id = u.id AND $review_condition) as review_count,
               (SELECT AVG(rating) FROM reviews r WHERE r.contractor_id = u.id AND $review_condition) as average_rating,
               (SELECT COUNT(*) FROM contractor_portfolios p WHERE p.contractor_id = u.id) as portfolio_count,
               (SELECT COUNT(*) FROM quote_responses qr WHERE qr.contractor_id = u.id) as quote_responses_count
        FROM contractor_profiles cp
        JOIN users u ON cp.user_id = u.id
        WHERE u.id = ? AND u.user_type = 'contractor'
    ");
    $stmt->execute([$contractor_id]);
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        $_SESSION['error'] = 'No contractor found with ID: ' . $contractor_id;
        header('Location: contractors.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: contractors.php');
    exit();
}

// Get portfolio projects
try {
    $stmt = $pdo->prepare("
        SELECT * FROM contractor_portfolios 
        WHERE contractor_id = ? 
        ORDER BY is_featured DESC, completion_date DESC 
        LIMIT 5
    ");
    $stmt->execute([$contractor_id]);
    $portfolio_projects = $stmt->fetchAll();
} catch (PDOException $e) {
    $portfolio_projects = [];
}

// Get recent reviews
try {
    // Check which review field structure we're using
    $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'status'");
    $has_status = $stmt->fetch();

    if ($has_status) {
        $review_condition = "(r.status IS NULL OR r.status != 'deleted')";
    } else {
        $review_condition = "(r.is_approved IS NULL OR r.is_approved != 0)";
    }

    $stmt = $pdo->prepare("
        SELECT r.*, cp.first_name, cp.last_name
        FROM reviews r
        JOIN customer_profiles cp ON r.customer_id = cp.user_id
        WHERE r.contractor_id = ? AND $review_condition
        ORDER BY r.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$contractor_id]);
    $recent_reviews = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_reviews = [];
}

// Get recent quote responses
try {
    // First check if quote_responses table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'quote_responses'");
    $table_exists = $stmt->fetch();

    if ($table_exists) {
        $stmt = $pdo->prepare("
            SELECT qr.*,
                   qreq.title as project_title,
                   qreq.description as project_description,
                   qreq.estimated_budget,
                   qreq.location,
                   qreq.district,
                   cp.first_name,
                   cp.last_name,
                   CASE
                       WHEN pp.id IS NOT NULL THEN 'completed'
                       WHEN qr.status = 'accepted' THEN 'accepted'
                       WHEN qr.status = 'rejected' THEN 'rejected'
                       ELSE 'pending'
                   END as response_status
            FROM quote_responses qr
            LEFT JOIN quote_requests qreq ON qr.quote_request_id = qreq.id
            LEFT JOIN customer_profiles cp ON qreq.customer_id = cp.user_id
            LEFT JOIN project_payments pp ON qr.id = pp.quote_response_id
            WHERE qr.contractor_id = ?
            ORDER BY qr.created_at DESC
            LIMIT 5
        ");
        $stmt->execute([$contractor_id]);
        $recent_quotes = $stmt->fetchAll();
    } else {
        $recent_quotes = [];
    }
} catch (PDOException $e) {
    $recent_quotes = [];
    error_log("Quote responses query error: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Contractor Details - Admin Panel</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF8C00;
            --accent-yellow: #FFD700;
            --light-gray: #F8F9FA;
            --medium-gray: #6C757D;
            --dark-gray: #495057;
            --accent-blue: #4A90E2;
            --accent-green: #27AE60;
            --soft-purple: #8E44AD;
            --warm-orange: #E67E22;
            --gradient-primary: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-red) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent-blue) 0%, var(--soft-purple) 100%);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: var(--light-gray);
            color: var(--dark-gray);
        }
        
        .container-fluid {
            padding: 2rem;
        }
        
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .contractor-header {
            background: var(--gradient-primary);
            color: white;
            padding: 2.5rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 2rem;
            box-shadow: 0 10px 30px rgba(6, 32, 43, 0.3);
            position: relative;
            overflow: hidden;
        }

        .contractor-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(50%, -50%);
        }
        
        .contractor-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-red), var(--accent-orange));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            font-weight: bold;
            box-shadow: 0 10px 25px rgba(197, 23, 46, 0.3);
        }
        
        .contractor-info h1 {
            color: #97a8af;
            margin-bottom: 0.5rem;
        }
        
        .status-badge {
            padding: 0.75rem 1.5rem;
            border-radius: 30px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .status-badge.pending {
            background: linear-gradient(135deg, var(--warm-orange) 0%, #F39C12 100%);
            color: white;
        }

        .status-badge.approved {
            background: linear-gradient(135deg, var(--accent-green) 0%, #2ECC71 100%);
            color: white;
        }

        .status-badge.rejected {
            background: linear-gradient(135deg, var(--primary-red) 0%, #E74C3C 100%);
            color: white;
        }

        .status-badge.suspended {
            background: linear-gradient(135deg, var(--dark-gray) 0%, #34495E 100%);
            color: white;
        }

        .status-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        
        .info-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(74, 144, 226, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--gradient-accent);
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .info-card h3 {
            color: var(--primary-dark);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .info-card h3 i {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gradient-accent);
            color: white;
            border-radius: 8px;
            font-size: 0.875rem;
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.25rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            margin-bottom: 1rem;
            border: 1px solid rgba(74, 144, 226, 0.1);
            transition: all 0.3s ease;
        }

        .detail-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-color: var(--accent-blue);
        }
        
        .detail-item i {
            color: white;
            font-size: 1rem;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gradient-accent);
            border-radius: 8px;
            flex-shrink: 0;
        }
        
        .rating-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 4px;
        }

        .rating-stars {
            display: inline-flex;
            align-items: center;
            gap: 1px;
        }

        .rating-stars i {
            color: #ffc107;
            font-size: 0.9rem;
            line-height: 1;
        }

        .rating-stars i.fa-star-o {
            color: #e9ecef;
        }

        .rating-text {
            font-size: 0.875rem;
            color: #6c757d;
            font-weight: 500;
        }
        
        .btn-back {
            background: var(--medium-gray);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-back:hover {
            background: var(--dark-gray);
            color: white;
            transform: translateY(-2px);
        }
        
        .portfolio-item, .review-item, .quote-item {
            background: var(--light-gray);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .portfolio-item h5, .review-item h5, .quote-item h5 {
            color: var(--primary-dark);
            margin-bottom: 0.5rem;
        }
        
        .text-muted {
            color: var(--medium-gray) !important;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, var(--primary-red), var(--accent-orange));
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h1><i class="fas fa-user-hard-hat me-2"></i>Contractor Details</h1>
                <a href="contractors.php" class="btn-back">
                    <i class="fas fa-arrow-left"></i>Back to Contractors
                </a>
            </div>
            
            <div class="contractor-header">
                <div class="contractor-avatar">
                    <?php echo strtoupper(substr($contractor['business_name'] ?? 'C', 0, 1)); ?>
                </div>
                <div class="contractor-info flex-grow-1">
                    <h1><?php echo htmlspecialchars($contractor['business_name'] ?? 'N/A'); ?></h1>
                    <p class="mb-2"><strong>Contact Person:</strong> <?php echo htmlspecialchars($contractor['contact_person'] ?? 'N/A'); ?></p>
                    <p class="mb-2"><strong>Email:</strong> <?php echo htmlspecialchars($contractor['email']); ?></p>
                    <span class="status-badge <?php echo $contractor['status']; ?>">
                        <?php echo ucfirst($contractor['status']); ?>
                    </span>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($contractor['average_rating'] ?? 0, 1); ?></div>
                    <div class="stat-label">Average Rating</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $contractor['review_count']; ?></div>
                    <div class="stat-label">Total Reviews</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $contractor['portfolio_count']; ?></div>
                    <div class="stat-label">Portfolio Projects</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $contractor['quote_responses_count']; ?></div>
                    <div class="stat-label">Quote Responses</div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="info-card">
            <h3><i class="fas fa-address-card"></i>Contact Information</h3>
            <div class="detail-grid">
                <div class="detail-item">
                    <i class="fas fa-phone"></i>
                    <span><?php echo htmlspecialchars($contractor['phone'] ?? 'N/A'); ?></span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-envelope"></i>
                    <span><?php echo htmlspecialchars($contractor['email']); ?></span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span><?php echo htmlspecialchars($contractor['business_address'] ?? 'N/A'); ?></span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-globe"></i>
                    <span><?php echo $contractor['website'] ? htmlspecialchars($contractor['website']) : 'N/A'; ?></span>
                </div>
            </div>
        </div>

        <!-- Business Information -->
        <div class="info-card">
            <h3><i class="fas fa-building"></i>Business Information</h3>
            <div class="detail-grid">
                <div class="detail-item">
                    <i class="fas fa-certificate"></i>
                    <span>CIDA Registration: <?php echo htmlspecialchars($contractor['cida_registration'] ?? 'N/A'); ?></span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-award"></i>
                    <span>CIDA Grade: <?php echo htmlspecialchars($contractor['cida_grade'] ?? 'N/A'); ?></span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Joined: <?php echo date('F j, Y', strtotime($contractor['user_created_at'])); ?></span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-star"></i>
                    <span>
                        <div class="rating-container">
                            <div class="rating-stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star<?php echo $i <= ($contractor['average_rating'] ?? 0) ? '' : '-o'; ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <span class="rating-text">
                                <?php echo number_format($contractor['average_rating'] ?? 0, 1); ?> (<?php echo $contractor['review_count']; ?> reviews)
                            </span>
                        </div>
                    </span>
                </div>
            </div>
        </div>

        <!-- Service Areas & Types -->
        <div class="row">
            <div class="col-md-6">
                <div class="info-card">
                    <h3><i class="fas fa-map"></i>Service Areas</h3>
                    <?php
                    $service_areas = json_decode($contractor['service_areas'], true);
                    if ($service_areas && is_array($service_areas)):
                    ?>
                        <div class="d-flex flex-wrap gap-2">
                            <?php foreach ($service_areas as $area): ?>
                                <span class="badge bg-primary"><?php echo htmlspecialchars($area); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No service areas specified</p>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-card">
                    <h3><i class="fas fa-tools"></i>Service Types</h3>
                    <?php
                    $service_types = json_decode($contractor['service_types'], true);
                    if ($service_types && is_array($service_types)):
                    ?>
                        <div class="d-flex flex-wrap gap-2">
                            <?php foreach ($service_types as $type): ?>
                                <?php
                                // Check if it's an ID (numeric) or already a name (string)
                                $service_name = is_numeric($type) ? getServiceTypeName($type) : $type;
                                ?>
                                <span class="badge bg-success"><?php echo htmlspecialchars($service_name); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No service types specified</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Business Description -->
        <?php if (!empty($contractor['business_description'])): ?>
        <div class="info-card">
            <h3><i class="fas fa-info-circle"></i>Business Description</h3>
            <p><?php echo nl2br(htmlspecialchars($contractor['business_description'])); ?></p>
        </div>
        <?php endif; ?>

        <!-- Recent Portfolio Projects -->
        <div class="info-card">
            <h3><i class="fas fa-briefcase"></i>Recent Portfolio Projects</h3>
            <?php if (!empty($portfolio_projects)): ?>
                <?php foreach ($portfolio_projects as $project): ?>
                    <div class="portfolio-item">
                        <h5><?php echo htmlspecialchars($project['project_name']); ?></h5>
                        <p class="text-muted mb-2"><?php echo htmlspecialchars($project['project_location']); ?></p>
                        <p><?php echo htmlspecialchars(substr($project['project_description'], 0, 200)) . (strlen($project['project_description']) > 200 ? '...' : ''); ?></p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                Completed: <?php echo date('M Y', strtotime($project['completion_date'])); ?>
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-dollar-sign me-1"></i>
                                Value: Rs. <?php echo number_format($project['project_value']); ?>
                            </small>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p class="text-muted">No portfolio projects available</p>
            <?php endif; ?>
        </div>

        <!-- Recent Reviews -->
        <div class="info-card">
            <h3><i class="fas fa-star"></i>Recent Reviews</h3>
            <?php if (!empty($recent_reviews)): ?>
                <?php foreach ($recent_reviews as $review): ?>
                    <div class="review-item">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5><?php echo htmlspecialchars($review['first_name'] . ' ' . $review['last_name']); ?></h5>
                            <div class="rating-stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star<?php echo $i <= $review['rating'] ? '' : '-o'; ?>"></i>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <p><?php echo htmlspecialchars($review['review_text']); ?></p>
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            <?php echo date('F j, Y', strtotime($review['created_at'])); ?>
                        </small>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p class="text-muted">No reviews available</p>
            <?php endif; ?>
        </div>

        <!-- Recent Quote Responses -->
        <div class="info-card">
            <h3><i class="fas fa-file-invoice-dollar"></i>Recent Quote Responses</h3>
            <?php if (!empty($recent_quotes)): ?>
                <?php foreach ($recent_quotes as $quote): ?>
                    <div class="quote-item">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="mb-0"><?php echo htmlspecialchars($quote['project_title'] ?? 'Project Quote'); ?></h5>
                            <?php if (isset($quote['response_status'])): ?>
                                <span class="badge bg-<?php
                                    echo $quote['response_status'] === 'accepted' ? 'success' :
                                        ($quote['response_status'] === 'completed' ? 'primary' :
                                        ($quote['response_status'] === 'rejected' ? 'danger' : 'warning'));
                                ?>">
                                    <?php echo ucfirst($quote['response_status']); ?>
                                </span>
                            <?php endif; ?>
                        </div>
                        <p class="text-muted mb-2">
                            <i class="fas fa-user me-1"></i>
                            Customer: <?php echo htmlspecialchars(($quote['first_name'] ?? 'Unknown') . ' ' . ($quote['last_name'] ?? 'Customer')); ?>
                        </p>
                        <?php if (!empty($quote['estimated_budget'])): ?>
                        <p class="text-muted mb-2">
                            <i class="fas fa-money-bill-wave me-1"></i>
                            Customer Budget: Rs. <?php echo number_format($quote['estimated_budget']); ?>
                        </p>
                        <?php endif; ?>
                        <?php if (!empty($quote['location'])): ?>
                        <p class="text-muted mb-2">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            Location: <?php echo htmlspecialchars($quote['location'] . ', ' . $quote['district']); ?>
                        </p>
                        <?php endif; ?>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="fw-bold text-success">
                                <i class="fas fa-rupee-sign me-1"></i>
                                Quoted: Rs. <?php echo number_format($quote['quoted_amount'] ?? 0); ?>
                            </span>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo date('M j, Y', strtotime($quote['created_at'])); ?>
                            </small>
                        </div>
                        <?php if (!empty($quote['estimated_timeline'])): ?>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Timeline: <?php echo htmlspecialchars($quote['estimated_timeline']); ?>
                            </small>
                        </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p class="text-muted">No quote responses available</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
