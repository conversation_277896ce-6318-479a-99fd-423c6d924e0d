<?php

class FileUpload {
    private $upload_dir;
    private $allowed_types;
    private $max_file_size;
    private $errors = [];
    
    public function __construct($upload_dir = '../uploads/', $allowed_types = [], $max_file_size = 5242880) {
        $this->upload_dir = rtrim($upload_dir, '/') . '/';
        $this->allowed_types = $allowed_types;
        $this->max_file_size = $max_file_size; // Default 5MB
        
        // Create upload directory if it doesn't exist
        if (!is_dir($this->upload_dir)) {
            mkdir($this->upload_dir, 0755, true);
        }
    }
    
    public function uploadSingle($file, $subfolder = '') {
        error_log("FileUpload::uploadSingle called with file: " . print_r($file, true));

        if (!$this->validateFile($file)) {
            error_log("File validation failed: " . implode(', ', $this->errors));
            return false;
        }

        $upload_path = $this->upload_dir . ($subfolder ? rtrim($subfolder, '/') . '/' : '');
        error_log("Upload path: " . $upload_path);

        // Create subfolder if it doesn't exist
        if (!is_dir($upload_path)) {
            mkdir($upload_path, 0755, true);
            error_log("Created directory: " . $upload_path);
        }

        $filename = $this->generateUniqueFilename($file['name']);
        $full_path = $upload_path . $filename;
        error_log("Full path: " . $full_path);

        if (move_uploaded_file($file['tmp_name'], $full_path)) {
            error_log("File uploaded successfully: " . $filename);
            return $filename;
        } else {
            $this->errors[] = 'Failed to move uploaded file.';
            error_log("Failed to move uploaded file from " . $file['tmp_name'] . " to " . $full_path);
            return false;
        }
    }
    
    public function uploadMultiple($files, $subfolder = '') {
        $uploaded_files = [];
        
        // Handle multiple files
        if (is_array($files['name'])) {
            for ($i = 0; $i < count($files['name']); $i++) {
                if (!empty($files['tmp_name'][$i])) {
                    $file = [
                        'name' => $files['name'][$i],
                        'type' => $files['type'][$i],
                        'tmp_name' => $files['tmp_name'][$i],
                        'error' => $files['error'][$i],
                        'size' => $files['size'][$i]
                    ];
                    
                    $filename = $this->uploadSingle($file, $subfolder);
                    if ($filename) {
                        $uploaded_files[] = $filename;
                    }
                }
            }
        } else {
            // Single file
            $filename = $this->uploadSingle($files, $subfolder);
            if ($filename) {
                $uploaded_files[] = $filename;
            }
        }
        
        return $uploaded_files;
    }
    
    private function validateFile($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $this->errors[] = $this->getUploadErrorMessage($file['error']);
            return false;
        }
        
        // Check file size
        if ($file['size'] > $this->max_file_size) {
            $this->errors[] = 'File size exceeds maximum allowed size (' . $this->formatBytes($this->max_file_size) . ').';
            return false;
        }
        
        // Check file type
        if (!empty($this->allowed_types)) {
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $mime_type = $file['type'];
            
            if (!in_array($file_extension, $this->allowed_types) && !in_array($mime_type, $this->allowed_types)) {
                $this->errors[] = 'File type not allowed. Allowed types: ' . implode(', ', $this->allowed_types);
                return false;
            }
        }
        
        // Additional security checks
        if (!$this->isValidImage($file)) {
            return false;
        }
        
        return true;
    }
    
    private function isValidImage($file) {
        $image_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        
        if (in_array($file['type'], $image_types)) {
            // Verify it's actually an image
            $image_info = getimagesize($file['tmp_name']);
            if ($image_info === false) {
                $this->errors[] = 'File is not a valid image.';
                return false;
            }
        }
        
        return true;
    }
    
    private function generateUniqueFilename($original_name) {
        $extension = pathinfo($original_name, PATHINFO_EXTENSION);
        $filename = pathinfo($original_name, PATHINFO_FILENAME);
        
        // Sanitize filename
        $filename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $filename);
        $filename = substr($filename, 0, 50); // Limit length
        
        // Add timestamp and random string for uniqueness
        $unique_id = uniqid() . '_' . time();
        
        return $filename . '_' . $unique_id . '.' . $extension;
    }
    
    public function getUploadErrorMessage($error_code) {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds the upload_max_filesize directive in php.ini.';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds the MAX_FILE_SIZE directive in the HTML form.';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded.';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded.';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing a temporary folder.';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk.';
            case UPLOAD_ERR_EXTENSION:
                return 'A PHP extension stopped the file upload.';
            default:
                return 'Unknown upload error.';
        }
    }
    
    private function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    public function deleteFile($filename, $subfolder = '') {
        $file_path = $this->upload_dir . ($subfolder ? rtrim($subfolder, '/') . '/' : '') . $filename;
        
        if (file_exists($file_path)) {
            return unlink($file_path);
        }
        
        return false;
    }
    
    public function getErrors() {
        return $this->errors;
    }
    
    public function hasErrors() {
        return !empty($this->errors);
    }
    
    public function clearErrors() {
        $this->errors = [];
    }
    
    // Static helper methods
    public static function getImageUploader() {
        return new self('../uploads/', ['jpg', 'jpeg', 'png', 'gif', 'image/jpeg', 'image/png', 'image/gif'], 5242880);
    }
    
    public static function getDocumentUploader() {
        return new self('../uploads/', ['pdf', 'doc', 'docx', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'], 10485760);
    }
    
    public static function resizeImage($source_path, $destination_path, $max_width = 800, $max_height = 600, $quality = 85) {
        $image_info = getimagesize($source_path);
        if ($image_info === false) {
            return false;
        }
        
        $original_width = $image_info[0];
        $original_height = $image_info[1];
        $image_type = $image_info[2];
        
        // Calculate new dimensions
        $ratio = min($max_width / $original_width, $max_height / $original_height);
        $new_width = round($original_width * $ratio);
        $new_height = round($original_height * $ratio);
        
        // Create image resource from source
        switch ($image_type) {
            case IMAGETYPE_JPEG:
                $source_image = imagecreatefromjpeg($source_path);
                break;
            case IMAGETYPE_PNG:
                $source_image = imagecreatefrompng($source_path);
                break;
            case IMAGETYPE_GIF:
                $source_image = imagecreatefromgif($source_path);
                break;
            default:
                return false;
        }
        
        if (!$source_image) {
            return false;
        }
        
        // Create new image
        $new_image = imagecreatetruecolor($new_width, $new_height);
        
        // Preserve transparency for PNG and GIF
        if ($image_type == IMAGETYPE_PNG || $image_type == IMAGETYPE_GIF) {
            imagealphablending($new_image, false);
            imagesavealpha($new_image, true);
            $transparent = imagecolorallocatealpha($new_image, 255, 255, 255, 127);
            imagefilledrectangle($new_image, 0, 0, $new_width, $new_height, $transparent);
        }
        
        // Resize image
        imagecopyresampled($new_image, $source_image, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
        
        // Save resized image
        $result = false;
        switch ($image_type) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg($new_image, $destination_path, $quality);
                break;
            case IMAGETYPE_PNG:
                $result = imagepng($new_image, $destination_path);
                break;
            case IMAGETYPE_GIF:
                $result = imagegif($new_image, $destination_path);
                break;
        }
        
        // Clean up
        imagedestroy($source_image);
        imagedestroy($new_image);
        
        return $result;
    }
}

?>
