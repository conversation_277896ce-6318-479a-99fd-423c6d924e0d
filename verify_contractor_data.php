<?php
require_once 'config/database.php';

echo "<h2>🔍 Contractor Data Verification</h2>";

try {
    // Check all contractors
    $stmt = $pdo->query("
        SELECT u.id, u.email, u.status, cp.business_name, cp.service_areas, cp.service_types
        FROM users u 
        LEFT JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor'
        ORDER BY u.status, u.id
    ");
    $contractors = $stmt->fetchAll();
    
    echo "<h3>All Contractors in System</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Email</th><th>Status</th><th>Business Name</th><th>Service Areas</th><th>Service Types</th><th>Issues</th></tr>";
    
    $approved_contractors = 0;
    $contractors_with_data = 0;
    
    foreach ($contractors as $contractor) {
        $issues = [];
        
        if (!$contractor['business_name']) {
            $issues[] = "No business name";
        }
        
        if (!$contractor['service_areas'] || $contractor['service_areas'] === '[]' || $contractor['service_areas'] === '') {
            $issues[] = "No service areas";
        } else {
            $areas = json_decode($contractor['service_areas'], true);
            if (!is_array($areas) || empty($areas)) {
                $issues[] = "Invalid service areas JSON";
            }
        }
        
        if (!$contractor['service_types'] || $contractor['service_types'] === '[]' || $contractor['service_types'] === '') {
            $issues[] = "No service types";
        } else {
            $types = json_decode($contractor['service_types'], true);
            if (!is_array($types) || empty($types)) {
                $issues[] = "Invalid service types JSON";
            }
        }
        
        if ($contractor['status'] === 'approved') {
            $approved_contractors++;
            if (empty($issues)) {
                $contractors_with_data++;
            }
        }
        
        $issues_text = empty($issues) ? "✅ OK" : "❌ " . implode(", ", $issues);
        $row_color = empty($issues) && $contractor['status'] === 'approved' ? 'background-color: #d4edda;' : '';
        
        echo "<tr style='$row_color'>";
        echo "<td>" . $contractor['id'] . "</td>";
        echo "<td>" . htmlspecialchars($contractor['email']) . "</td>";
        echo "<td>" . $contractor['status'] . "</td>";
        echo "<td>" . htmlspecialchars($contractor['business_name'] ?: 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars(substr($contractor['service_areas'] ?: 'N/A', 0, 50)) . "</td>";
        echo "<td>" . htmlspecialchars(substr($contractor['service_types'] ?: 'N/A', 0, 50)) . "</td>";
        echo "<td>$issues_text</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Summary</h3>";
    echo "<ul>";
    echo "<li>Total contractors: " . count($contractors) . "</li>";
    echo "<li>Approved contractors: $approved_contractors</li>";
    echo "<li>Approved contractors with complete data: $contractors_with_data</li>";
    echo "</ul>";
    
    if ($contractors_with_data == 0) {
        echo "<p style='color: red; font-weight: bold;'>❌ No contractors have complete data! This is why quotes aren't showing.</p>";
        echo "<p><strong>To fix this:</strong></p>";
        echo "<ol>";
        echo "<li>Contractors need to complete their profiles</li>";
        echo "<li>Service areas and service types must be properly set</li>";
        echo "<li>Data must be in valid JSON format</li>";
        echo "</ol>";
    } else {
        echo "<p style='color: green; font-weight: bold;'>✅ Found $contractors_with_data contractors with complete data</p>";
    }
    
    // Check quotes in system
    echo "<h3>Quotes in System</h3>";
    
    $stmt = $pdo->query("
        SELECT qr.id, qr.title, qr.district, qr.service_category_id, qr.specific_contractor_id, qr.status,
               sc.name_en as service_name
        FROM quote_requests qr
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        ORDER BY qr.created_at DESC
        LIMIT 10
    ");
    $quotes = $stmt->fetchAll();
    
    if (empty($quotes)) {
        echo "<p style='color: red;'>❌ No quotes found in the system</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>District</th><th>Service</th><th>Type</th><th>Status</th></tr>";
        
        foreach ($quotes as $quote) {
            $quote_type = $quote['specific_contractor_id'] ? 'Specific' : 'General';
            echo "<tr>";
            echo "<td>" . $quote['id'] . "</td>";
            echo "<td>" . htmlspecialchars($quote['title']) . "</td>";
            echo "<td>" . $quote['district'] . "</td>";
            echo "<td>" . htmlspecialchars($quote['service_name']) . " (ID: " . $quote['service_category_id'] . ")</td>";
            echo "<td>$quote_type</td>";
            echo "<td>" . $quote['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test matching for first contractor with complete data
    if ($contractors_with_data > 0) {
        echo "<h3>Test Matching for First Complete Contractor</h3>";
        
        foreach ($contractors as $contractor) {
            if ($contractor['status'] === 'approved' && 
                $contractor['service_areas'] && $contractor['service_areas'] !== '[]' &&
                $contractor['service_types'] && $contractor['service_types'] !== '[]') {
                
                $contractor_id = $contractor['id'];
                $service_areas = json_decode($contractor['service_areas'], true);
                $service_types = json_decode($contractor['service_types'], true);
                
                echo "<p><strong>Testing contractor:</strong> " . htmlspecialchars($contractor['business_name']) . " (ID: $contractor_id)</p>";
                echo "<p><strong>Service Areas:</strong> " . implode(', ', $service_areas) . "</p>";
                echo "<p><strong>Service Types:</strong> " . implode(', ', $service_types) . "</p>";
                
                // Check which quotes would match
                $matching_quotes = 0;
                foreach ($quotes as $quote) {
                    $would_match = false;
                    
                    if ($quote['specific_contractor_id'] == $contractor_id) {
                        $would_match = true;
                    } elseif ($quote['specific_contractor_id'] === null) {
                        $has_area = in_array($quote['district'], $service_areas);
                        $has_service = in_array((int)$quote['service_category_id'], $service_types) ||
                                      in_array((string)$quote['service_category_id'], $service_types);
                        
                        if ($has_area && $has_service) {
                            $would_match = true;
                        }
                    }
                    
                    if ($would_match) {
                        $matching_quotes++;
                        echo "<div style='border: 1px solid green; padding: 5px; margin: 2px;'>";
                        echo "✅ Quote ID " . $quote['id'] . ": " . htmlspecialchars($quote['title']);
                        echo " (District: " . $quote['district'] . ", Service: " . $quote['service_category_id'] . ")";
                        echo "</div>";
                    }
                }
                
                echo "<p><strong>Total matching quotes:</strong> $matching_quotes</p>";
                break;
            }
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
