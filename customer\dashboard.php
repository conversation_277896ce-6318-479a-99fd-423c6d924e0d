<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

// Get customer profile
try {
    $stmt = $pdo->prepare("SELECT cp.*, u.email FROM customer_profiles cp JOIN users u ON cp.user_id = u.id WHERE cp.user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $customer = $stmt->fetch();

    if (!$customer) {
        $_SESSION['error'] = 'Customer profile not found.';
        header('Location: ../login.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: ../login.php');
    exit();
}

// Get unread notifications count
$unread_count = 0;
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$_SESSION['user_id']]);
    $unread_count = $stmt->fetchColumn();
} catch (PDOException $e) {
    // Ignore error, keep count as 0
}

// Get featured contractors
try {
    $stmt = $pdo->prepare("
        SELECT cp.*, u.email, u.status,
               (SELECT COUNT(*) FROM reviews r WHERE r.contractor_id = u.id AND r.is_approved = 1) as review_count
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        ORDER BY cp.average_rating DESC, cp.total_projects DESC 
        LIMIT 6
    ");
    $stmt->execute();
    $featured_contractors = $stmt->fetchAll();
} catch (PDOException $e) {
    $featured_contractors = [];
}

// Get service categories
try {
    $stmt = $pdo->prepare("SELECT * FROM service_categories WHERE is_active = 1 ORDER BY name_en");
    $stmt->execute();
    $service_categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $service_categories = [];
}

// Get recent quote requests
try {
    $stmt = $pdo->prepare("
        SELECT qr.*, sc.name_en as service_name,
               (SELECT COUNT(*) FROM quote_responses qres WHERE qres.quote_request_id = qr.id) as response_count
        FROM quote_requests qr
        JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE qr.customer_id = ?
        ORDER BY qr.created_at DESC
        LIMIT 3
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $recent_quotes = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_quotes = [];
}

// Get favorites count
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM customer_favorites WHERE customer_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $favorites_count = $stmt->fetchColumn() ?: 0;
} catch (PDOException $e) {
    $favorites_count = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Customer Dashboard - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, customer dashboard" name="keywords">
    <meta content="Customer Dashboard - Brick & Click" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <style>
        .marketplace-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .marketplace-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
            z-index: 1;
        }

        .marketplace-header .container {
            position: relative;
            z-index: 2;
        }

        .welcome-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 25px;
            padding: 3rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .search-bar {
            background: white;
            border-radius: 50px;
            padding: 0.5rem;
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .search-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(253, 126, 20, 0.1), transparent);
            transition: left 0.5s;
        }

        .search-bar:hover::before {
            left: 100%;
        }

        .search-bar:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.2);
        }

        .search-bar .form-control {
            border: none;
            border-radius: 50px;
            padding: 1rem 1.5rem;
            font-size: 1.1rem;
            position: relative;
            z-index: 2;
        }

        .search-bar .btn {
            border-radius: 50px;
            padding: 1rem 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .search-bar .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(253, 126, 20, 0.3);
        }
        
        .category-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.4s ease;
            border: none;
            height: 100%;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea, #764ba2);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .category-card:hover::before {
            opacity: 0.1;
        }

        .category-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(102, 126, 234, 0.2);
        }

        .category-card * {
            position: relative;
            z-index: 2;
        }

        .category-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
            box-shadow: 0 10px 25px rgba(253, 126, 20, 0.3);
            transition: all 0.3s ease;
            animation: float 3s ease-in-out infinite;
        }

        .category-card:hover .category-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #667eea, #764ba2);
        }
        
        .contractor-card {
            background: linear-gradient(145deg, #f8f9ff, #e8e9ff);
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.4s ease;
            border: 1px solid rgba(102, 126, 234, 0.1);
            height: 100%;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.15);
            position: relative;
        }

        .contractor-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.2), transparent);
            transition: left 0.5s;
        }

        .contractor-card:hover::before {
            left: 100%;
        }

        .contractor-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(102, 126, 234, 0.25);
        }

        .contractor-avatar {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            background: linear-gradient(135deg, #C5172E, #FF6B35);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.2rem;
            margin: 0 auto;
            box-shadow: 0 10px 25px rgba(197, 23, 46, 0.3);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .contractor-avatar .profile-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .contractor-card:hover .contractor-avatar {
            transform: scale(1.1);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .section-title {
            position: relative;
            display: inline-block;
            margin-bottom: 3rem;
            font-weight: 700;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .stats-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .stats-card:hover::before {
            left: 100%;
        }

        .stats-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            animation: pulse 2s infinite;
        }

        .stats-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin: 0 auto 1rem;
            box-shadow: 0 5px 15px rgba(253, 126, 20, 0.3);
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-elements::before,
        .floating-elements::after {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-elements::before {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-elements::after {
            bottom: 10%;
            right: 10%;
            animation-delay: 3s;
        }

        .btn-primary {
            background: linear-gradient(135deg, #C5172E, #FF6B35);
            border: none;
            border-radius: 15px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(197, 23, 46, 0.3);
            background: linear-gradient(135deg, #A01426, #E55A2B);
        }
        
        .rating-stars {
            color: #ffc107;
        }

        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            background: transparent;
            border-radius: 15px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-align: center;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-color: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        .cida-badge {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border-left: 4px solid #fd7e14;
        }

        .stats-card.favorites-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border-left: 4px solid #fd7e14;
        }


        .stats-number {
            font-size: 2rem;
            font-weight: 600;
            color: #fd7e14;
        }
        
        .quick-action-btn {
            background: linear-gradient(135deg, #fd7e14, #ff9500);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 1rem;
        }
        
        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(253, 126, 20, 0.3);
            color: white;
        }
        
        .quote-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            border-left: 4px solid #fd7e14;
            margin-bottom: 1rem;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-open {
            background: #d4edda;
            color: #155724;
        }
        
        .status-closed {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>

<body>
    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
        <a href="../index.php" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="../img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link active">Dashboard</a>
                <a href="contractors.php" class="nav-item nav-link">Find Contractors</a>
                <a href="quotes.php" class="nav-item nav-link">My Quotes</a>
                <a href="cost_estimator.php" class="nav-item nav-link">Cost Estimator</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
            </div>
            <div class="dropdown">
                <a href="#" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($customer['first_name']); ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="notifications.php"><i class="fas fa-bell me-2"></i>Notifications <?php if ($unread_count > 0): ?><span class="badge bg-danger"><?php echo $unread_count; ?></span><?php endif; ?></a></li>
                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Marketplace Header Start -->
    <div class="marketplace-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="welcome-card">
                        <h1 class="mb-3">Welcome back, <?php echo htmlspecialchars($customer['first_name']); ?>!</h1>
                        <p class="mb-4">Find verified contractors for your construction projects. Browse, compare, and connect with CIDA-certified professionals.</p>
                        
                        <!-- Search Bar -->
                        <div class="search-bar">
                            <form action="contractors.php" method="GET" class="d-flex">
                                <input type="text" class="form-control me-2" name="search" placeholder="Search contractors, services, or locations...">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="text-center text-lg-end">
                        <img src="../img/icons/icon-1.png" alt="Brick & Click" style="max-width: 150px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Marketplace Header End -->

    <!-- Quick Stats Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="fas fa-quote-right"></i>
                        </div>
                        <div class="stats-number"><?php echo count($recent_quotes); ?></div>
                        <h6 class="mb-0">Active Quotes</h6>
                        <small class="text-muted">Pending responses</small>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stats-number"><?php echo count($featured_contractors); ?>+</div>
                        <h6 class="mb-0">Verified Contractors</h6>
                        <small class="text-muted">Available for hire</small>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card favorites-card">
                        <div class="stats-icon favorites-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="stats-number"><?php echo $favorites_count; ?></div>
                        <h6 class="mb-0">Saved Favorites</h6>
                        <small class="text-muted">Bookmarked contractors</small>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stats-number">0</div>
                        <h6 class="mb-0">Completed Projects</h6>
                        <small class="text-muted">Successfully finished</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Quick Stats End -->

    <!-- Service Categories Start -->
    <div class="container-xxl py-5 bg-light">
        <div class="container">
            <div class="text-center mx-auto mb-5">
                <h2 class="display-6 mb-4">Construction Services We Offer</h2>
                <p class="text-muted">Professional contractors for every aspect of your construction project</p>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <a href="contractors.php?category=1" class="text-decoration-none">
                        <div class="category-card">
                            <div class="category-icon">
                                <i class="fas fa-home"></i>
                            </div>
                            <h5 class="mb-2">House Construction</h5>
                            <p class="text-muted mb-0">Complete residential construction from foundation to finishing. New homes, extensions, and custom builds.</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6">
                    <a href="contractors.php?category=2" class="text-decoration-none">
                        <div class="category-card">
                            <div class="category-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <h5 class="mb-2">Building Renovation</h5>
                            <p class="text-muted mb-0">Modernize and upgrade existing buildings. Kitchen, bathroom, and full property renovations.</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6">
                    <a href="contractors.php?category=3" class="text-decoration-none">
                        <div class="category-card">
                            <div class="category-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <h5 class="mb-2">Commercial Construction</h5>
                            <p class="text-muted mb-0">Office buildings, retail spaces, warehouses, and commercial complexes for business needs.</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6">
                    <a href="contractors.php?category=4" class="text-decoration-none">
                        <div class="category-card">
                            <div class="category-icon">
                                <i class="fas fa-paint-roller"></i>
                            </div>
                            <h5 class="mb-2">Interior Design & Finishing</h5>
                            <p class="text-muted mb-0">Professional interior design, painting, flooring, and finishing touches for beautiful spaces.</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6">
                    <a href="contractors.php?category=5" class="text-decoration-none">
                        <div class="category-card">
                            <div class="category-icon">
                                <i class="fas fa-warehouse"></i>
                            </div>
                            <h5 class="mb-2">Roofing & Waterproofing</h5>
                            <p class="text-muted mb-0">Expert roofing installation, repairs, and comprehensive waterproofing solutions.</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6">
                    <a href="contractors.php?category=6" class="text-decoration-none">
                        <div class="category-card">
                            <div class="category-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <h5 class="mb-2">Electrical Work</h5>
                            <p class="text-muted mb-0">Licensed electrical installations, wiring, lighting, and power system maintenance.</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6">
                    <a href="contractors.php?category=7" class="text-decoration-none">
                        <div class="category-card">
                            <div class="category-icon">
                                <i class="fas fa-wrench"></i>
                            </div>
                            <h5 class="mb-2">Plumbing & Sanitation</h5>
                            <p class="text-muted mb-0">Professional plumbing installations, pipe work, and sanitary system solutions.</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6">
                    <a href="contractors.php?category=8" class="text-decoration-none">
                        <div class="category-card">
                            <div class="category-icon">
                                <i class="fas fa-seedling"></i>
                            </div>
                            <h5 class="mb-2">Landscaping & Gardening</h5>
                            <p class="text-muted mb-0">Beautiful garden design, landscaping, and outdoor space transformation services.</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6">
                    <a href="contractors.php?category=9" class="text-decoration-none">
                        <div class="category-card">
                            <div class="category-icon">
                                <i class="fas fa-swimming-pool"></i>
                            </div>
                            <h5 class="mb-2">Swimming Pool Construction</h5>
                            <p class="text-muted mb-0">Custom swimming pool design and construction for residential and commercial properties.</p>
                        </div>
                    </a>
                </div>
                <div class="col-lg-4 col-md-6">
                    <a href="contractors.php?category=10" class="text-decoration-none">
                        <div class="category-card">
                            <div class="category-icon">
                                <i class="fas fa-road"></i>
                            </div>
                            <h5 class="mb-2">Road & Infrastructure</h5>
                            <p class="text-muted mb-0">Road construction, drainage systems, and infrastructure development projects.</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Service Categories End -->

    <!-- Featured Contractors Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="row align-items-center mb-5">
                <div class="col-lg-8">
                    <h2 class="display-6 mb-2">Top Rated Contractors</h2>
                    <p class="text-muted">Highly rated and verified contractors in your area</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <a href="contractors.php" class="btn btn-outline-primary">View All Contractors</a>
                </div>
            </div>
            
            <div class="row g-4">
                <?php foreach ($featured_contractors as $contractor): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="contractor-card">
                        <div class="p-4">
                            <div class="text-center mb-3">
                                <div class="contractor-avatar">
                                    <i class="fas fa-hard-hat"></i>
                                </div>
                                <h5 class="mt-3 mb-1"><?php echo htmlspecialchars($contractor['business_name']); ?></h5>
                                <span class="cida-badge">CIDA <?php echo htmlspecialchars($contractor['cida_grade']); ?></span>
                            </div>
                            
                            <div class="text-center mb-3">
                                <div class="rating-stars mb-1">
                                    <?php
                                    $rating = $contractor['average_rating'];
                                    for ($i = 1; $i <= 5; $i++) {
                                        if ($i <= $rating) {
                                            echo '<i class="fas fa-star"></i>';
                                        } elseif ($i - 0.5 <= $rating) {
                                            echo '<i class="fas fa-star-half-alt"></i>';
                                        } else {
                                            echo '<i class="far fa-star"></i>';
                                        }
                                    }
                                    ?>
                                </div>
                                <small class="text-muted"><?php echo number_format($rating, 1); ?> (<?php echo $contractor['review_count']; ?> reviews)</small>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted d-block"><i class="fas fa-map-marker-alt me-1"></i>
                                    <?php 
                                    $areas = json_decode($contractor['service_areas'], true);
                                    echo htmlspecialchars(implode(', ', array_slice($areas, 0, 2)));
                                    if (count($areas) > 2) echo ' +' . (count($areas) - 2) . ' more';
                                    ?>
                                </small>
                                <small class="text-muted d-block"><i class="fas fa-tools me-1"></i><?php echo $contractor['total_projects']; ?> Projects Completed</small>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <a href="contractor_profile.php?id=<?php echo $contractor['user_id']; ?>" class="btn btn-outline-primary btn-sm flex-fill">View Profile</a>
                                <a href="request_quote.php?contractor=<?php echo $contractor['user_id']; ?>" class="btn btn-primary btn-sm flex-fill">Request Quote</a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <!-- Featured Contractors End -->

    <!-- Recent Activity Start -->
    <div class="container-xxl py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <h3 class="mb-4">Recent Quote Requests</h3>
                    <?php if (empty($recent_quotes)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5>No quote requests yet</h5>
                            <p class="text-muted">Start by requesting quotes from contractors</p>
                            <a href="contractors.php" class="btn btn-primary">Find Contractors</a>
                        </div>
                    <?php else: ?>
                        <?php foreach ($recent_quotes as $quote): ?>
                        <div class="quote-card">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0"><?php echo htmlspecialchars($quote['title']); ?></h6>
                                <span class="status-badge status-<?php echo $quote['status']; ?>">
                                    <?php echo ucfirst($quote['status']); ?>
                                </span>
                            </div>
                            <p class="text-muted mb-2"><?php echo htmlspecialchars($quote['service_name']); ?> • <?php echo htmlspecialchars($quote['location']); ?></p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i><?php echo date('M j, Y', strtotime($quote['created_at'])); ?>
                                </small>
                                <small class="text-primary">
                                    <i class="fas fa-reply me-1"></i><?php echo $quote['response_count']; ?> responses
                                </small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <div class="text-center">
                            <a href="quotes.php" class="btn btn-outline-primary">View All Quotes</a>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-lg-4">
                    <h3 class="mb-4">Quick Actions</h3>
                    <div class="bg-white p-4 rounded-3">
                        <button class="quick-action-btn" onclick="location.href='request_quote.php'">
                            <i class="fas fa-plus me-2"></i>Request New Quote
                        </button>
                        <button class="quick-action-btn" onclick="location.href='contractors.php'">
                            <i class="fas fa-search me-2"></i>Browse Contractors
                        </button>
                        <button class="quick-action-btn" onclick="location.href='cost_estimator.php'">
                            <i class="fas fa-calculator me-2"></i>Cost Estimator
                        </button>
                        <button class="quick-action-btn" onclick="location.href='favorites.php'">
                            <i class="fas fa-heart me-2"></i>My Favorites
                        </button>
                    </div>
                    
                    <div class="mt-4 bg-white p-4 rounded-3">
                        <h5 class="mb-3">Need Help?</h5>
                        <p class="text-muted mb-3">Our support team is here to help you find the right contractor for your project.</p>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-phone text-primary me-2"></i>
                            <span>+94 77 123 4567</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-envelope text-primary me-2"></i>
                            <span><EMAIL></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Recent Activity End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer mt-5 pt-5 px-0">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Brick & Click</h3>
                    <p class="mb-2 text-light">Sri Lanka's leading construction contractor marketplace</p>
                    <p class="mb-2 text-light"><i class="fa fa-phone-alt text-primary me-3"></i>+94 77 123 4567</p>
                    <p class="mb-2 text-light"><i class="fa fa-envelope text-primary me-3"></i><EMAIL></p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Quick Links</h3>
                    <a class="btn btn-link text-light" href="contractors.php">Find Contractors</a>
                    <a class="btn btn-link text-light" href="quotes.php">My Quotes</a>
                    <a class="btn btn-link text-light" href="cost_estimator.php">Cost Estimator</a>
                    <a class="btn btn-link text-light" href="favorites.php">Favorites</a>
                    <a class="btn btn-link text-light" href="profile.php">Profile</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Services</h3>
                    <a class="btn btn-link text-light" href="contractors.php?category=1">House Construction</a>
                    <a class="btn btn-link text-light" href="contractors.php?category=2">Building Renovation</a>
                    <a class="btn btn-link text-light" href="contractors.php?category=3">Commercial Construction</a>
                    <a class="btn btn-link text-light" href="contractors.php?category=5">Roofing & Waterproofing</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Support</h3>
                    <a class="btn btn-link text-light" href="#">Help Center</a>
                    <a class="btn btn-link text-light" href="#">Contact Support</a>
                    <a class="btn btn-link text-light" href="#">Terms of Service</a>
                    <a class="btn btn-link text-light" href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="container-fluid copyright">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        <span class="text-light">&copy; <a href="#" class="text-primary">Brick & Click</a>, All Right Reserved.</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
</body>
</html>
