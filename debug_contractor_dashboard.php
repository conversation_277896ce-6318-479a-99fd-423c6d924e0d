<?php
session_start();
require_once 'config/database.php';

// For testing, let's use the first approved contractor
$stmt = $pdo->query("
    SELECT u.id, cp.business_name, cp.service_types, cp.service_areas
    FROM users u
    JOIN contractor_profiles cp ON u.id = cp.user_id
    WHERE u.user_type = 'contractor' AND u.status = 'approved'
    LIMIT 1
");
$test_contractor = $stmt->fetch();

if (!$test_contractor) {
    echo "<p style='color: red;'>❌ No approved contractors found</p>";
    exit;
}

$contractor_id = $test_contractor['id'];
$contractor = $test_contractor;

echo "<h2>🔍 Debug Contractor Dashboard</h2>";
echo "<p>Testing with contractor: " . htmlspecialchars($test_contractor['business_name']) . " (ID: $contractor_id)</p>";

echo "<h3>Contractor Services & Areas</h3>";
$services = json_decode($contractor['service_types'], true) ?: [];
$areas = json_decode($contractor['service_areas'], true) ?: [];
echo "<p><strong>Services:</strong> " . implode(', ', $services) . "</p>";
echo "<p><strong>Areas:</strong> " . implode(', ', $areas) . "</p>";

try {
    // Step 1: Check all open quotes
    echo "<h3>Step 1: All Open Quotes</h3>";
    
    $stmt = $pdo->query("
        SELECT qr.*, sc.name_en as service_name
        FROM quote_requests qr
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE qr.status = 'open'
        ORDER BY qr.created_at DESC
    ");
    $all_open_quotes = $stmt->fetchAll();
    
    echo "<p>Found " . count($all_open_quotes) . " open quotes in database</p>";
    
    if (count($all_open_quotes) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Service</th><th>District</th><th>Type</th><th>Created</th></tr>";
        foreach ($all_open_quotes as $quote) {
            $type = $quote['specific_contractor_id'] ? 'Specific' : 'General';
            echo "<tr>";
            echo "<td>" . $quote['id'] . "</td>";
            echo "<td>" . htmlspecialchars(substr($quote['title'], 0, 30)) . "...</td>";
            echo "<td>" . htmlspecialchars($quote['service_name'] ?? 'Unknown') . "</td>";
            echo "<td>" . htmlspecialchars($quote['district']) . "</td>";
            echo "<td>" . $type . "</td>";
            echo "<td>" . $quote['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 2: Check quotes this contractor has responded to
    echo "<h3>Step 2: Contractor's Responses</h3>";
    
    $stmt = $pdo->prepare("
        SELECT qr.quote_request_id, qr.quoted_amount, qr.status, qr.created_at
        FROM quote_responses qr
        WHERE qr.contractor_id = ?
        ORDER BY qr.created_at DESC
    ");
    $stmt->execute([$contractor_id]);
    $responses = $stmt->fetchAll();
    
    echo "<p>Contractor has responded to " . count($responses) . " quotes</p>";
    
    if (count($responses) > 0) {
        echo "<ul>";
        foreach ($responses as $response) {
            echo "<li>Quote ID {$response['quote_request_id']}: LKR " . number_format($response['quoted_amount']) . " ({$response['status']})</li>";
        }
        echo "</ul>";
    }
    
    // Step 3: Dashboard query (exact copy from dashboard.php)
    echo "<h3>Step 3: Dashboard Query</h3>";
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        ORDER BY qr.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$contractor_id, $contractor_id]);
    $all_recent_quotes = $stmt->fetchAll();
    
    echo "<p>Dashboard query returned " . count($all_recent_quotes) . " quotes</p>";
    
    // Step 4: Filter quotes (exact copy from dashboard.php)
    echo "<h3>Step 4: Filtering Process</h3>";
    
    $recent_quotes = [];
    foreach ($all_recent_quotes as $quote) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0;'>";
        echo "<h4>Quote ID {$quote['id']}: " . htmlspecialchars($quote['title']) . "</h4>";
        echo "<p><strong>Service ID:</strong> {$quote['service_category_id']}</p>";
        echo "<p><strong>District:</strong> {$quote['district']}</p>";
        echo "<p><strong>Specific Contractor ID:</strong> " . ($quote['specific_contractor_id'] ?: 'NULL (General)') . "</p>";
        echo "<p><strong>Has Responded:</strong> " . ($quote['has_responded'] ? 'Yes' : 'No') . "</p>";
        
        $include_quote = false;
        $reason = "";
        
        // Always show direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $recent_quotes[] = $quote;
            $include_quote = true;
            $reason = "Direct quote for this contractor";
        }
        // For general quotes (no specific contractor), check service and area match
        elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($contractor['service_types'], true) ?: [];
            $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];
            
            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            
            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);
            
            echo "<p><strong>Service Match:</strong> " . ($has_service ? 'Yes' : 'No') . " (looking for {$quote['service_category_id']} in [" . implode(', ', $contractor_services) . "])</p>";
            echo "<p><strong>Area Match:</strong> " . ($has_area ? 'Yes' : 'No') . " (looking for {$quote['district']} in [" . implode(', ', $contractor_areas) . "])</p>";
            
            if ($has_service && $has_area) {
                $recent_quotes[] = $quote;
                $include_quote = true;
                $reason = "General quote - service and area match";
            } else {
                $reason = "General quote - no match (service: " . ($has_service ? 'yes' : 'no') . ", area: " . ($has_area ? 'yes' : 'no') . ")";
            }
        } else {
            $reason = "Direct quote for different contractor (ID: {$quote['specific_contractor_id']})";
        }
        
        echo "<p><strong>Result:</strong> " . ($include_quote ? '✅ INCLUDED' : '❌ EXCLUDED') . " - $reason</p>";
        echo "</div>";
        
        // Limit to 5 quotes for dashboard
        if (count($recent_quotes) >= 5) {
            break;
        }
    }
    
    echo "<h3>Step 5: Final Results</h3>";
    echo "<p><strong>Total quotes to display:</strong> " . count($recent_quotes) . "</p>";
    
    if (count($recent_quotes) > 0) {
        echo "<ul>";
        foreach ($recent_quotes as $quote) {
            echo "<li><strong>ID {$quote['id']}:</strong> " . htmlspecialchars($quote['title']) . " - {$quote['first_name']} {$quote['last_name']} ({$quote['district']})</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ <strong>NO QUOTES TO DISPLAY</strong> - This explains the empty dashboard!</p>";
    }
    
    // Step 6: Check pending quotes count
    echo "<h3>Step 6: Pending Quotes Count</h3>";
    
    $stmt = $pdo->prepare("
        SELECT qr.*
        FROM quote_requests qr
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.id NOT IN (
            SELECT COALESCE(quote_request_id, 0)
            FROM quote_responses
            WHERE contractor_id = ?
        )
    ");
    $stmt->execute([$contractor_id, $contractor_id]);
    $all_pending_quotes = $stmt->fetchAll();
    
    $pending_quotes = 0;
    foreach ($all_pending_quotes as $quote) {
        // Always count direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $pending_quotes++;
            continue;
        }
        
        // For general quotes (no specific contractor), check service and area match
        if ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($contractor['service_types'], true) ?: [];
            $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];
            
            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            
            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $pending_quotes++;
            }
        }
    }
    
    echo "<p><strong>Pending quotes count:</strong> $pending_quotes</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>
