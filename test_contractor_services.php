<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔧 Test Contractor Services Configuration</h2>";

// Get <NAME_EMAIL>
$stmt = $pdo->prepare("
    SELECT u.id, u.email, u.status, cp.business_name, cp.service_types, cp.service_areas
    FROM users u
    LEFT JOIN contractor_profiles cp ON u.id = cp.user_id
    WHERE u.email = ?
");
$stmt->execute(['<EMAIL>']);
$contractor = $stmt->fetch();

if (!$contractor) {
    echo "<p>❌ Contractor not found!</p>";
    exit;
}

echo "<h3>👷 Contractor Details:</h3>";
echo "<ul>";
echo "<li><strong>ID:</strong> {$contractor['id']}</li>";
echo "<li><strong>Email:</strong> {$contractor['email']}</li>";
echo "<li><strong>Status:</strong> {$contractor['status']}</li>";
echo "<li><strong>Business Name:</strong> " . ($contractor['business_name'] ?: 'NULL') . "</li>";
echo "<li><strong>Service Types (Raw):</strong> " . ($contractor['service_types'] ?: 'NULL') . "</li>";
echo "<li><strong>Service Areas (Raw):</strong> " . ($contractor['service_areas'] ?: 'NULL') . "</li>";
echo "</ul>";

if (!$contractor['service_types'] || !$contractor['service_areas']) {
    echo "<p>❌ <strong>PROBLEM FOUND:</strong> Contractor profile is incomplete!</p>";
    echo "<p>This contractor needs to complete their profile with service types and service areas.</p>";
    
    // Check if profile exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM contractor_profiles WHERE user_id = ?");
    $stmt->execute([$contractor['id']]);
    $profile_exists = $stmt->fetchColumn();
    
    if ($profile_exists == 0) {
        echo "<p>❌ No contractor profile found! This contractor needs to create their profile.</p>";
    } else {
        echo "<p>✅ Profile exists but service types/areas are empty.</p>";
    }
    exit;
}

$services = json_decode($contractor['service_types'], true) ?: [];
$areas = json_decode($contractor['service_areas'], true) ?: [];

echo "<h3>📋 Parsed Configuration:</h3>";
echo "<p><strong>Services:</strong> [" . implode(', ', $services) . "]</p>";
echo "<p><strong>Areas:</strong> [" . implode(', ', $areas) . "]</p>";

// Get service categories
echo "<h3>🔍 Service Categories:</h3>";
$stmt = $pdo->query("SELECT id, name_en FROM service_categories ORDER BY id");
$categories = $stmt->fetchAll();

foreach ($categories as $category) {
    $has_service = in_array($category['id'], $services) || in_array((string)$category['id'], $services);
    echo "<p>ID {$category['id']}: {$category['name_en']} - " . ($has_service ? '✅ HAS' : '❌ NO') . "</p>";
}

// Check areas
echo "<h3>🗺️ Service Areas:</h3>";
$districts = ['Colombo', 'Gampaha', 'Kalutara', 'Kandy', 'Galle', 'Matara', 'Hambantota', 'Jaffna', 'Kilinochchi', 'Mannar', 'Mullaitivu', 'Vavuniya', 'Puttalam', 'Kurunegala', 'Anuradhapura', 'Polonnaruwa', 'Matale', 'Nuwara Eliya', 'Kegalle', 'Ratnapura', 'Trincomalee', 'Batticaloa', 'Ampara', 'Badulla', 'Monaragala'];

foreach ($districts as $district) {
    $has_area = in_array($district, $areas);
    if ($has_area) {
        echo "<p>✅ {$district}</p>";
    }
}

// Test with latest quote
echo "<h3>🧪 Test with Latest Quote:</h3>";
$stmt = $pdo->query("
    SELECT qr.*, sc.name_en as service_name
    FROM quote_requests qr
    LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
    ORDER BY qr.created_at DESC
    LIMIT 1
");
$latest_quote = $stmt->fetch();

if ($latest_quote) {
    echo "<p><strong>Latest Quote:</strong> {$latest_quote['title']}</p>";
    echo "<p><strong>Service:</strong> {$latest_quote['service_name']} (ID: {$latest_quote['service_category_id']})</p>";
    echo "<p><strong>District:</strong> {$latest_quote['district']}</p>";
    
    $has_service = in_array((int)$latest_quote['service_category_id'], $services) ||
                  in_array((string)$latest_quote['service_category_id'], $services);
    $has_area = in_array($latest_quote['district'], $areas);
    
    echo "<p><strong>Service Match:</strong> " . ($has_service ? '✅ YES' : '❌ NO') . "</p>";
    echo "<p><strong>Area Match:</strong> " . ($has_area ? '✅ YES' : '❌ NO') . "</p>";
    echo "<p><strong>Should See Quote:</strong> " . ($has_service && $has_area ? '✅ YES' : '❌ NO') . "</p>";
    
    if (!$has_service) {
        echo "<p>🔧 <strong>Service Issue:</strong> Contractor doesn't have service ID {$latest_quote['service_category_id']} in their profile.</p>";
    }
    
    if (!$has_area) {
        echo "<p>🗺️ <strong>Area Issue:</strong> Contractor doesn't serve {$latest_quote['district']} area.</p>";
    }
} else {
    echo "<p>❌ No quotes found</p>";
}

// Show what quotes this contractor should see
echo "<h3>📋 Quotes This Contractor Should See:</h3>";
$stmt = $pdo->prepare("
    SELECT qr.*, sc.name_en as service_name
    FROM quote_requests qr
    LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
    WHERE qr.status = 'open'
    AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
    ORDER BY qr.created_at DESC
");
$stmt->execute([$contractor['id']]);
$all_quotes = $stmt->fetchAll();

$matching_quotes = [];
foreach ($all_quotes as $quote) {
    if ($quote['specific_contractor_id'] == $contractor['id']) {
        $matching_quotes[] = $quote;
        echo "<p>✅ <strong>Direct Quote:</strong> {$quote['title']} - {$quote['service_name']} - {$quote['district']}</p>";
    } elseif ($quote['specific_contractor_id'] === null) {
        $has_service = in_array((int)$quote['service_category_id'], $services) ||
                      in_array((string)$quote['service_category_id'], $services);
        $has_area = in_array($quote['district'], $areas);
        
        if ($has_service && $has_area) {
            $matching_quotes[] = $quote;
            echo "<p>✅ <strong>General Quote:</strong> {$quote['title']} - {$quote['service_name']} - {$quote['district']}</p>";
        } else {
            echo "<p>❌ <strong>No Match:</strong> {$quote['title']} - {$quote['service_name']} - {$quote['district']} (Service: " . ($has_service ? 'YES' : 'NO') . ", Area: " . ($has_area ? 'YES' : 'NO') . ")</p>";
        }
    }
}

echo "<p><strong>Total matching quotes:</strong> " . count($matching_quotes) . "</p>";
?>
