-- Sample Contractor Data for BrickClick Platform
-- Run this script to add sample contractor data

-- Insert sample contractor user
INSERT INTO users (email, password, user_type, status, created_at, updated_at) VALUES 
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'contractor', 'approved', NOW(), NOW());

-- Get the user ID (assuming it's the last inserted ID)
SET @contractor_user_id = LAST_INSERT_ID();

-- Insert contractor profile
INSERT INTO contractor_profiles (
    user_id, business_name, contact_person, phone, business_address,
    service_areas, service_types, cida_registration, cida_grade,
    business_description, website, profile_image, average_rating,
    total_reviews, total_projects, language_preference, created_at, updated_at
) VALUES (
    @contractor_user_id,
    'Elite Construction Solutions',
    '<PERSON><PERSON>',
    '+94 77 123 4567',
    'No. 45, Galle Road, Colombo 03, Sri Lanka',
    '["Colombo", "Gampaha", "<PERSON>lut<PERSON>", "Kandy"]',
    '["House Construction", "Building Renovation", "Commercial Construction", "Interior Design & Finishing"]',
    'CIDA/REG/2023/001234',
    'C5',
    'Elite Construction Solutions is a leading construction company in Sri Lanka with over 15 years of experience in residential and commercial construction. We specialize in modern villa construction, office buildings, and luxury interior finishing. Our team of certified engineers and skilled craftsmen ensure quality workmanship and timely project completion. We have successfully completed over 200 projects across the Western and Central provinces.',
    'https://www.eliteconstructionlk.com',
    NULL,
    4.7,
    23,
    47,
    'en',
    NOW(),
    NOW()
);

-- Insert sample portfolio projects
INSERT INTO contractor_portfolios (
    contractor_id, project_name, project_description, project_location,
    completion_date, project_value, project_images, is_featured, created_at, updated_at
) VALUES 
(
    @contractor_user_id,
    'Modern Villa - Colombo 07',
    'A stunning 4-bedroom modern villa featuring contemporary architecture, smart home automation, and luxury finishes. The project included a swimming pool, landscaped garden, and a 3-car garage. Built with eco-friendly materials and energy-efficient systems.',
    'Colombo 07, Sri Lanka',
    '2023-08-15',
    25000000.00,
    '[]',
    1,
    NOW(),
    NOW()
),
(
    @contractor_user_id,
    'Commercial Office Complex',
    'A 5-story commercial office building with modern amenities including central air conditioning, high-speed elevators, and underground parking. The building features a glass facade with energy-efficient design and LEED certification.',
    'Gampaha, Sri Lanka',
    '2023-06-30',
    45000000.00,
    '[]',
    1,
    NOW(),
    NOW()
),
(
    @contractor_user_id,
    'Luxury Apartment Renovation',
    'Complete renovation of a 3-bedroom apartment including kitchen remodeling, bathroom upgrades, flooring replacement, and interior design. The project transformed an old apartment into a modern living space with premium finishes.',
    'Kandy, Sri Lanka',
    '2023-09-20',
    3500000.00,
    '[]',
    0,
    NOW(),
    NOW()
),
(
    @contractor_user_id,
    'Restaurant Interior Design',
    'Interior design and construction for a fine dining restaurant featuring custom woodwork, ambient lighting, and modern kitchen setup. The project included custom furniture, decorative elements, and complete electrical and plumbing work.',
    'Kalutara, Sri Lanka',
    '2023-07-10',
    2800000.00,
    '[]',
    0,
    NOW(),
    NOW()
);

-- Insert sample reviews
INSERT INTO reviews (
    customer_id, contractor_id, quote_request_id, rating, comment, is_approved, created_at, updated_at
) VALUES 
-- Note: These will need actual customer IDs when customers are created
-- For now, using placeholder values that should be updated when customer data exists
(
    1, -- This should be replaced with actual customer ID
    @contractor_user_id,
    NULL,
    5,
    'Excellent work! Elite Construction delivered exactly what they promised. The villa construction was completed on time and the quality is outstanding. Rajesh and his team were very professional throughout the project.',
    1,
    '2023-08-20 10:30:00',
    '2023-08-20 10:30:00'
),
(
    2, -- This should be replaced with actual customer ID
    @contractor_user_id,
    NULL,
    5,
    'Highly recommend Elite Construction! They renovated our apartment beautifully. The attention to detail and quality of work exceeded our expectations. Great communication and fair pricing.',
    1,
    '2023-09-25 14:15:00',
    '2023-09-25 14:15:00'
),
(
    3, -- This should be replaced with actual customer ID
    @contractor_user_id,
    NULL,
    4,
    'Very satisfied with the office building construction. Professional team, good quality work, and completed within the agreed timeline. Would definitely work with them again for future projects.',
    1,
    '2023-07-05 09:45:00',
    '2023-07-05 09:45:00'
);

-- Insert sample quote requests (these would normally come from customers)
INSERT INTO quote_requests (
    customer_id, contractor_id, service_category, description, estimated_budget,
    timeline, location, status, created_at, updated_at
) VALUES 
(
    1, -- This should be replaced with actual customer ID
    NULL, -- Open to all contractors
    'House Construction',
    'Looking to build a 3-bedroom house with modern design. The plot size is 15 perches. Need complete construction including electrical, plumbing, and basic interior work. Prefer eco-friendly materials and energy-efficient design.',
    8000000.00,
    '6-8 months',
    'Nugegoda, Colombo',
    'open',
    NOW(),
    NOW()
),
(
    2, -- This should be replaced with actual customer ID
    NULL,
    'Building Renovation',
    'Complete renovation of a 2-story house built in 1990. Need to update electrical wiring, plumbing, flooring, and interior design. Also need to add a modern kitchen and upgrade bathrooms.',
    4500000.00,
    '3-4 months',
    'Kandy, Central Province',
    'open',
    NOW(),
    NOW()
);

-- Insert sample quote responses from our contractor
INSERT INTO quote_responses (
    quote_request_id, contractor_id, quoted_amount, estimated_timeline,
    description, terms_conditions, status, created_at, updated_at
) VALUES 
(
    1, -- First quote request
    @contractor_user_id,
    7800000.00,
    '7 months',
    'We propose to construct a modern 3-bedroom house with high-quality materials and finishes. Our package includes architectural design, structural engineering, complete electrical and plumbing work, tiling, painting, and basic interior work. We will use eco-friendly materials and implement energy-efficient solutions including LED lighting and proper insulation.',
    'Payment terms: 20% advance, 30% at foundation completion, 30% at roof completion, 20% at final handover. All materials will be of premium quality. 1-year warranty on construction work. Project timeline: 7 months from commencement. Any additional work will be charged separately.',
    'pending',
    NOW(),
    NOW()
),
(
    2, -- Second quote request
    @contractor_user_id,
    4200000.00,
    '4 months',
    'Complete renovation package including electrical rewiring with modern safety standards, plumbing upgrades, premium flooring (tiles/hardwood options), modern kitchen with granite countertops, bathroom renovations with modern fixtures, interior painting, and basic interior design consultation.',
    'Payment terms: 25% advance, 25% after demolition and preparation, 25% at 50% completion, 25% at final completion. All old materials disposal included. 6-month warranty on renovation work. Timeline: 4 months. Customer to provide temporary accommodation during renovation.',
    'pending',
    NOW(),
    NOW()
);

-- Update contractor profile with correct statistics
UPDATE contractor_profiles 
SET total_reviews = (SELECT COUNT(*) FROM reviews WHERE contractor_id = @contractor_user_id AND is_approved = 1),
    average_rating = (SELECT AVG(rating) FROM reviews WHERE contractor_id = @contractor_user_id AND is_approved = 1),
    total_projects = (SELECT COUNT(*) FROM contractor_portfolios WHERE contractor_id = @contractor_user_id)
WHERE user_id = @contractor_user_id;

-- Display the login credentials
SELECT 
    'CONTRACTOR LOGIN CREDENTIALS' as info,
    'Email: <EMAIL>' as email,
    'Password: password' as password,
    'Status: Approved' as status,
    'Business: Elite Construction Solutions' as business;
