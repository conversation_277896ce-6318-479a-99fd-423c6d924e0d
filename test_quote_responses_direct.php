<?php
session_start();
require_once 'config/database.php';

// Simulate being logged in as a customer
$_SESSION['user_id'] = 1; // Use the customer ID from our test data
$_SESSION['user_type'] = 'customer';

$quote_id = (int)($_GET['id'] ?? 1);

echo "<h2>Testing Quote Responses for Quote ID: $quote_id</h2>";

if (!$quote_id) {
    echo "<p style='color: red;'>Invalid quote request.</p>";
    exit();
}

// Get quote request details
try {
    $stmt = $pdo->prepare("
        SELECT qr.*, sc.name_en as service_name
        FROM quote_requests qr 
        JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE qr.id = ? AND qr.customer_id = ?
    ");
    $stmt->execute([$quote_id, $_SESSION['user_id']]);
    $quote = $stmt->fetch();
    
    if (!$quote) {
        echo "<p style='color: red;'>Quote request not found.</p>";
        
        // Debug: Check if quote exists at all
        $stmt = $pdo->prepare("SELECT * FROM quote_requests WHERE id = ?");
        $stmt->execute([$quote_id]);
        $quote_any = $stmt->fetch();
        
        if ($quote_any) {
            echo "<p>Quote exists but customer_id mismatch. Quote customer_id: " . $quote_any['customer_id'] . ", Session user_id: " . $_SESSION['user_id'] . "</p>";
            
            // Update session to match the quote's customer
            $_SESSION['user_id'] = $quote_any['customer_id'];
            echo "<p>Updated session user_id to: " . $_SESSION['user_id'] . "</p>";
            
            // Try again
            $stmt = $pdo->prepare("
                SELECT qr.*, sc.name_en as service_name
                FROM quote_requests qr 
                JOIN service_categories sc ON qr.service_category_id = sc.id
                WHERE qr.id = ? AND qr.customer_id = ?
            ");
            $stmt->execute([$quote_id, $_SESSION['user_id']]);
            $quote = $stmt->fetch();
        } else {
            echo "<p>Quote does not exist at all.</p>";
        }
    }
    
    if ($quote) {
        echo "<h3>Quote Details:</h3>";
        echo "<p><strong>Title:</strong> " . htmlspecialchars($quote['title']) . "</p>";
        echo "<p><strong>Service:</strong> " . htmlspecialchars($quote['service_name']) . "</p>";
        echo "<p><strong>Location:</strong> " . htmlspecialchars($quote['location'] . ', ' . $quote['district']) . "</p>";
        echo "<p><strong>Budget:</strong> Rs. " . number_format($quote['estimated_budget'], 2) . "</p>";
        echo "<p><strong>Status:</strong> " . htmlspecialchars($quote['status']) . "</p>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error in quote fetch: " . $e->getMessage() . "</p>";
    exit();
}

// Get quote responses
try {
    // First, check if there are any responses at all
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM quote_responses WHERE quote_request_id = ?");
    $stmt->execute([$quote_id]);
    $response_count = $stmt->fetch()['count'];
    
    echo "<h3>Quote Responses:</h3>";
    echo "<p>Total responses in database: $response_count</p>";

    $stmt = $pdo->prepare("
        SELECT qres.*, cp.business_name, cp.contact_person, cp.phone, cp.profile_image,
               cp.average_rating, cp.total_reviews, cp.cida_grade,
               pp.payment_status, pp.amount as paid_amount, pp.payment_date, pp.id as payment_id,
               r.id as review_id, r.rating as review_rating, r.review_text
        FROM quote_responses qres
        JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
        LEFT JOIN project_payments pp ON qres.id = pp.quote_response_id AND pp.payment_type = 'down_payment'
        LEFT JOIN reviews r ON pp.id = r.payment_id AND r.customer_id = ?
        WHERE qres.quote_request_id = ?
        ORDER BY qres.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id'], $quote_id]);
    $responses = $stmt->fetchAll();
    
    echo "<p>Responses fetched: " . count($responses) . "</p>";
    
    if (empty($responses)) {
        echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; text-align: center;'>";
        echo "<h5>No responses yet</h5>";
        echo "<p>Contractors will respond to your quote request soon. You'll be notified when responses are received.</p>";
        echo "</div>";
    } else {
        foreach ($responses as $response) {
            echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h5>" . htmlspecialchars($response['business_name']) . "</h5>";
            echo "<p><strong>Contact:</strong> " . htmlspecialchars($response['contact_person']) . "</p>";
            echo "<p><strong>Phone:</strong> " . htmlspecialchars($response['phone']) . "</p>";
            echo "<p><strong>Quoted Amount:</strong> Rs. " . number_format($response['quoted_amount'], 2) . "</p>";
            echo "<p><strong>Timeline:</strong> " . htmlspecialchars($response['estimated_timeline']) . "</p>";
            echo "<p><strong>Description:</strong> " . nl2br(htmlspecialchars($response['description'])) . "</p>";
            echo "<p><strong>Status:</strong> " . htmlspecialchars($response['status']) . "</p>";
            echo "<p><strong>CIDA Grade:</strong> " . htmlspecialchars($response['cida_grade']) . "</p>";
            
            if ($response['status'] === 'pending') {
                echo "<div style='margin-top: 10px;'>";
                echo "<button style='background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin-right: 10px;'>Accept Quote</button>";
                echo "<button style='background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px;'>Reject Quote</button>";
                echo "</div>";
            }
            echo "</div>";
        }
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error in responses fetch: " . $e->getMessage() . "</p>";
    
    // Debug: Check if contractor_profiles table has data
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM contractor_profiles");
        $cp_count = $stmt->fetchColumn();
        echo "<p>Contractor profiles count: $cp_count</p>";
        
        if ($cp_count > 0) {
            $stmt = $pdo->query("SELECT * FROM contractor_profiles LIMIT 1");
            $sample_cp = $stmt->fetch();
            echo "<p>Sample contractor profile user_id: " . $sample_cp['user_id'] . "</p>";
        }
        
        // Check quote_responses without JOIN
        $stmt = $pdo->prepare("SELECT * FROM quote_responses WHERE quote_request_id = ?");
        $stmt->execute([$quote_id]);
        $simple_responses = $stmt->fetchAll();
        echo "<p>Simple responses (no JOIN): " . count($simple_responses) . "</p>";
        
        if (!empty($simple_responses)) {
            foreach ($simple_responses as $sr) {
                echo "<p>Response contractor_id: " . $sr['contractor_id'] . ", amount: " . $sr['quoted_amount'] . "</p>";
            }
        }
    } catch (PDOException $e2) {
        echo "<p style='color: red;'>Debug query error: " . $e2->getMessage() . "</p>";
    }
}
?>
