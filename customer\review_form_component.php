<?php
// Review Form Component
// Usage: include this file and call renderReviewForm($contractor_data, $payment_data, $modal_id)

function renderReviewForm($contractor_data, $payment_data, $modal_id = 'reviewModal') {
    $contractor_id = $contractor_data['contractor_id'];
    $business_name = $contractor_data['business_name'];
    $payment_id = $payment_data['payment_id'];
    $quote_response_id = $payment_data['quote_response_id'];
?>

<!-- Review Modal -->
<div class="modal fade" id="<?php echo $modal_id; ?>" tabindex="-1" aria-labelledby="<?php echo $modal_id; ?>Label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content review-modal-content">
            <div class="modal-header review-modal-header">
                <div class="review-header-content">
                    <div class="review-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div>
                        <h5 class="modal-title" id="<?php echo $modal_id; ?>Label">
                            How was your experience with
                        </h5>
                        <h4 class="contractor-name"><?php echo htmlspecialchars($business_name); ?>?</h4>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            
            <form action="submit_review.php" method="POST" id="reviewForm<?php echo $modal_id; ?>" class="review-form">
                <input type="hidden" name="contractor_id" value="<?php echo $contractor_id; ?>">
                <input type="hidden" name="payment_id" value="<?php echo $payment_id; ?>">
                <input type="hidden" name="quote_response_id" value="<?php echo $quote_response_id; ?>">

                <div class="modal-body review-modal-body">
                    <!-- Overall Rating -->
                    <div class="rating-section">
                        <label class="rating-label">Overall Rating *</label>
                        <div class="star-rating-container">
                            <div class="star-rating" data-rating="0" data-target="rating<?php echo $modal_id; ?>">
                                <i class="fas fa-star" data-value="1"></i>
                                <i class="fas fa-star" data-value="2"></i>
                                <i class="fas fa-star" data-value="3"></i>
                                <i class="fas fa-star" data-value="4"></i>
                                <i class="fas fa-star" data-value="5"></i>
                            </div>
                            <span class="rating-text">Click to rate</span>
                        </div>
                        <input type="hidden" name="rating" id="rating<?php echo $modal_id; ?>">
                    </div>

                    <!-- Detailed Ratings -->
                    <div class="detailed-ratings">
                        <div class="rating-row">
                            <div class="rating-item">
                                <label class="rating-label">Quality of Work</label>
                                <div class="star-rating" data-rating="0" data-target="quality_rating<?php echo $modal_id; ?>">
                                    <i class="fas fa-star" data-value="1"></i>
                                    <i class="fas fa-star" data-value="2"></i>
                                    <i class="fas fa-star" data-value="3"></i>
                                    <i class="fas fa-star" data-value="4"></i>
                                    <i class="fas fa-star" data-value="5"></i>
                                </div>
                                <input type="hidden" name="quality_rating" id="quality_rating<?php echo $modal_id; ?>">
                            </div>
                            
                            <div class="rating-item">
                                <label class="rating-label">Communication</label>
                                <div class="star-rating" data-rating="0" data-target="communication_rating<?php echo $modal_id; ?>">
                                    <i class="fas fa-star" data-value="1"></i>
                                    <i class="fas fa-star" data-value="2"></i>
                                    <i class="fas fa-star" data-value="3"></i>
                                    <i class="fas fa-star" data-value="4"></i>
                                    <i class="fas fa-star" data-value="5"></i>
                                </div>
                                <input type="hidden" name="communication_rating" id="communication_rating<?php echo $modal_id; ?>">
                            </div>
                        </div>
                        
                        <div class="rating-row">
                            <div class="rating-item">
                                <label class="rating-label">Timeliness</label>
                                <div class="star-rating" data-rating="0" data-target="timeliness_rating<?php echo $modal_id; ?>">
                                    <i class="fas fa-star" data-value="1"></i>
                                    <i class="fas fa-star" data-value="2"></i>
                                    <i class="fas fa-star" data-value="3"></i>
                                    <i class="fas fa-star" data-value="4"></i>
                                    <i class="fas fa-star" data-value="5"></i>
                                </div>
                                <input type="hidden" name="timeliness_rating" id="timeliness_rating<?php echo $modal_id; ?>">
                            </div>
                            
                            <div class="rating-item">
                                <label class="rating-label">Value for Money</label>
                                <div class="star-rating" data-rating="0" data-target="value_rating<?php echo $modal_id; ?>">
                                    <i class="fas fa-star" data-value="1"></i>
                                    <i class="fas fa-star" data-value="2"></i>
                                    <i class="fas fa-star" data-value="3"></i>
                                    <i class="fas fa-star" data-value="4"></i>
                                    <i class="fas fa-star" data-value="5"></i>
                                </div>
                                <input type="hidden" name="value_rating" id="value_rating<?php echo $modal_id; ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Review Text -->
                    <div class="review-text-section">
                        <label for="review_text<?php echo $modal_id; ?>" class="rating-label">Your Review</label>
                        <textarea class="form-control review-textarea" id="review_text<?php echo $modal_id; ?>" name="review_text" rows="4"
                                  placeholder="Share your experience with this contractor..."></textarea>
                    </div>

                    <!-- Recommendation -->
                    <div class="recommendation-section">
                        <label class="rating-label">Would you recommend this contractor?</label>
                        <div class="recommendation-options">
                            <label class="recommendation-option">
                                <input type="radio" name="recommend" value="1">
                                <span class="checkmark recommend-yes">
                                    <i class="fas fa-thumbs-up"></i>
                                    Yes, I would recommend
                                </span>
                            </label>
                            <label class="recommendation-option">
                                <input type="radio" name="recommend" value="0">
                                <span class="checkmark recommend-no">
                                    <i class="fas fa-thumbs-down"></i>
                                    No, I would not recommend
                                </span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="modal-footer review-modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary submit-review-btn">
                        <i class="fas fa-paper-plane me-2"></i>Submit Review
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.review-modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.review-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px 20px 0 0;
    padding: 25px 30px;
    border: none;
}

.review-header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.review-icon {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.contractor-name {
    color: #ffd700;
    margin: 5px 0 0 0;
    font-weight: 600;
}

.review-modal-body {
    padding: 30px;
}

.rating-section {
    text-align: center;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
}

.rating-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    font-size: 16px;
}

.star-rating-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.star-rating {
    display: flex;
    gap: 8px;
    font-size: 32px;
    cursor: pointer;
}

.star-rating .fa-star {
    color: #e0e0e0;
    transition: all 0.3s ease;
}

.star-rating .fa-star:hover,
.star-rating .fa-star.active {
    color: #ffd700;
    transform: scale(1.1);
}

.rating-text {
    color: #666;
    font-size: 14px;
    font-style: italic;
}

.detailed-ratings {
    margin-bottom: 25px;
}

.rating-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 20px;
}

.rating-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.rating-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.rating-item .star-rating {
    font-size: 20px;
    justify-content: center;
    margin-top: 10px;
}

.review-text-section {
    margin-bottom: 25px;
}

.review-textarea {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    resize: vertical;
    min-height: 120px;
}

.review-textarea:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.recommendation-section {
    margin-bottom: 20px;
}

.recommendation-options {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 15px;
}

.recommendation-option {
    cursor: pointer;
    margin: 0;
}

.recommendation-option input[type="radio"] {
    display: none;
}

.checkmark {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    background: white;
    transition: all 0.3s ease;
    font-weight: 500;
}

.recommend-yes {
    color: #28a745;
}

.recommend-no {
    color: #dc3545;
}

.recommendation-option input[type="radio"]:checked + .checkmark {
    border-color: currentColor;
    background: currentColor;
    color: white;
}

.review-modal-footer {
    padding: 20px 30px;
    border: none;
    background: #f8f9fa;
    border-radius: 0 0 20px 20px;
}

.submit-review-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.submit-review-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

@media (max-width: 768px) {
    .rating-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .recommendation-options {
        flex-direction: column;
        align-items: center;
    }
    
    .review-modal-body {
        padding: 20px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Star rating functionality
    document.querySelectorAll('.star-rating').forEach(function(rating) {
        const stars = rating.querySelectorAll('.fa-star');
        const targetId = rating.getAttribute('data-target');
        const targetInput = document.getElementById(targetId);
        
        stars.forEach(function(star, index) {
            star.addEventListener('click', function() {
                const value = parseInt(star.getAttribute('data-value'));
                rating.setAttribute('data-rating', value);
                targetInput.value = value;
                
                // Update visual state
                stars.forEach(function(s, i) {
                    if (i < value) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
                
                // Update rating text for overall rating
                if (targetId.includes('rating<?php echo $modal_id; ?>')) {
                    const ratingText = rating.parentElement.querySelector('.rating-text');
                    const texts = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
                    ratingText.textContent = texts[value] || 'Click to rate';
                }
            });
            
            star.addEventListener('mouseenter', function() {
                const value = parseInt(star.getAttribute('data-value'));
                stars.forEach(function(s, i) {
                    if (i < value) {
                        s.style.color = '#ffd700';
                    } else {
                        s.style.color = '#e0e0e0';
                    }
                });
            });
        });
        
        rating.addEventListener('mouseleave', function() {
            const currentRating = parseInt(rating.getAttribute('data-rating'));
            stars.forEach(function(s, i) {
                if (i < currentRating) {
                    s.style.color = '#ffd700';
                } else {
                    s.style.color = '#e0e0e0';
                }
            });
        });
    });
    
    // Form validation
    document.getElementById('reviewForm<?php echo $modal_id; ?>').addEventListener('submit', function(e) {
        const rating = document.getElementById('rating<?php echo $modal_id; ?>').value;
        
        if (!rating || rating < 1) {
            e.preventDefault();
            alert('Please provide an overall rating before submitting your review.');
            return false;
        }
    });
});
</script>

<?php
}
?>
