<?php
session_start();
require_once 'config/database.php';

echo "<h2>🌟 Review System Test</h2>";

try {
    // First, check users table structure
    echo "<h3>Users Table Structure</h3>";
    $stmt = $pdo->query("DESCRIBE users");
    $user_columns = $stmt->fetchAll();

    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Column</th><th style='padding: 8px;'>Type</th></tr>";
    foreach ($user_columns as $column) {
        echo "<tr><td style='padding: 8px;'>" . $column['Field'] . "</td><td style='padding: 8px;'>" . $column['Type'] . "</td></tr>";
    }
    echo "</table>";

    // Get test data - find a completed payment
    $stmt = $pdo->query("
        SELECT pp.*, qr.title, cp.business_name, u.email as customer_name
        FROM project_payments pp
        JOIN quote_responses qres ON pp.quote_response_id = qres.id
        JOIN quote_requests qr ON qres.quote_request_id = qr.id
        JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
        JOIN users u ON pp.customer_id = u.id
        WHERE pp.payment_status = 'completed'
        ORDER BY pp.created_at DESC
        LIMIT 1
    ");
    $payment = $stmt->fetch();
    
    if (!$payment) {
        echo "<p style='color: red;'>No completed payments found. Please complete a payment first.</p>";
        echo "<p><a href='test_payment_flow.php' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Complete a Payment</a></p>";
        exit;
    }
    
    echo "<h3>1. Test Payment Found</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<p><strong>Payment ID:</strong> " . $payment['id'] . "</p>";
    echo "<p><strong>Customer:</strong> " . htmlspecialchars($payment['customer_name']) . "</p>";
    echo "<p><strong>Contractor:</strong> " . htmlspecialchars($payment['business_name']) . "</p>";
    echo "<p><strong>Project:</strong> " . htmlspecialchars($payment['title']) . "</p>";
    echo "<p><strong>Amount:</strong> Rs. " . number_format($payment['amount'], 2) . "</p>";
    echo "<p><strong>Status:</strong> " . $payment['payment_status'] . "</p>";
    echo "</div>";
    
    // Check if review already exists
    $stmt = $pdo->prepare("
        SELECT * FROM reviews 
        WHERE customer_id = ? AND contractor_id = ? AND payment_id = ?
    ");
    $stmt->execute([$payment['customer_id'], $payment['contractor_id'], $payment['id']]);
    $existing_review = $stmt->fetch();
    
    echo "<h3>2. Review Status</h3>";
    if ($existing_review) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<p style='color: green;'><strong>✓ Review Already Exists</strong></p>";
        echo "<p><strong>Rating:</strong> " . $existing_review['rating'] . "/5 stars</p>";
        echo "<p><strong>Review Text:</strong> " . htmlspecialchars($existing_review['review_text'] ?: 'No review text') . "</p>";
        echo "<p><strong>Quality:</strong> " . ($existing_review['quality_rating'] ?: 'Not rated') . "</p>";
        echo "<p><strong>Communication:</strong> " . ($existing_review['communication_rating'] ?: 'Not rated') . "</p>";
        echo "<p><strong>Timeliness:</strong> " . ($existing_review['timeliness_rating'] ?: 'Not rated') . "</p>";
        echo "<p><strong>Value:</strong> " . ($existing_review['value_rating'] ?: 'Not rated') . "</p>";
        echo "<p><strong>Recommend:</strong> " . ($existing_review['recommend'] ? 'Yes' : 'No') . "</p>";
        echo "<p><strong>Created:</strong> " . date('M j, Y H:i', strtotime($existing_review['created_at'])) . "</p>";
        echo "</div>";
        
        echo "<h4>Delete Existing Review (for testing)</h4>";
        echo "<form method='POST' style='margin: 10px 0;'>";
        echo "<input type='hidden' name='delete_review' value='" . $existing_review['id'] . "'>";
        echo "<button type='submit' style='background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;'>🗑️ Delete Review</button>";
        echo "</form>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<p style='color: #856404;'><strong>⚠ No Review Found</strong></p>";
        echo "<p>This payment is eligible for review submission.</p>";
        echo "</div>";
    }
    
    // Handle review deletion
    if (isset($_POST['delete_review'])) {
        $review_id = (int)$_POST['delete_review'];
        $stmt = $pdo->prepare("DELETE FROM reviews WHERE id = ?");
        $stmt->execute([$review_id]);
        echo "<p style='color: green;'>✓ Review deleted successfully!</p>";
        echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
    }
    
    // Set up session for testing
    $_SESSION['user_id'] = $payment['customer_id'];
    $_SESSION['user_type'] = 'customer';
    
    echo "<h3>3. Test Review Form</h3>";
    echo "<p>Session configured for customer ID: " . $payment['customer_id'] . "</p>";
    
    // Include the review form component
    require_once 'customer/review_form_component.php';
    
    // Prepare data for the review form
    $contractor_data = [
        'contractor_id' => $payment['contractor_id'],
        'business_name' => $payment['business_name']
    ];
    
    $payment_data = [
        'payment_id' => $payment['id'],
        'quote_response_id' => $payment['quote_response_id']
    ];
    
    echo "<div style='margin: 20px 0;'>";
    echo "<button type='button' class='btn btn-primary' data-bs-toggle='modal' data-bs-target='#testReviewModal' style='background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>";
    echo "🌟 Test Review Form";
    echo "</button>";
    echo "</div>";
    
    // Render the review form
    renderReviewForm($contractor_data, $payment_data, 'testReviewModal');
    
    echo "<h3>4. Direct Form Test</h3>";
    echo "<form method='POST' action='customer/submit_review.php' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>Quick Review Submission</h4>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label><strong>Rating (1-5):</strong></label><br>";
    echo "<select name='rating' required style='padding: 8px; margin: 5px 0;'>";
    echo "<option value=''>Select rating</option>";
    echo "<option value='5'>5 - Excellent</option>";
    echo "<option value='4'>4 - Very Good</option>";
    echo "<option value='3'>3 - Good</option>";
    echo "<option value='2'>2 - Fair</option>";
    echo "<option value='1'>1 - Poor</option>";
    echo "</select>";
    echo "</div>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label><strong>Review Text:</strong></label><br>";
    echo "<textarea name='review_text' rows='3' style='width: 100%; padding: 8px; margin: 5px 0;' placeholder='Great work! Professional and on time.'>Great work! Professional and on time.</textarea>";
    echo "</div>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label><strong>Quality Rating:</strong></label><br>";
    echo "<select name='quality_rating' style='padding: 8px; margin: 5px 0;'>";
    echo "<option value=''>Optional</option>";
    echo "<option value='5' selected>5 - Excellent</option>";
    echo "<option value='4'>4 - Very Good</option>";
    echo "<option value='3'>3 - Good</option>";
    echo "<option value='2'>2 - Fair</option>";
    echo "<option value='1'>1 - Poor</option>";
    echo "</select>";
    echo "</div>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label><strong>Recommend:</strong></label><br>";
    echo "<label><input type='radio' name='recommend' value='1' checked> Yes, I would recommend</label><br>";
    echo "<label><input type='radio' name='recommend' value='0'> No, I would not recommend</label>";
    echo "</div>";
    
    echo "<input type='hidden' name='contractor_id' value='" . $payment['contractor_id'] . "'>";
    echo "<input type='hidden' name='payment_id' value='" . $payment['id'] . "'>";
    echo "<input type='hidden' name='quote_response_id' value='" . $payment['quote_response_id'] . "'>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<button type='submit' style='background: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>📝 Submit Test Review</button>";
    echo "</div>";
    echo "</form>";
    
    echo "<h3>5. Check Reviews Table Structure</h3>";
    $stmt = $pdo->query("DESCRIBE reviews");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Column</th><th style='padding: 8px;'>Type</th><th style='padding: 8px;'>Null</th><th style='padding: 8px;'>Key</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . $column['Field'] . "</td>";
        echo "<td style='padding: 8px;'>" . $column['Type'] . "</td>";
        echo "<td style='padding: 8px;'>" . $column['Null'] . "</td>";
        echo "<td style='padding: 8px;'>" . $column['Key'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>6. Quick Links</h3>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='customer/payment_success.php?payment_id=" . $payment['id'] . "' target='_blank' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>💳 Payment Success Page</a>";
    echo "<a href='customer/quote_responses.php?id=" . $payment['quote_response_id'] . "' target='_blank' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>📋 Quote Responses</a>";
    echo "<a href='contractor/reviews.php' target='_blank' style='background: #6f42c1; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>⭐ Contractor Reviews</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>

<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}

h2, h3, h4 {
    color: #333;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
</style>
