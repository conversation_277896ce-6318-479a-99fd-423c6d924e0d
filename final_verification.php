<?php
require_once 'config/database.php';

echo "<h1>🔧 Final Database Fix Verification</h1>";

$all_good = true;
$issues = [];

try {
    echo "<h2>1. Database Schema Verification</h2>";
    
    // Check quote_requests table for specific_contractor_id
    echo "<h3>Quote Requests Table</h3>";
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $has_specific_contractor = $stmt->fetch();
    
    if ($has_specific_contractor) {
        echo "<p style='color: green;'>✓ quote_requests.specific_contractor_id column exists</p>";
    } else {
        echo "<p style='color: red;'>✗ quote_requests.specific_contractor_id column missing</p>";
        $issues[] = "Missing specific_contractor_id column in quote_requests table";
        $all_good = false;
    }
    
    // Check reviews table structure
    echo "<h3>Reviews Table</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'reviews'");
    $reviews_exists = $stmt->fetch();
    
    if ($reviews_exists) {
        $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'payment_id'");
        $has_payment_id = $stmt->fetch();
        
        if ($has_payment_id) {
            echo "<p style='color: green;'>✓ reviews.payment_id column exists</p>";
        } else {
            echo "<p style='color: orange;'>⚠ reviews.payment_id column missing (using fallback)</p>";
        }
        
        $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'quote_request_id'");
        $has_quote_request_id = $stmt->fetch();
        
        if ($has_quote_request_id) {
            echo "<p style='color: green;'>✓ reviews.quote_request_id column exists</p>";
        } else {
            echo "<p style='color: orange;'>⚠ reviews.quote_request_id column missing</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ reviews table does not exist</p>";
        $issues[] = "Reviews table missing";
        $all_good = false;
    }
    
    echo "<h2>2. Test Data Verification</h2>";
    
    // Check for test users
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'customer' AND status = 'approved'");
    $customer_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'contractor' AND status = 'approved'");
    $contractor_count = $stmt->fetchColumn();
    
    echo "<p>Test customers: $customer_count</p>";
    echo "<p>Test contractors: $contractor_count</p>";
    
    if ($customer_count == 0) {
        $issues[] = "No test customers available";
        $all_good = false;
    }
    
    if ($contractor_count == 0) {
        $issues[] = "No test contractors available";
        $all_good = false;
    }
    
    // Check for quote requests and responses
    $stmt = $pdo->query("SELECT COUNT(*) FROM quote_requests");
    $quote_count = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM quote_responses");
    $response_count = $stmt->fetchColumn();
    
    echo "<p>Quote requests: $quote_count</p>";
    echo "<p>Quote responses: $response_count</p>";
    
    if ($quote_count == 0) {
        $issues[] = "No test quote requests available";
        $all_good = false;
    }
    
    if ($response_count == 0) {
        $issues[] = "No test quote responses available";
        $all_good = false;
    }
    
    echo "<h2>3. Quote Responses Query Test</h2>";
    
    if ($customer_count > 0 && $quote_count > 0) {
        // Get test data
        $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' AND status = 'approved' LIMIT 1");
        $test_customer_id = $stmt->fetchColumn();
        
        $stmt = $pdo->prepare("SELECT id FROM quote_requests WHERE customer_id = ? LIMIT 1");
        $stmt->execute([$test_customer_id]);
        $test_quote_id = $stmt->fetchColumn();
        
        if ($test_quote_id) {
            // Test the query that was failing
            try {
                $stmt = $pdo->prepare("
                    SELECT qres.*, 
                           COALESCE(cp.business_name, 'Unknown Contractor') as business_name, 
                           COALESCE(cp.contact_person, 'N/A') as contact_person, 
                           COALESCE(cp.phone, 'N/A') as phone, 
                           cp.profile_image,
                           COALESCE(cp.average_rating, 0) as average_rating, 
                           COALESCE(cp.total_reviews, 0) as total_reviews, 
                           COALESCE(cp.cida_grade, 'N/A') as cida_grade,
                           pp.payment_status, pp.amount as paid_amount, pp.payment_date, pp.id as payment_id,
                           NULL as review_id, NULL as review_rating, NULL as review_text
                    FROM quote_responses qres
                    LEFT JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
                    LEFT JOIN project_payments pp ON qres.id = pp.quote_response_id AND pp.payment_type = 'down_payment'
                    WHERE qres.quote_request_id = ?
                    ORDER BY qres.created_at DESC
                ");
                $stmt->execute([$test_quote_id]);
                $test_responses = $stmt->fetchAll();
                
                echo "<p style='color: green;'>✓ Quote responses query executed successfully</p>";
                echo "<p>Found " . count($test_responses) . " responses for quote ID $test_quote_id</p>";
                
                if (count($test_responses) > 0) {
                    echo "<p style='color: green;'>✓ Quote responses data retrieved successfully</p>";
                } else {
                    echo "<p style='color: orange;'>⚠ No responses found (this may be normal if no contractors have responded)</p>";
                }
                
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ Quote responses query failed: " . $e->getMessage() . "</p>";
                $issues[] = "Quote responses query still failing: " . $e->getMessage();
                $all_good = false;
            }
        } else {
            echo "<p style='color: orange;'>⚠ No quote requests found for test customer</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ Cannot test query - missing test data</p>";
    }
    
    echo "<h2>4. Final Status</h2>";
    
    if ($all_good && empty($issues)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3 style='margin-top: 0;'>🎉 All Systems Operational!</h3>";
        echo "<p>The database errors have been successfully fixed. Customers should now be able to:</p>";
        echo "<ul>";
        echo "<li>View contractor responses to their quote requests</li>";
        echo "<li>Accept/reject quotes without database errors</li>";
        echo "<li>Proceed with downpayments when accepting quotes</li>";
        echo "</ul>";
        echo "<p><strong>Test the system:</strong></p>";
        echo "<p><a href='test_login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Login & Test</a>";
        echo "<a href='verify_quote_system.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>System Status</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3 style='margin-top: 0;'>⚠ Issues Found</h3>";
        echo "<p>The following issues need to be addressed:</p>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        echo "<p><a href='fix_database_issues.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Run Database Fix</a></p>";
        echo "</div>";
    }
    
    echo "<h2>5. Quick Links</h2>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='fix_database_issues.php' style='background: #ffc107; color: #212529; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Database Setup</a>";
    echo "<a href='test_login.php' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Test Login</a>";
    echo "<a href='verify_quote_system.php' style='background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>System Check</a>";
    echo "<a href='check_reviews_structure.php' style='background: #6f42c1; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Reviews Check</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 10px;'>";
    echo "<h3>Database Connection Error</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and ensure the server is running.</p>";
    echo "</div>";
}
?>
