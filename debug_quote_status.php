<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔍 Debug Quote Status</h2>";

// Check if quote_requests table exists and has data
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM quote_requests");
    $total_quotes = $stmt->fetchColumn();
    echo "<p><strong>Total quote requests in database:</strong> {$total_quotes}</p>";
    
    if ($total_quotes == 0) {
        echo "<p>❌ <strong>PROBLEM:</strong> No quote requests found in database!</p>";
        echo "<p>This means you need to create a quote request first.</p>";
        echo "<p><a href='customer/dashboard.php' target='_blank'>Go to Customer Dashboard to Create Quote</a></p>";
        exit;
    }
    
    // Get recent quotes
    $stmt = $pdo->query("
        SELECT qr.*, sc.name_en as service_name, cp.first_name, cp.last_name
        FROM quote_requests qr
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        LEFT JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        ORDER BY qr.created_at DESC
        LIMIT 5
    ");
    $quotes = $stmt->fetchAll();
    
    echo "<h3>📋 Recent Quote Requests:</h3>";
    foreach ($quotes as $quote) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
        echo "<h4>Quote ID: {$quote['id']}</h4>";
        echo "<p><strong>Title:</strong> " . htmlspecialchars($quote['title']) . "</p>";
        echo "<p><strong>Customer:</strong> {$quote['first_name']} {$quote['last_name']}</p>";
        echo "<p><strong>Service:</strong> {$quote['service_name']} (ID: {$quote['service_category_id']})</p>";
        echo "<p><strong>District:</strong> {$quote['district']}</p>";
        echo "<p><strong>Status:</strong> <span style='color: " . ($quote['status'] === 'open' ? 'green' : 'red') . ";'>{$quote['status']}</span></p>";
        echo "<p><strong>Specific Contractor:</strong> " . ($quote['specific_contractor_id'] ?: 'NULL (General Quote)') . "</p>";
        echo "<p><strong>Created:</strong> {$quote['created_at']}</p>";
        echo "</div>";
    }
    
    // Check open quotes specifically
    $stmt = $pdo->query("SELECT COUNT(*) as open_count FROM quote_requests WHERE status = 'open'");
    $open_quotes = $stmt->fetchColumn();
    echo "<p><strong>Open quote requests:</strong> {$open_quotes}</p>";
    
    if ($open_quotes == 0) {
        echo "<p>❌ <strong>PROBLEM:</strong> No open quote requests!</p>";
        echo "<p>All quotes might be closed, completed, or cancelled.</p>";
        
        // Show status breakdown
        $stmt = $pdo->query("SELECT status, COUNT(*) as count FROM quote_requests GROUP BY status");
        $status_breakdown = $stmt->fetchAll();
        echo "<h4>Quote Status Breakdown:</h4>";
        foreach ($status_breakdown as $status) {
            echo "<p>- {$status['status']}: {$status['count']}</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p>❌ <strong>Database Error:</strong> " . $e->getMessage() . "</p>";
    exit;
}

// Check the specific contractor
echo "<h3>👷 Contractor Check:</h3>";
try {
    $stmt = $pdo->prepare("
        SELECT u.id, u.email, u.status, u.user_type, cp.business_name, cp.service_types, cp.service_areas
        FROM users u
        LEFT JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.email = ?
    ");
    $stmt->execute(['<EMAIL>']);
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p>❌ Contractor <EMAIL> not found!</p>";
        
        // Check what contractors exist
        $stmt = $pdo->query("SELECT email FROM users WHERE user_type = 'contractor' LIMIT 5");
        $contractors = $stmt->fetchAll();
        echo "<p>Available contractors:</p>";
        foreach ($contractors as $c) {
            echo "<p>- {$c['email']}</p>";
        }
        exit;
    }
    
    echo "<div style='background: #f0f0f0; padding: 15px;'>";
    echo "<p><strong>Contractor ID:</strong> {$contractor['id']}</p>";
    echo "<p><strong>Email:</strong> {$contractor['email']}</p>";
    echo "<p><strong>User Type:</strong> {$contractor['user_type']}</p>";
    echo "<p><strong>Status:</strong> <span style='color: " . ($contractor['status'] === 'approved' ? 'green' : 'red') . ";'>{$contractor['status']}</span></p>";
    echo "<p><strong>Business Name:</strong> " . ($contractor['business_name'] ?: 'NULL') . "</p>";
    echo "<p><strong>Service Types:</strong> " . ($contractor['service_types'] ?: 'NULL') . "</p>";
    echo "<p><strong>Service Areas:</strong> " . ($contractor['service_areas'] ?: 'NULL') . "</p>";
    echo "</div>";
    
    if ($contractor['status'] !== 'approved') {
        echo "<p>❌ <strong>PROBLEM:</strong> Contractor is not approved!</p>";
        echo "<p>Fixing contractor status...</p>";
        $stmt = $pdo->prepare("UPDATE users SET status = 'approved' WHERE id = ?");
        $stmt->execute([$contractor['id']]);
        echo "<p>✅ Contractor status updated to approved</p>";
    }
    
    if (!$contractor['service_types'] || !$contractor['service_areas']) {
        echo "<p>❌ <strong>PROBLEM:</strong> Contractor profile incomplete!</p>";
        echo "<p>The contractor needs service types and service areas to receive quotes.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>❌ <strong>Error checking contractor:</strong> " . $e->getMessage() . "</p>";
}

// Test if contractor should see any quotes
if ($open_quotes > 0 && $contractor && $contractor['service_types'] && $contractor['service_areas']) {
    echo "<h3>🧪 Quote Matching Test:</h3>";
    
    $contractor_services = json_decode($contractor['service_types'], true) ?: [];
    $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];
    
    echo "<p><strong>Contractor Services:</strong> [" . implode(', ', $contractor_services) . "]</p>";
    echo "<p><strong>Contractor Areas:</strong> [" . implode(', ', $contractor_areas) . "]</p>";
    
    $stmt = $pdo->query("
        SELECT qr.*, sc.name_en as service_name
        FROM quote_requests qr
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE qr.status = 'open'
        ORDER BY qr.created_at DESC
    ");
    $open_quotes_list = $stmt->fetchAll();
    
    $matching_quotes = 0;
    foreach ($open_quotes_list as $quote) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0;'>";
        echo "<h5>Quote {$quote['id']}: {$quote['title']}</h5>";
        
        if ($quote['specific_contractor_id'] == $contractor['id']) {
            $matching_quotes++;
            echo "<p>✅ <strong>MATCH:</strong> Direct quote for this contractor</p>";
        } elseif ($quote['specific_contractor_id'] === null) {
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            echo "<p>Service: {$quote['service_name']} (ID: {$quote['service_category_id']}) - " . ($has_service ? '✅' : '❌') . "</p>";
            echo "<p>Area: {$quote['district']} - " . ($has_area ? '✅' : '❌') . "</p>";
            
            if ($has_service && $has_area) {
                $matching_quotes++;
                echo "<p>✅ <strong>MATCH:</strong> General quote with service and area match</p>";
            } else {
                echo "<p>❌ <strong>NO MATCH:</strong> Missing " . (!$has_service ? 'service' : '') . (!$has_service && !$has_area ? ' and ' : '') . (!$has_area ? 'area' : '') . "</p>";
            }
        } else {
            echo "<p>❌ <strong>NO MATCH:</strong> Direct quote for contractor ID {$quote['specific_contractor_id']}</p>";
        }
        echo "</div>";
    }
    
    echo "<p><strong>Total matching quotes:</strong> {$matching_quotes}</p>";
    
    if ($matching_quotes == 0) {
        echo "<p>❌ <strong>RESULT:</strong> No quotes match this contractor's profile!</p>";
        echo "<p>This explains why the dashboard is empty.</p>";
    } else {
        echo "<p>✅ <strong>RESULT:</strong> Contractor should see {$matching_quotes} quotes!</p>";
        echo "<p>If the dashboard is still empty, there might be a code issue.</p>";
    }
}

echo "<h3>🔗 Quick Links:</h3>";
echo "<p><a href='contractor/login.php' target='_blank'>Contractor Login</a></p>";
echo "<p><a href='customer/dashboard.php' target='_blank'>Customer Dashboard (to create quotes)</a></p>";
?>
