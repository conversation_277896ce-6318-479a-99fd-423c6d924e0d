<?php
// Suppress PHP notices for cleaner user interface
error_reporting(E_ERROR | E_WARNING | E_PARSE);

session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

$success_message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $first_name = trim($_POST['first_name']);
        $last_name = trim($_POST['last_name']);
        $phone = trim($_POST['phone']);
        $address = trim($_POST['address']);
        $city = trim($_POST['city']);
        $postal_code = trim($_POST['postal_code']);
        $date_of_birth = $_POST['date_of_birth'];
        $gender = $_POST['gender'];
        $bio = trim($_POST['bio']);
        
        // Update customer profile
        $stmt = $pdo->prepare("
            UPDATE customer_profiles 
            SET first_name = ?, last_name = ?, phone = ?, address = ?, city = ?, 
                postal_code = ?, date_of_birth = ?, gender = ?, bio = ?, updated_at = NOW()
            WHERE user_id = ?
        ");
        $stmt->execute([
            $first_name, $last_name, $phone, $address, $city, 
            $postal_code, $date_of_birth, $gender, $bio, $_SESSION['user_id']
        ]);
        
        // Update email if changed
        if (isset($_POST['email']) && !empty($_POST['email'])) {
            $email = trim($_POST['email']);
            $stmt = $pdo->prepare("UPDATE users SET email = ? WHERE id = ?");
            $stmt->execute([$email, $_SESSION['user_id']]);
        }
        
        $success_message = 'Profile updated successfully!';
    } catch (PDOException $e) {
        $error_message = 'Error updating profile. Please try again.';
    }
}

// Get customer profile and user data
try {
    $stmt = $pdo->prepare("
        SELECT cp.*, u.email, u.created_at as user_created_at
        FROM customer_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE cp.user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $customer = $stmt->fetch();
    
    if (!$customer) {
        // Create profile if doesn't exist
        $stmt = $pdo->prepare("
            INSERT INTO customer_profiles (user_id, first_name, last_name, created_at) 
            VALUES (?, '', '', NOW())
        ");
        $stmt->execute([$_SESSION['user_id']]);
        
        // Fetch again
        $stmt = $pdo->prepare("
            SELECT cp.*, u.email, u.created_at as user_created_at
            FROM customer_profiles cp 
            JOIN users u ON cp.user_id = u.id 
            WHERE cp.user_id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $customer = $stmt->fetch();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: dashboard.php');
    exit();
}

// Get unread notifications count
$unread_count = 0;
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$_SESSION['user_id']]);
    $unread_count = $stmt->fetchColumn();
} catch (PDOException $e) {
    // Ignore error, keep count as 0
}

// Get customer statistics
try {
    // Count quotes requested
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM quote_requests WHERE customer_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $total_quotes = $stmt->fetchColumn();
    
    // Count active projects (quotes with status 'accepted' or 'in_progress')
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM quote_requests 
        WHERE customer_id = ? AND status IN ('accepted', 'in_progress')
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $active_projects = $stmt->fetchColumn();
    
    // Count completed projects
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM quote_requests 
        WHERE customer_id = ? AND status = 'completed'
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $completed_projects = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $total_quotes = 0;
    $active_projects = 0;
    $completed_projects = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>My Profile - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, profile" name="keywords">
    <meta content="Customer Profile - Brick & Click" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <style>
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .profile-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
            z-index: 1;
        }

        .profile-header .container {
            position: relative;
            z-index: 2;
        }

        .profile-avatar {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3.5rem;
            color: white;
            margin: 0 auto 1.5rem;
            border: 5px solid white;
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
            animation: pulse 3s infinite;
            transition: all 0.3s ease;
        }

        .profile-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 20px 50px rgba(0,0,0,0.4);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }
        
        .stats-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 2.5rem;
            text-align: center;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            border: none;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .stats-card:hover::before {
            left: 100%;
        }

        .stats-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 60px rgba(0,0,0,0.15);
        }

        .stats-number {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            animation: countUp 2s ease-out;
        }

        @keyframes countUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
            box-shadow: 0 10px 25px rgba(253, 126, 20, 0.3);
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }
        
        .profile-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-top: -4rem;
            position: relative;
            z-index: 10;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem 1.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.8);
        }

        .form-control:focus {
            border-color: #fd7e14;
            box-shadow: 0 0 0 0.3rem rgba(253, 126, 20, 0.15);
            background: white;
            transform: translateY(-2px);
        }

        .form-control:hover {
            border-color: #fd7e14;
            background: white;
        }

        .btn-update {
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            border: none;
            border-radius: 15px;
            padding: 1rem 3rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
        }

        .btn-update::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-update:hover::before {
            left: 100%;
        }

        .btn-update:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(253, 126, 20, 0.4);
            color: white;
        }
        
        .section-title {
            color: #212529;
            font-weight: 700;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            position: relative;
            display: inline-block;
            font-size: 1.5rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 4px;
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            border-radius: 2px;
        }

        .member-since {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-size: 1rem;
            display: inline-block;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            animation: fadeInUp 1s ease-out 0.5s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card {
            border-radius: 20px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.12);
        }

        .progress {
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(135deg, #28a745, #20c997);
            transition: width 1s ease-in-out;
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 1rem 1.5rem;
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
    </style>
</head>

<body>
    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
        <a href="../index.php" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="../img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="contractors.php" class="nav-item nav-link">Find Contractors</a>
                <a href="quotes.php" class="nav-item nav-link">My Quotes</a>
                <a href="cost_estimator.php" class="nav-item nav-link">Cost Estimator</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="profile.php" class="nav-item nav-link active">Profile</a>
            </div>
            <div class="dropdown">
                <a href="#" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($customer['first_name'] ?: 'User'); ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="notifications.php"><i class="fas fa-bell me-2"></i>Notifications <?php if ($unread_count > 0): ?><span class="badge bg-danger"><?php echo $unread_count; ?></span><?php endif; ?></a></li>
                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Profile Header Start -->
    <div class="profile-header">
        <div class="container text-center">
            <div class="profile-avatar">
                <i class="fas fa-user"></i>
            </div>
            <h2 class="mb-2"><?php echo htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']); ?></h2>
            <p class="mb-3"><?php echo htmlspecialchars($customer['email']); ?></p>
            <div class="member-since">
                <i class="fas fa-calendar-alt me-2"></i>
                Member since <?php echo date('F Y', strtotime($customer['user_created_at'])); ?>
            </div>
        </div>
    </div>
    <!-- Profile Header End -->

    <!-- Profile Content Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <!-- Statistics Cards -->
            <div class="row g-4 mb-5">
                <div class="col-lg-4 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number"><?php echo $total_quotes; ?></div>
                        <h6 class="mb-0">Total Quotes Requested</h6>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number"><?php echo $active_projects; ?></div>
                        <h6 class="mb-0">Active Projects</h6>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="stats-card">
                        <div class="stats-number"><?php echo $completed_projects; ?></div>
                        <h6 class="mb-0">Completed Projects</h6>
                    </div>
                </div>
            </div>

            <!-- Profile Form -->
            <div class="profile-card">
                <div class="p-4">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <!-- Personal Information -->
                        <div class="mb-5">
                            <h4 class="section-title">Personal Information</h4>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="first_name" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name"
                                           value="<?php echo htmlspecialchars($customer['first_name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="last_name" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name"
                                           value="<?php echo htmlspecialchars($customer['last_name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo htmlspecialchars($customer['email'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($customer['phone'] ?? ''); ?>"
                                           placeholder="+94 77 123 4567">
                                </div>
                                <div class="col-md-6">
                                    <label for="date_of_birth" class="form-label">Date of Birth</label>
                                    <input type="date" class="form-control" id="date_of_birth" name="date_of_birth"
                                           value="<?php echo htmlspecialchars($customer['date_of_birth'] ?? ''); ?>">
                                </div>
                                <div class="col-md-6">
                                    <label for="gender" class="form-label">Gender</label>
                                    <select class="form-control" id="gender" name="gender">
                                        <option value="">Select Gender</option>
                                        <option value="male" <?php echo ($customer['gender'] ?? '') === 'male' ? 'selected' : ''; ?>>Male</option>
                                        <option value="female" <?php echo ($customer['gender'] ?? '') === 'female' ? 'selected' : ''; ?>>Female</option>
                                        <option value="other" <?php echo ($customer['gender'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="mb-5">
                            <h4 class="section-title">Address Information</h4>
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="address" class="form-label">Street Address</label>
                                    <input type="text" class="form-control" id="address" name="address"
                                           value="<?php echo htmlspecialchars($customer['address'] ?? ''); ?>"
                                           placeholder="Enter your street address">
                                </div>
                                <div class="col-md-8">
                                    <label for="city" class="form-label">City</label>
                                    <input type="text" class="form-control" id="city" name="city"
                                           value="<?php echo htmlspecialchars($customer['city'] ?? ''); ?>"
                                           placeholder="Enter your city">
                                </div>
                                <div class="col-md-4">
                                    <label for="postal_code" class="form-label">Postal Code</label>
                                    <input type="text" class="form-control" id="postal_code" name="postal_code"
                                           value="<?php echo htmlspecialchars($customer['postal_code'] ?? ''); ?>"
                                           placeholder="Enter postal code">
                                </div>
                            </div>
                        </div>

                        <!-- Bio Section -->
                        <div class="mb-5">
                            <h4 class="section-title">About Me</h4>
                            <div class="row">
                                <div class="col-12">
                                    <label for="bio" class="form-label">Bio</label>
                                    <textarea class="form-control" id="bio" name="bio" rows="4"
                                              placeholder="Tell us a bit about yourself and your construction needs..."><?php echo htmlspecialchars($customer['bio'] ?? ''); ?></textarea>
                                    <div class="form-text">This information helps contractors understand your preferences and requirements.</div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-update me-3">
                                <i class="fas fa-save me-2"></i>Update Profile
                            </button>
                            <a href="payment_history.php" class="btn btn-info me-3">
                                <i class="fas fa-history me-2"></i>Payment History
                            </a>
                            <a href="dashboard.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Additional Information Cards -->
            <div class="row g-4 mt-4">
                <div class="col-lg-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h5 class="card-title mb-3">
                                <i class="fas fa-shield-alt text-primary me-2"></i>Account Security
                            </h5>
                            <p class="card-text text-muted mb-3">Keep your account secure with strong passwords and updated information.</p>
                            <a href="settings.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-cog me-2"></i>Security Settings
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h5 class="card-title mb-3">
                                <i class="fas fa-bell text-warning me-2"></i>Notifications
                            </h5>
                            <p class="card-text text-muted mb-3">Manage your notification preferences for quotes, messages, and updates.</p>
                            <a href="settings.php" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-bell me-2"></i>Notification Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-body p-4">
                    <h5 class="card-title mb-4">
                        <i class="fas fa-history text-info me-2"></i>Recent Activity
                    </h5>
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="mb-3">
                                <i class="fas fa-quote-right fa-2x text-primary mb-2"></i>
                                <h6>Latest Quote Request</h6>
                                <p class="text-muted small">
                                    <?php
                                    try {
                                        $stmt = $pdo->prepare("
                                            SELECT title, created_at
                                            FROM quote_requests
                                            WHERE customer_id = ?
                                            ORDER BY created_at DESC
                                            LIMIT 1
                                        ");
                                        $stmt->execute([$_SESSION['user_id']]);
                                        $latest_quote = $stmt->fetch();

                                        if ($latest_quote) {
                                            echo htmlspecialchars($latest_quote['title']) . '<br>';
                                            echo '<small class="text-muted">' . date('M j, Y', strtotime($latest_quote['created_at'])) . '</small>';
                                        } else {
                                            echo 'No quotes requested yet';
                                        }
                                    } catch (PDOException $e) {
                                        echo 'No recent activity';
                                    }
                                    ?>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="mb-3">
                                <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                                <h6>Profile Completion</h6>
                                <p class="text-muted small">
                                    <?php
                                    $completion = 0;
                                    if (!empty($customer['first_name'] ?? '')) $completion += 15;
                                    if (!empty($customer['last_name'] ?? '')) $completion += 15;
                                    if (!empty($customer['email'] ?? '')) $completion += 20;
                                    if (!empty($customer['phone'] ?? '')) $completion += 15;
                                    if (!empty($customer['address'] ?? '')) $completion += 10;
                                    if (!empty($customer['city'] ?? '')) $completion += 10;
                                    if (!empty($customer['date_of_birth'] ?? '')) $completion += 5;
                                    if (!empty($customer['gender'] ?? '')) $completion += 5;
                                    if (!empty($customer['bio'] ?? '')) $completion += 5;

                                    echo $completion . '% Complete';
                                    ?>
                                </p>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-success" style="width: <?php echo $completion; ?>%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="mb-3">
                                <i class="fas fa-calendar-check fa-2x text-warning mb-2"></i>
                                <h6>Member Since</h6>
                                <p class="text-muted small">
                                    <?php echo date('F j, Y', strtotime($customer['user_created_at'])); ?><br>
                                    <small class="text-muted">
                                        <?php
                                        $days = floor((time() - strtotime($customer['user_created_at'])) / (60 * 60 * 24));
                                        echo $days . ' days ago';
                                        ?>
                                    </small>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Profile Content End -->

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>

    <script>
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const firstName = document.getElementById('first_name').value.trim();
            const lastName = document.getElementById('last_name').value.trim();
            const email = document.getElementById('email').value.trim();

            if (!firstName || !lastName || !email) {
                e.preventDefault();
                alert('Please fill in all required fields (First Name, Last Name, and Email).');
                return false;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('Please enter a valid email address.');
                return false;
            }

            // Phone validation (if provided)
            const phone = document.getElementById('phone').value.trim();
            if (phone && !/^[\+]?[0-9\s\-\(\)]{10,}$/.test(phone)) {
                e.preventDefault();
                alert('Please enter a valid phone number.');
                return false;
            }
        });

        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
