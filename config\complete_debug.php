<?php
session_start();
require_once 'database.php';

echo "<h2>🔍 Complete System Debug</h2>";

try {
    // 1. Check database connection
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>✅ Database Connection</h3>";
    echo "<p>Database connection successful!</p>";
    echo "</div>";
    
    // 2. Check admin users
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>👨‍💼 Admin Users</h3>";
    $stmt = $pdo->query("SELECT id, email, user_type, status FROM users WHERE user_type = 'admin'");
    $admins = $stmt->fetchAll();
    
    if (empty($admins)) {
        echo "<p style='color: red;'>❌ No admin users found!</p>";
        echo "<p>Creating admin user...</p>";
        
        // Create admin user
        $admin_email = '<EMAIL>';
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status, created_at, updated_at) VALUES (?, ?, 'admin', 'approved', NOW(), NOW())");
        $stmt->execute([$admin_email, $admin_password]);
        
        echo "<p style='color: green;'>✅ Admin user created!</p>";
        echo "<p><strong>Email:</strong> <EMAIL></p>";
        echo "<p><strong>Password:</strong> admin123</p>";
    } else {
        foreach ($admins as $admin) {
            echo "<p><strong>ID:</strong> " . $admin['id'] . " | <strong>Email:</strong> " . htmlspecialchars($admin['email']) . " | <strong>Status:</strong> " . $admin['status'] . "</p>";
        }
    }
    echo "</div>";
    
    // 3. Check contractors
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🏗️ Contractors</h3>";
    $stmt = $pdo->query("SELECT id, email, user_type, status, created_at FROM users WHERE user_type = 'contractor' ORDER BY created_at DESC");
    $contractors = $stmt->fetchAll();
    
    if (empty($contractors)) {
        echo "<p style='color: red;'>❌ No contractors found!</p>";
        echo "<p><a href='add_sample_contractor.php'>Add Sample Contractor</a></p>";
    } else {
        echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Email</th><th>Status</th><th>Created</th></tr>";
        foreach ($contractors as $contractor) {
            $status_color = ['pending' => 'orange', 'approved' => 'green', 'rejected' => 'red', 'suspended' => 'gray'][$contractor['status']] ?? 'black';
            echo "<tr>";
            echo "<td>" . $contractor['id'] . "</td>";
            echo "<td>" . htmlspecialchars($contractor['email']) . "</td>";
            echo "<td style='color: $status_color; font-weight: bold;'>" . $contractor['status'] . "</td>";
            echo "<td>" . $contractor['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
    // 4. Test approval functionality
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_approval'])) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🧪 Testing Approval</h3>";
        
        $contractor_id = (int)$_POST['contractor_id'];
        echo "<p><strong>Testing approval for contractor ID:</strong> $contractor_id</p>";
        
        try {
            // Check if contractor exists
            $stmt = $pdo->prepare("SELECT id, email, status FROM users WHERE id = ? AND user_type = 'contractor'");
            $stmt->execute([$contractor_id]);
            $contractor = $stmt->fetch();
            
            if (!$contractor) {
                echo "<p style='color: red;'>❌ Contractor not found with ID: $contractor_id</p>";
            } else {
                echo "<p><strong>Found contractor:</strong> " . htmlspecialchars($contractor['email']) . " (Current status: " . $contractor['status'] . ")</p>";
                
                // Try to update status
                $stmt = $pdo->prepare("UPDATE users SET status = 'approved' WHERE id = ? AND user_type = 'contractor'");
                $result = $stmt->execute([$contractor_id]);
                $affected_rows = $stmt->rowCount();
                
                echo "<p><strong>Update result:</strong> " . ($result ? 'Success' : 'Failed') . "</p>";
                echo "<p><strong>Affected rows:</strong> $affected_rows</p>";
                
                if ($affected_rows > 0) {
                    echo "<p style='color: green;'>✅ Status updated successfully!</p>";
                    
                    // Verify the update
                    $stmt = $pdo->prepare("SELECT status FROM users WHERE id = ?");
                    $stmt->execute([$contractor_id]);
                    $new_status = $stmt->fetchColumn();
                    echo "<p><strong>New status:</strong> $new_status</p>";
                } else {
                    echo "<p style='color: red;'>❌ No rows were updated</p>";
                }
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        echo "</div>";
    }
    
    // 5. Test form
    if (!empty($contractors)) {
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🧪 Test Approval Function</h3>";
        echo "<form method='POST'>";
        echo "<select name='contractor_id' required>";
        foreach ($contractors as $contractor) {
            echo "<option value='" . $contractor['id'] . "'>" . htmlspecialchars($contractor['email']) . " (ID: " . $contractor['id'] . ", Status: " . $contractor['status'] . ")</option>";
        }
        echo "</select>";
        echo "<button type='submit' name='test_approval' style='background: green; color: white; padding: 10px 15px; border: none; border-radius: 3px; margin-left: 10px;'>Test Approval</button>";
        echo "</form>";
        echo "</div>";
    }
    
    // 6. Session info
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🔐 Current Session</h3>";
    if (isset($_SESSION['user_id'])) {
        echo "<p><strong>Logged in as:</strong> " . htmlspecialchars($_SESSION['user_email'] ?? $_SESSION['email'] ?? 'Unknown') . "</p>";
        echo "<p><strong>User Type:</strong> " . htmlspecialchars($_SESSION['user_type']) . "</p>";
        echo "<p><strong>User ID:</strong> " . $_SESSION['user_id'] . "</p>";
    } else {
        echo "<p>❌ No active session</p>";
    }
    echo "</div>";
    
    // 7. Quick links
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🔗 Quick Links</h3>";
    echo "<p><a href='../admin/login.php' target='_blank'>Admin Login</a></p>";
    echo "<p><a href='../admin/contractors.php' target='_blank'>Admin Contractors Page</a></p>";
    echo "<p><a href='../login.php' target='_blank'>Contractor Login</a></p>";
    echo "<p><a href='fix_contractor_status.php'>Fix Contractor Status</a></p>";
    echo "<p><a href='add_sample_contractor.php'>Add Sample Contractor</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Complete System Debug</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        h2, h3 { color: #333; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>🔧 BrickClick Complete System Debug</h1>
    <p>This page provides comprehensive debugging for the contractor approval system.</p>
</body>
</html>
