<?php
require_once 'config/database.php';

echo "<h2>🔧 Fix General Quote Request System</h2>";

try {
    // Step 1: Check and add missing column
    echo "<h3>Step 1: Database Structure</h3>";
    
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        echo "<p>❌ Missing specific_contractor_id column. Adding...</p>";
        
        $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        $pdo->exec("ALTER TABLE quote_requests ADD INDEX idx_specific_contractor_id (specific_contractor_id)");
        $pdo->exec("ALTER TABLE quote_requests ADD FOREIGN KEY (specific_contractor_id) REFERENCES users(id) ON DELETE SET NULL");
        
        echo "<p style='color: green;'>✅ Added specific_contractor_id column</p>";
    } else {
        echo "<p style='color: green;'>✅ specific_contractor_id column exists</p>";
    }
    
    // Step 2: Check service categories
    echo "<h3>Step 2: Service Categories</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM service_categories WHERE is_active = 1");
    $category_count = $stmt->fetchColumn();
    
    if ($category_count == 0) {
        echo "<p>❌ No active service categories. Adding...</p>";
        
        $categories = [
            ['House Construction', 'නිවාස ඉදිකිරීම', 'வீட்டு கட்டுமானம்'],
            ['Building Renovation', 'ගොඩනැගිලි අලුත්වැඩියා', 'கட்டிட புனரமைப்பு'],
            ['Commercial Construction', 'වාණිජ ඉදිකිරීම', 'வணிக கட்டுமானம்'],
            ['Interior Design & Finishing', 'අභ්‍යන්තර සැලසුම් සහ නිම කිරීම', 'உள்துறை வடிவமைப்பு மற்றும் முடித்தல்'],
            ['Roofing & Waterproofing', 'වහල සහ ජල ආරක්ෂණ', 'கூரை மற்றும் நீர்ப்புகாமை'],
            ['Electrical Work', 'විදුලි වැඩ', 'மின் வேலை'],
            ['Plumbing & Sanitation', 'ජල නල සහ සනීපාරක්ෂක', 'குழாய் மற்றும் சுகாதாரம்'],
            ['Landscaping & Gardening', 'භූමි අලංකරණ සහ උද්‍යාන', 'இயற்கை அலங்காரம் மற்றும் தோட்டக்கலை'],
            ['Swimming Pool Construction', 'පිහිනුම් තටාක ඉදිකිරීම', 'நீச்சல் குளம் கட்டுமானம்'],
            ['Road & Infrastructure', 'මාර්ග සහ යටිතල පහසුකම්', 'சாலை மற்றும் உள்கட்டமைப்பு']
        ];
        
        foreach ($categories as $category) {
            $stmt = $pdo->prepare("INSERT INTO service_categories (name_en, name_si, name_ta, is_active) VALUES (?, ?, ?, 1)");
            $stmt->execute($category);
        }
        
        echo "<p style='color: green;'>✅ Added " . count($categories) . " service categories</p>";
    } else {
        echo "<p style='color: green;'>✅ Found $category_count active service categories</p>";
    }
    
    // Step 3: Check and fix contractor data
    echo "<h3>Step 3: Contractor Data Analysis</h3>";
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as total,
               SUM(CASE WHEN u.status = 'approved' THEN 1 ELSE 0 END) as approved,
               SUM(CASE WHEN cp.service_areas IS NOT NULL AND cp.service_areas != '[]' THEN 1 ELSE 0 END) as with_areas,
               SUM(CASE WHEN cp.service_types IS NOT NULL AND cp.service_types != '[]' THEN 1 ELSE 0 END) as with_services
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor'
    ");
    $stats = $stmt->fetch();
    
    echo "<p>📊 Contractor Statistics:</p>";
    echo "<ul>";
    echo "<li>Total contractors: {$stats['total']}</li>";
    echo "<li>Approved contractors: {$stats['approved']}</li>";
    echo "<li>With service areas: {$stats['with_areas']}</li>";
    echo "<li>With service types: {$stats['with_services']}</li>";
    echo "</ul>";
    
    // Step 4: Fix contractor data format
    echo "<h3>Step 4: Fix Contractor Service Data</h3>";
    
    $stmt = $pdo->query("
        SELECT cp.user_id, cp.business_name, cp.service_areas, cp.service_types 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
    ");
    $contractors = $stmt->fetchAll();
    
    $fixed_count = 0;
    foreach ($contractors as $contractor) {
        $needs_update = false;
        $service_areas = json_decode($contractor['service_areas'], true);
        $service_types = json_decode($contractor['service_types'], true);
        
        // Fix service areas if empty or invalid
        if (!is_array($service_areas) || empty($service_areas)) {
            $service_areas = ['Colombo', 'Gampaha', 'Kalutara']; // Default areas
            $needs_update = true;
        }
        
        // Fix service types if empty or invalid
        if (!is_array($service_types) || empty($service_types)) {
            $service_types = [1, 2]; // Default to House Construction and Renovation
            $needs_update = true;
        } else {
            // Ensure service types are integers
            $new_service_types = [];
            foreach ($service_types as $type) {
                if (is_numeric($type)) {
                    $new_service_types[] = (int)$type;
                } else {
                    // Try to map service name to ID
                    $stmt_map = $pdo->prepare("SELECT id FROM service_categories WHERE name_en = ? LIMIT 1");
                    $stmt_map->execute([$type]);
                    $service_id = $stmt_map->fetchColumn();
                    if ($service_id) {
                        $new_service_types[] = (int)$service_id;
                    }
                }
            }
            if (!empty($new_service_types) && $new_service_types != $service_types) {
                $service_types = $new_service_types;
                $needs_update = true;
            }
        }
        
        if ($needs_update) {
            $stmt_update = $pdo->prepare("
                UPDATE contractor_profiles 
                SET service_areas = ?, service_types = ? 
                WHERE user_id = ?
            ");
            $stmt_update->execute([
                json_encode($service_areas),
                json_encode($service_types),
                $contractor['user_id']
            ]);
            $fixed_count++;
            
            echo "<p>🔧 Fixed data for: " . htmlspecialchars($contractor['business_name']) . "</p>";
        }
    }
    
    echo "<p style='color: green;'>✅ Fixed $fixed_count contractor profiles</p>";
    
    // Step 5: Test the quote matching system
    echo "<h3>Step 5: Test Quote Matching</h3>";
    
    $test_district = 'Colombo';
    $test_service_id = 1; // House Construction
    
    echo "<p>Testing with District: '$test_district' and Service ID: $test_service_id</p>";
    
    // Use the same query logic as the fixed process_quote_request.php
    $stmt = $pdo->prepare("
        SELECT u.id, cp.business_name, cp.contact_person, cp.service_areas, cp.service_types
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.status = 'approved'
        AND u.user_type = 'contractor'
        AND cp.service_areas IS NOT NULL 
        AND cp.service_types IS NOT NULL
        AND (
            JSON_CONTAINS(cp.service_areas, ?) 
            OR cp.service_areas LIKE ?
        )
        AND (
            JSON_CONTAINS(cp.service_types, ?)
            OR cp.service_types LIKE ?
        )
    ");

    $district_json = json_encode($test_district);
    $service_json = json_encode($test_service_id);
    $district_like = "%\"$test_district\"%";
    $service_like = "%\"$test_service_id\"%";

    $stmt->execute([
        $district_json,
        $district_like,
        $service_json,
        $service_like
    ]);
    
    $potential_contractors = $stmt->fetchAll();
    
    // Filter results in PHP
    $matching_contractors = [];
    foreach ($potential_contractors as $contractor) {
        $service_areas = json_decode($contractor['service_areas'], true);
        $service_types = json_decode($contractor['service_types'], true);
        
        $has_area = is_array($service_areas) && in_array($test_district, $service_areas);
        $has_service = is_array($service_types) && (
            in_array($test_service_id, $service_types) || 
            in_array((string)$test_service_id, $service_types)
        );
        
        if ($has_area && $has_service) {
            $matching_contractors[] = $contractor;
        }
    }
    
    echo "<p><strong>Found " . count($matching_contractors) . " matching contractors:</strong></p>";
    
    if (!empty($matching_contractors)) {
        echo "<ul>";
        foreach ($matching_contractors as $contractor) {
            echo "<li>" . htmlspecialchars($contractor['business_name']) . " (ID: " . $contractor['id'] . ")</li>";
        }
        echo "</ul>";
        echo "<p style='color: green;'>✅ Quote matching system is working!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ No contractors found. This might indicate a data issue.</p>";
        
        // Show some debug info
        echo "<h4>Debug Information:</h4>";
        echo "<p>Total potential contractors found by SQL: " . count($potential_contractors) . "</p>";
        
        if (!empty($potential_contractors)) {
            echo "<p>Sample contractor data:</p>";
            $sample = $potential_contractors[0];
            echo "<pre>";
            echo "Business: " . htmlspecialchars($sample['business_name']) . "\n";
            echo "Service Areas: " . htmlspecialchars($sample['service_areas']) . "\n";
            echo "Service Types: " . htmlspecialchars($sample['service_types']) . "\n";
            echo "Decoded Areas: " . print_r(json_decode($sample['service_areas'], true), true) . "\n";
            echo "Decoded Types: " . print_r(json_decode($sample['service_types'], true), true) . "\n";
            echo "</pre>";
        }
    }
    
    echo "<h3>✅ General Quote System Fix Complete!</h3>";
    echo "<p>The system should now properly match contractors for general quote requests.</p>";
    echo "<p><a href='customer/request_quote.php'>Test General Quote Request</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
