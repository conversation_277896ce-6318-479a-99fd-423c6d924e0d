<?php
require_once 'config/database.php';

echo "<h2>Running Database Migration</h2>";

try {
    // Check if specific_contractor_id column already exists
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $column_exists = $stmt->fetch();
    
    if ($column_exists) {
        echo "<p style='color: green;'>Column 'specific_contractor_id' already exists in quote_requests table.</p>";
    } else {
        echo "<p>Adding 'specific_contractor_id' column to quote_requests table...</p>";
        
        // Run the migration
        $migration_sql = "
            ALTER TABLE quote_requests 
            ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline,
            ADD INDEX idx_specific_contractor_id (specific_contractor_id),
            ADD FOREIGN KEY (specific_contractor_id) REFERENCES users(id) ON DELETE SET NULL;
        ";
        
        $pdo->exec($migration_sql);
        echo "<p style='color: green;'>Migration completed successfully!</p>";
    }
    
    // Update table comment
    $pdo->exec("ALTER TABLE quote_requests COMMENT = 'Customer quote requests - can be sent to all contractors or specific contractor'");
    
    // Show updated table structure
    echo "<h3>Updated Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE quote_requests");
    $columns = $stmt->fetchAll();
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p style='color: green; font-weight: bold;'>Database migration completed! The quote responses should now work properly.</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Migration failed: " . $e->getMessage() . "</p>";
}
?>
