<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

// Get customer profile
try {
    $stmt = $pdo->prepare("SELECT * FROM customer_profiles WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $customer = $stmt->fetch();
    
    if (!$customer) {
        $_SESSION['error'] = 'Customer profile not found.';
        header('Location: ../login.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: ../login.php');
    exit();
}

// Get payment history
try {
    $stmt = $pdo->prepare("
        SELECT pp.*, qres.quoted_amount, qr.title, qr.description, qr.location, qr.district,
               cp.business_name, cp.contact_person, cp.phone,
               r.rating, r.review_text
        FROM project_payments pp
        JOIN quote_responses qres ON pp.quote_response_id = qres.id
        JOIN quote_requests qr ON qres.quote_request_id = qr.id
        JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
        LEFT JOIN reviews r ON pp.id = r.payment_id
        WHERE pp.customer_id = ?
        ORDER BY pp.payment_date DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $payments = $stmt->fetchAll();
    
    // Calculate totals
    $total_paid = 0;
    $total_payments = count($payments);
    foreach ($payments as $payment) {
        $total_paid += $payment['amount'];
    }
} catch (PDOException $e) {
    $payments = [];
    $total_paid = 0;
    $total_payments = 0;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Payment History - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border: none;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .payment-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            transition: all 0.3s ease;
        }
        
        .payment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .payment-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .payment-amount {
            font-size: 1.5rem;
            font-weight: 700;
            color: #28a745;
        }
        
        .contractor-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .btn-view-receipt {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-view-receipt:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
            color: white;
        }
        
        .review-badge {
            background: #fff3cd;
            color: #856404;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .review-given {
            background: #d4edda;
            color: #155724;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
</head>

<body>
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 mb-3">Payment History</h1>
                    <p class="lead mb-0">Track all your payments and transactions</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <a href="profile.php" class="btn btn-light btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>Back to Profile
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Payment Statistics -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number"><?php echo $total_payments; ?></div>
                    <h6 class="text-muted">Total Payments</h6>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number">Rs. <?php echo number_format($total_paid, 2); ?></div>
                    <h6 class="text-muted">Total Amount Paid</h6>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-number"><?php echo count(array_filter($payments, function($p) { return $p['payment_status'] === 'completed'; })); ?></div>
                    <h6 class="text-muted">Successful Payments</h6>
                </div>
            </div>
        </div>

        <!-- Payment History -->
        <?php if (empty($payments)): ?>
        <div class="empty-state">
            <i class="fas fa-receipt"></i>
            <h4>No Payment History</h4>
            <p>You haven't made any payments yet. Start by accepting a quote and making a down payment.</p>
            <a href="quotes.php" class="btn btn-primary btn-lg">
                <i class="fas fa-search me-2"></i>View My Quotes
            </a>
        </div>
        <?php else: ?>
        <div class="row">
            <div class="col-12">
                <h4 class="mb-4">Payment Transactions</h4>
                
                <?php foreach ($payments as $payment): ?>
                <div class="payment-card">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="d-flex align-items-center mb-2">
                                <h5 class="mb-0 me-3"><?php echo htmlspecialchars($payment['title']); ?></h5>
                                <span class="payment-status status-<?php echo $payment['payment_status']; ?>">
                                    <?php echo ucfirst($payment['payment_status']); ?>
                                </span>
                                <?php if ($payment['rating']): ?>
                                <span class="review-badge review-given">
                                    <i class="fas fa-star me-1"></i>Review Given
                                </span>
                                <?php else: ?>
                                <span class="review-badge">
                                    <i class="fas fa-star me-1"></i>Review Pending
                                </span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="contractor-info">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">Contractor:</small><br>
                                        <strong><?php echo htmlspecialchars($payment['business_name']); ?></strong>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">Location:</small><br>
                                        <strong><?php echo htmlspecialchars($payment['location'] . ', ' . $payment['district']); ?></strong>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <small class="text-muted">Payment Type:</small><br>
                                    <strong><?php echo ucfirst(str_replace('_', ' ', $payment['payment_type'])); ?></strong>
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">Payment Method:</small><br>
                                    <strong><?php echo ucfirst($payment['payment_method']); ?></strong>
                                </div>
                                <div class="col-md-4">
                                    <small class="text-muted">Transaction ID:</small><br>
                                    <strong><?php echo htmlspecialchars($payment['transaction_id']); ?></strong>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 text-lg-end">
                            <div class="payment-amount mb-3">
                                Rs. <?php echo number_format($payment['amount'], 2); ?>
                            </div>
                            <div class="text-muted mb-3">
                                <?php echo date('M j, Y g:i A', strtotime($payment['payment_date'])); ?>
                            </div>
                            <a href="payment_success.php?payment_id=<?php echo $payment['id']; ?>" class="btn-view-receipt">
                                <i class="fas fa-receipt me-2"></i>View Receipt
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
