<?php
require_once 'config/database.php';

echo "<h2>🔍 Reviews Table Structure Analysis</h2>";

try {
    // Check if reviews table exists
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'reviews'");
    $stmt->execute();
    $reviews_exists = $stmt->fetch();
    
    if ($reviews_exists) {
        echo "<p>✅ Reviews table exists</p>";
        
        // Get table structure
        $stmt = $pdo->prepare("DESCRIBE reviews");
        $stmt->execute();
        $columns = $stmt->fetchAll();
        
        echo "<h3>Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        $required_fields = [];
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
            
            // Track required fields (NOT NULL and no default)
            if ($column['Null'] === 'NO' && $column['Default'] === null && $column['Extra'] !== 'auto_increment') {
                $required_fields[] = $column['Field'];
            }
        }
        echo "</table>";
        
        echo "<h3>Required Fields (NOT NULL, no default):</h3>";
        echo "<ul>";
        foreach ($required_fields as $field) {
            echo "<li>$field</li>";
        }
        echo "</ul>";
        
        // Get foreign key constraints
        $stmt = $pdo->prepare("
            SELECT 
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = 'brick_click' 
            AND TABLE_NAME = 'reviews' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $stmt->execute();
        $foreign_keys = $stmt->fetchAll();
        
        echo "<h3>Foreign Key Constraints:</h3>";
        if (!empty($foreign_keys)) {
            echo "<ul>";
            foreach ($foreign_keys as $fk) {
                echo "<li>{$fk['COLUMN_NAME']} → {$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No foreign key constraints found</p>";
        }
        
        // Check current data
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM reviews");
        $stmt->execute();
        $count = $stmt->fetchColumn();
        echo "<h3>Current Data:</h3>";
        echo "<p>Total reviews: $count</p>";
        
        if ($count > 0) {
            $stmt = $pdo->prepare("SELECT * FROM reviews LIMIT 3");
            $stmt->execute();
            $sample_data = $stmt->fetchAll();
            echo "<h4>Sample Data:</h4>";
            echo "<pre>" . print_r($sample_data, true) . "</pre>";
        }
        
        // Check related tables
        echo "<h3>Related Tables Check:</h3>";
        
        // Check quote_requests
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM quote_requests");
        $stmt->execute();
        $quote_count = $stmt->fetchColumn();
        echo "<p>Quote requests available: $quote_count</p>";
        
        // Check project_payments if referenced
        $stmt = $pdo->prepare("SHOW TABLES LIKE 'project_payments'");
        $stmt->execute();
        $payments_exists = $stmt->fetch();
        if ($payments_exists) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM project_payments");
            $stmt->execute();
            $payment_count = $stmt->fetchColumn();
            echo "<p>Project payments available: $payment_count</p>";
        } else {
            echo "<p>Project payments table: Not found</p>";
        }
        
        // Check quote_responses
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM quote_responses");
        $stmt->execute();
        $response_count = $stmt->fetchColumn();
        echo "<p>Quote responses available: $response_count</p>";
        
    } else {
        echo "<p>❌ Reviews table does not exist</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Reviews Table Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        table { margin: 20px 0; width: 100%; }
        th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
</body>
</html>
