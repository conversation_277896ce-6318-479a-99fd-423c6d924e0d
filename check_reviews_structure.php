<?php
require_once 'config/database.php';

echo "<h2>Reviews Table Structure Check</h2>";

try {
    // Check if reviews table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'reviews'");
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "<h3>Current Reviews Table Structure:</h3>";
        $stmt = $pdo->query("DESCRIBE reviews");
        $columns = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        $has_payment_id = false;
        $has_quote_request_id = false;
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
            
            if ($column['Field'] === 'payment_id') {
                $has_payment_id = true;
            }
            if ($column['Field'] === 'quote_request_id') {
                $has_quote_request_id = true;
            }
        }
        echo "</table>";
        
        echo "<h3>Column Analysis:</h3>";
        echo "<p>Has payment_id column: " . ($has_payment_id ? "YES" : "NO") . "</p>";
        echo "<p>Has quote_request_id column: " . ($has_quote_request_id ? "YES" : "NO") . "</p>";
        
        if (!$has_payment_id) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Issue Found:</h4>";
            echo "<p>The reviews table is missing the 'payment_id' column that is expected by the quote_responses.php query.</p>";
            echo "<p>The query tries to join: <code>LEFT JOIN reviews r ON pp.id = r.payment_id</code></p>";
            echo "</div>";
        }
        
        // Check what data exists
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM reviews");
        $review_count = $stmt->fetchColumn();
        echo "<p>Total reviews in table: $review_count</p>";
        
        if ($review_count > 0) {
            echo "<h3>Sample Review Data:</h3>";
            $stmt = $pdo->query("SELECT * FROM reviews LIMIT 3");
            $sample_reviews = $stmt->fetchAll();
            
            if (!empty($sample_reviews)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                $first_row = true;
                foreach ($sample_reviews as $review) {
                    if ($first_row) {
                        echo "<tr>";
                        foreach (array_keys($review) as $key) {
                            if (!is_numeric($key)) {
                                echo "<th>" . htmlspecialchars($key) . "</th>";
                            }
                        }
                        echo "</tr>";
                        $first_row = false;
                    }
                    
                    echo "<tr>";
                    foreach ($review as $key => $value) {
                        if (!is_numeric($key)) {
                            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                        }
                    }
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>Reviews table does not exist!</p>";
    }
    
    // Check project_payments table
    echo "<h3>Project Payments Table Check:</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'project_payments'");
    $pp_exists = $stmt->fetch();
    
    if ($pp_exists) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM project_payments");
        $pp_count = $stmt->fetchColumn();
        echo "<p>Project payments table exists with $pp_count records</p>";
    } else {
        echo "<p style='color: red;'>Project payments table does not exist!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>
