<?php
require_once 'config/database.php';

echo "<h2>🔍 Quote System Check</h2>";

try {
    // Step 1: Check if specific_contractor_id column exists
    echo "<h3>Step 1: Database Structure</h3>";
    
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $column_exists = $stmt->fetch();
    
    if ($column_exists) {
        echo "<p style='color: green;'>✅ specific_contractor_id column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ specific_contractor_id column missing - adding it...</p>";
        $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        echo "<p style='color: green;'>✅ Added specific_contractor_id column</p>";
    }
    
    // Step 2: Check existing quotes
    echo "<h3>Step 2: Existing Quotes</h3>";
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as total_quotes,
               COUNT(CASE WHEN specific_contractor_id IS NULL THEN 1 END) as general_quotes,
               COUNT(CASE WHEN specific_contractor_id IS NOT NULL THEN 1 END) as specific_quotes,
               COUNT(CASE WHEN status = 'open' THEN 1 END) as open_quotes
        FROM quote_requests
    ");
    $stats = $stmt->fetch();
    
    echo "<p><strong>Quote Statistics:</strong></p>";
    echo "<ul>";
    echo "<li>Total quotes: " . $stats['total_quotes'] . "</li>";
    echo "<li>General quotes: " . $stats['general_quotes'] . "</li>";
    echo "<li>Specific quotes: " . $stats['specific_quotes'] . "</li>";
    echo "<li>Open quotes: " . $stats['open_quotes'] . "</li>";
    echo "</ul>";
    
    // Step 3: Show recent quotes
    echo "<h3>Step 3: Recent Quotes</h3>";
    
    $stmt = $pdo->query("
        SELECT qr.*, sc.name_en as service_name 
        FROM quote_requests qr 
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id 
        ORDER BY qr.created_at DESC 
        LIMIT 5
    ");
    $quotes = $stmt->fetchAll();
    
    if (count($quotes) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Service</th><th>District</th><th>Type</th><th>Status</th><th>Created</th></tr>";
        foreach ($quotes as $quote) {
            $type = $quote['specific_contractor_id'] ? 'Specific' : 'General';
            echo "<tr>";
            echo "<td>" . $quote['id'] . "</td>";
            echo "<td>" . htmlspecialchars(substr($quote['title'], 0, 30)) . "...</td>";
            echo "<td>" . htmlspecialchars($quote['service_name'] ?? 'Unknown') . "</td>";
            echo "<td>" . htmlspecialchars($quote['district']) . "</td>";
            echo "<td>" . $type . "</td>";
            echo "<td>" . $quote['status'] . "</td>";
            echo "<td>" . $quote['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No quotes found in database.</p>";
    }
    
    // Step 4: Check contractors
    echo "<h3>Step 4: Contractor Check</h3>";
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as total_contractors,
               COUNT(CASE WHEN u.status = 'approved' THEN 1 END) as approved_contractors
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.user_type = 'contractor'
    ");
    $contractor_stats = $stmt->fetch();
    
    echo "<p><strong>Contractor Statistics:</strong></p>";
    echo "<ul>";
    echo "<li>Total contractors: " . $contractor_stats['total_contractors'] . "</li>";
    echo "<li>Approved contractors: " . $contractor_stats['approved_contractors'] . "</li>";
    echo "</ul>";
    
    // Step 5: Test contractor dashboard query
    echo "<h3>Step 5: Test Contractor Dashboard Query</h3>";
    
    // Get first approved contractor
    $stmt = $pdo->query("
        SELECT u.id, cp.business_name, cp.service_types, cp.service_areas
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        LIMIT 1
    ");
    $test_contractor = $stmt->fetch();
    
    if ($test_contractor) {
        echo "<p>Testing with contractor: " . htmlspecialchars($test_contractor['business_name']) . "</p>";
        
        // Test the dashboard query
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, cp.district,
                   (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            WHERE qr.status = 'open'
            AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
            ORDER BY qr.created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$test_contractor['id'], $test_contractor['id']]);
        $dashboard_quotes = $stmt->fetchAll();
        
        echo "<p>Dashboard query returned: " . count($dashboard_quotes) . " quotes</p>";
        
        if (count($dashboard_quotes) > 0) {
            echo "<p><strong>Quotes found:</strong></p>";
            echo "<ul>";
            foreach ($dashboard_quotes as $quote) {
                $type = $quote['specific_contractor_id'] ? 'Specific' : 'General';
                echo "<li>ID {$quote['id']}: " . htmlspecialchars($quote['title']) . " ({$type})</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠️ No quotes found for this contractor</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ No approved contractors found</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>
