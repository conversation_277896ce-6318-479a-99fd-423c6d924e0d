<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Sign Up - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, signup" name="keywords">
    <meta content="Join Brick & Click - Sri Lanka's leading construction contractor platform" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">

    <style>
        .signup-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #212529 0%, #34495e 100%);
            display: flex;
            align-items: center;
            padding: 2rem 0;
        }
        
        .signup-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .user-type-selector {
            background: #f8f9fa;
            padding: 2rem;
            text-align: center;
        }
        
        .user-type-card {
            background: white;
            border: 3px solid #e9ecef;
            border-radius: 15px;
            padding: 2rem;
            margin: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .user-type-card:hover {
            border-color: #fd7e14;
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(253, 126, 20, 0.2);
        }
        
        .user-type-card.active {
            border-color: #fd7e14;
            background: linear-gradient(135deg, #fd7e14, #ff9500);
            color: white;
        }
        
        .user-type-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #fd7e14;
        }
        
        .user-type-card.active .user-type-icon {
            color: white;
        }
        
        .form-section {
            padding: 2rem;
            display: none;
        }
        
        .form-section.active {
            display: block;
        }
        
        .form-control {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 1rem 1.25rem;
            font-size: 1rem;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #C5172E;
            box-shadow: 0 0 0 0.2rem rgba(197, 23, 46, 0.25);
            background: white;
            outline: none;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.75rem;
        }
        
        .btn-signup {
            background: linear-gradient(135deg, #fd7e14, #ff9500);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-signup:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(253, 126, 20, 0.3);
            color: white;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 1rem;
            font-weight: 600;
            color: #6c757d;
        }
        
        .step.active {
            background: #fd7e14;
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .step-line {
            width: 60px;
            height: 2px;
            background: #e9ecef;
            margin-top: 19px;
        }
        
        .step-line.completed {
            background: #28a745;
        }

        .checkbox-grid .form-check {
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .checkbox-grid .form-check:hover {
            background: rgba(197, 23, 46, 0.05);
        }

        .checkbox-grid .form-check-input {
            margin-top: 0.25rem;
            border: 2px solid #dee2e6;
            border-radius: 4px;
        }

        .checkbox-grid .form-check-input:checked {
            background-color: #C5172E;
            border-color: #C5172E;
        }

        .checkbox-grid .form-check-input:focus {
            border-color: #C5172E;
            box-shadow: 0 0 0 0.2rem rgba(197, 23, 46, 0.25);
        }

        .checkbox-grid .form-check-label {
            font-weight: 500;
            color: #495057;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
        <a href="index.php" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <div class="navbar-nav ms-auto p-4 p-lg-0 d-flex align-items-center">
            <a href="index.php" class="nav-item nav-link">Home</a>
            <a href="login.php" class="btn btn-outline-primary ms-3" style="padding: 0.5rem 1.5rem; border-radius: 25px; font-weight: 600; border: 2px solid #C5172E; color: #C5172E;">Login</a>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Signup Container Start -->
    <div class="signup-container">
        <div class="container">
            <div class="signup-card">
                <!-- Step Indicator -->
                <div class="step-indicator mt-4">
                    <div class="step active" id="step1">1</div>
                    <div class="step-line" id="line1"></div>
                    <div class="step" id="step2">2</div>
                </div>

                <!-- User Type Selection -->
                <div class="user-type-selector" id="userTypeSection">
                    <h2 class="mb-4">Choose Your Account Type</h2>
                    <p class="text-muted mb-5">Select how you want to use Brick & Click</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="user-type-card" data-type="customer">
                                <i class="fas fa-user user-type-icon"></i>
                                <h4>I'm a Customer</h4>
                                <p class="mb-0">Looking for contractors for my construction project</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="user-type-card" data-type="contractor">
                                <i class="fas fa-hard-hat user-type-icon"></i>
                                <h4>I'm a Contractor</h4>
                                <p class="mb-0">Want to showcase my services and find new clients</p>
                            </div>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-signup mt-4" id="continueBtn" disabled>Continue</button>
                </div>

                <!-- Customer Registration Form -->
                <div class="form-section" id="customerForm">
                    <h3 class="mb-4"><i class="fas fa-user text-primary me-2"></i>Customer Registration</h3>
                    <form action="process_signup.php" method="POST">
                        <input type="hidden" name="user_type" value="customer">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">First Name *</label>
                                <input type="text" class="form-control" name="first_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Last Name *</label>
                                <input type="text" class="form-control" name="last_name" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email Address *</label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" name="phone">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">District *</label>
                                <select class="form-control" name="district" required>
                                    <option value="">Select District</option>
                                    <option value="Colombo">Colombo</option>
                                    <option value="Gampaha">Gampaha</option>
                                    <option value="Kalutara">Kalutara</option>
                                    <option value="Kandy">Kandy</option>
                                    <option value="Matale">Matale</option>
                                    <option value="Nuwara Eliya">Nuwara Eliya</option>
                                    <option value="Galle">Galle</option>
                                    <option value="Matara">Matara</option>
                                    <option value="Hambantota">Hambantota</option>
                                    <option value="Jaffna">Jaffna</option>
                                    <option value="Kilinochchi">Kilinochchi</option>
                                    <option value="Mannar">Mannar</option>
                                    <option value="Vavuniya">Vavuniya</option>
                                    <option value="Mullaitivu">Mullaitivu</option>
                                    <option value="Batticaloa">Batticaloa</option>
                                    <option value="Ampara">Ampara</option>
                                    <option value="Trincomalee">Trincomalee</option>
                                    <option value="Kurunegala">Kurunegala</option>
                                    <option value="Puttalam">Puttalam</option>
                                    <option value="Anuradhapura">Anuradhapura</option>
                                    <option value="Polonnaruwa">Polonnaruwa</option>
                                    <option value="Badulla">Badulla</option>
                                    <option value="Moneragala">Moneragala</option>
                                    <option value="Ratnapura">Ratnapura</option>
                                    <option value="Kegalle">Kegalle</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Preferred Language</label>
                                <select class="form-control" name="language_preference">
                                    <option value="en">English</option>
                                    <option value="si">Sinhala</option>
                                    <option value="ta">Tamil</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Address</label>
                            <textarea class="form-control" name="address" rows="3"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Password *</label>
                                <input type="password" class="form-control" name="password" required minlength="6">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Confirm Password *</label>
                                <input type="password" class="form-control" name="confirm_password" required minlength="6">
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="terms" required>
                                <label class="form-check-label">
                                    I agree to the <a href="#" class="text-primary">Terms of Service</a> and <a href="#" class="text-primary">Privacy Policy</a>
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-secondary" onclick="goBack()">Back</button>
                            <button type="submit" class="btn btn-signup">Create Account</button>
                        </div>
                    </form>
                </div>

                <!-- Contractor Registration Form -->
                <div class="form-section" id="contractorForm">
                    <h3 class="mb-4"><i class="fas fa-hard-hat text-primary me-2"></i>Contractor Registration</h3>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Your registration will be reviewed by our admin team. You'll receive an email once approved.
                    </div>
                    
                    <form action="process_signup.php" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="user_type" value="contractor">
                        
                        <h5 class="mb-3 text-primary">Business Information</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Business Name *</label>
                                <input type="text" class="form-control" name="business_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Contact Person *</label>
                                <input type="text" class="form-control" name="contact_person" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email Address *</label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" name="phone" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Business Address *</label>
                            <textarea class="form-control" name="business_address" rows="3" required></textarea>
                        </div>
                        
                        <h5 class="mb-3 text-primary">CIDA Registration</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">CIDA Registration Number *</label>
                                <input type="text" class="form-control" name="cida_registration" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">CIDA Grade *</label>
                                <select class="form-control" name="cida_grade" required>
                                    <option value="">Select CIDA Grade</option>
                                    <option value="C1">C1</option>
                                    <option value="C2">C2</option>
                                    <option value="C3">C3</option>
                                    <option value="C4">C4</option>
                                    <option value="C5">C5</option>
                                    <option value="C6">C6</option>
                                    <option value="C7">C7</option>
                                    <option value="C8">C8</option>
                                    <option value="C9">C9</option>
                                    <option value="C10">C10</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">CIDA Certificate *</label>
                                <input type="file" class="form-control" name="cida_document" accept=".pdf,.jpg,.jpeg,.png" required>
                                <small class="text-muted">Upload PDF, JPG, or PNG (Max 5MB)</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Business License</label>
                                <input type="file" class="form-control" name="license_document" accept=".pdf,.jpg,.jpeg,.png">
                                <small class="text-muted">Upload PDF, JPG, or PNG (Max 5MB)</small>
                            </div>
                        </div>
                        
                        <h5 class="mb-3 text-primary">Services & Areas</h5>
                        <div class="mb-3">
                            <label class="form-label">Service Types *</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_types[]" value="house_construction">
                                        <label class="form-check-label">House Construction</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_types[]" value="building_renovation">
                                        <label class="form-check-label">Building Renovation</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_types[]" value="commercial_construction">
                                        <label class="form-check-label">Commercial Construction</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_types[]" value="interior_design_finishing">
                                        <label class="form-check-label">Interior Design & Finishing</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_types[]" value="roofing_waterproofing">
                                        <label class="form-check-label">Roofing & Waterproofing</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_types[]" value="electrical_work">
                                        <label class="form-check-label">Electrical Work</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_types[]" value="plumbing_sanitation">
                                        <label class="form-check-label">Plumbing & Sanitation</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_types[]" value="landscaping_gardening">
                                        <label class="form-check-label">Landscaping & Gardening</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_types[]" value="swimming_pool_construction">
                                        <label class="form-check-label">Swimming Pool Construction</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="service_types[]" value="road_infrastructure">
                                        <label class="form-check-label">Road & Infrastructure</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="form-label">Service Areas *</label>
                            <p class="text-muted mb-3">Select the districts where you provide services:</p>
                            <div class="checkbox-grid">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Colombo" id="area_colombo">
                                            <label class="form-check-label" for="area_colombo">Colombo</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Gampaha" id="area_gampaha">
                                            <label class="form-check-label" for="area_gampaha">Gampaha</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Kalutara" id="area_kalutara">
                                            <label class="form-check-label" for="area_kalutara">Kalutara</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Kandy" id="area_kandy">
                                            <label class="form-check-label" for="area_kandy">Kandy</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Matale" id="area_matale">
                                            <label class="form-check-label" for="area_matale">Matale</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Nuwara Eliya" id="area_nuwara_eliya">
                                            <label class="form-check-label" for="area_nuwara_eliya">Nuwara Eliya</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Galle" id="area_galle">
                                            <label class="form-check-label" for="area_galle">Galle</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Matara" id="area_matara">
                                            <label class="form-check-label" for="area_matara">Matara</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Hambantota" id="area_hambantota">
                                            <label class="form-check-label" for="area_hambantota">Hambantota</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Jaffna" id="area_jaffna">
                                            <label class="form-check-label" for="area_jaffna">Jaffna</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Kilinochchi" id="area_kilinochchi">
                                            <label class="form-check-label" for="area_kilinochchi">Kilinochchi</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Mannar" id="area_mannar">
                                            <label class="form-check-label" for="area_mannar">Mannar</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Vavuniya" id="area_vavuniya">
                                            <label class="form-check-label" for="area_vavuniya">Vavuniya</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Mullaitivu" id="area_mullaitivu">
                                            <label class="form-check-label" for="area_mullaitivu">Mullaitivu</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Batticaloa" id="area_batticaloa">
                                            <label class="form-check-label" for="area_batticaloa">Batticaloa</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Ampara" id="area_ampara">
                                            <label class="form-check-label" for="area_ampara">Ampara</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Trincomalee" id="area_trincomalee">
                                            <label class="form-check-label" for="area_trincomalee">Trincomalee</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Kurunegala" id="area_kurunegala">
                                            <label class="form-check-label" for="area_kurunegala">Kurunegala</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Puttalam" id="area_puttalam">
                                            <label class="form-check-label" for="area_puttalam">Puttalam</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Anuradhapura" id="area_anuradhapura">
                                            <label class="form-check-label" for="area_anuradhapura">Anuradhapura</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Polonnaruwa" id="area_polonnaruwa">
                                            <label class="form-check-label" for="area_polonnaruwa">Polonnaruwa</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Badulla" id="area_badulla">
                                            <label class="form-check-label" for="area_badulla">Badulla</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Moneragala" id="area_moneragala">
                                            <label class="form-check-label" for="area_moneragala">Moneragala</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Ratnapura" id="area_ratnapura">
                                            <label class="form-check-label" for="area_ratnapura">Ratnapura</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="service_areas[]" value="Kegalle" id="area_kegalle">
                                            <label class="form-check-label" for="area_kegalle">Kegalle</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Business Description</label>
                            <textarea class="form-control" name="business_description" rows="4" placeholder="Tell us about your business, experience, and specializations..."></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Website (Optional)</label>
                                <input type="url" class="form-control" name="website">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Preferred Language</label>
                                <select class="form-control" name="language_preference">
                                    <option value="en">English</option>
                                    <option value="si">Sinhala</option>
                                    <option value="ta">Tamil</option>
                                </select>
                            </div>
                        </div>
                        
                        <h5 class="mb-3 text-primary">Account Security</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Password *</label>
                                <input type="password" class="form-control" name="password" required minlength="6">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Confirm Password *</label>
                                <input type="password" class="form-control" name="confirm_password" required minlength="6">
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="terms" required>
                                <label class="form-check-label">
                                    I agree to the <a href="#" class="text-primary">Terms of Service</a> and <a href="#" class="text-primary">Privacy Policy</a>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="verification_consent" required>
                                <label class="form-check-label">
                                    I consent to CIDA verification and understand my account will be reviewed before approval
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-secondary" onclick="goBack()">Back</button>
                            <button type="submit" class="btn btn-signup">Submit for Review</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Signup Container End -->

    <script>
        let selectedUserType = null;

        // User type selection
        document.querySelectorAll('.user-type-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove active class from all cards
                document.querySelectorAll('.user-type-card').forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked card
                this.classList.add('active');
                
                // Store selected type
                selectedUserType = this.dataset.type;
                
                // Enable continue button
                document.getElementById('continueBtn').disabled = false;
            });
        });

        // Continue button
        document.getElementById('continueBtn').addEventListener('click', function() {
            if (selectedUserType) {
                // Update step indicator
                document.getElementById('step1').classList.remove('active');
                document.getElementById('step1').classList.add('completed');
                document.getElementById('line1').classList.add('completed');
                document.getElementById('step2').classList.add('active');
                
                // Hide user type selection
                document.getElementById('userTypeSection').style.display = 'none';
                
                // Show appropriate form
                if (selectedUserType === 'customer') {
                    document.getElementById('customerForm').classList.add('active');
                } else {
                    document.getElementById('contractorForm').classList.add('active');
                }
            }
        });

        // Go back function
        function goBack() {
            // Reset step indicator
            document.getElementById('step1').classList.add('active');
            document.getElementById('step1').classList.remove('completed');
            document.getElementById('line1').classList.remove('completed');
            document.getElementById('step2').classList.remove('active');
            
            // Show user type selection
            document.getElementById('userTypeSection').style.display = 'block';
            
            // Hide forms
            document.getElementById('customerForm').classList.remove('active');
            document.getElementById('contractorForm').classList.remove('active');
        }

        // Check URL parameters for pre-selection
        const urlParams = new URLSearchParams(window.location.search);
        const typeParam = urlParams.get('type');
        if (typeParam === 'contractor') {
            document.querySelector('[data-type="contractor"]').click();
        }

        // Password confirmation validation
        function validatePasswords(form) {
            const password = form.querySelector('input[name="password"]').value;
            const confirmPassword = form.querySelector('input[name="confirm_password"]').value;
            
            if (password !== confirmPassword) {
                alert('Passwords do not match!');
                return false;
            }
            return true;
        }

        // Form submission validation
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!validatePasswords(this)) {
                    e.preventDefault();
                }
            });
        });
    </script>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
