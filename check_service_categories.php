<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔍 Service Categories Check</h2>";

// Get all service categories
$stmt = $pdo->query("SELECT * FROM service_categories ORDER BY id");
$categories = $stmt->fetchAll();

echo "<h3>📋 All Service Categories:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Name (EN)</th><th>Name (SI)</th><th>Name (TA)</th><th>Active</th></tr>";

foreach ($categories as $category) {
    echo "<tr>";
    echo "<td>{$category['id']}</td>";
    echo "<td>{$category['name_en']}</td>";
    echo "<td>{$category['name_si']}</td>";
    echo "<td>{$category['name_ta']}</td>";
    echo "<td>" . ($category['is_active'] ? 'Yes' : 'No') . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check what plumbing-related categories exist
echo "<h3>🔧 Plumbing-Related Categories:</h3>";
$stmt = $pdo->prepare("SELECT * FROM service_categories WHERE name_en LIKE '%plumb%' OR name_en LIKE '%sanit%'");
$stmt->execute();
$plumbing_categories = $stmt->fetchAll();

if (count($plumbing_categories) > 0) {
    foreach ($plumbing_categories as $category) {
        echo "<p><strong>ID {$category['id']}:</strong> {$category['name_en']}</p>";
    }
} else {
    echo "<p>❌ No plumbing-related categories found!</p>";
}

// Check the latest quote request details
echo "<h3>📋 Latest Quote Request Service:</h3>";
$stmt = $pdo->query("
    SELECT qr.service_category_id, sc.name_en, qr.title, qr.district
    FROM quote_requests qr
    LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
    ORDER BY qr.created_at DESC
    LIMIT 1
");
$latest = $stmt->fetch();

if ($latest) {
    echo "<p><strong>Service Category ID:</strong> {$latest['service_category_id']}</p>";
    echo "<p><strong>Service Name:</strong> {$latest['name_en']}</p>";
    echo "<p><strong>Quote Title:</strong> {$latest['title']}</p>";
    echo "<p><strong>District:</strong> {$latest['district']}</p>";
} else {
    echo "<p>❌ No quote requests found</p>";
}

// Check contractors with plumbing services
echo "<h3>👷 Contractors with Plumbing Services:</h3>";
$stmt = $pdo->query("
    SELECT u.id, u.email, cp.business_name, cp.service_types, cp.service_areas
    FROM users u
    JOIN contractor_profiles cp ON u.id = cp.user_id
    WHERE u.status = 'approved'
    AND u.user_type = 'contractor'
");
$contractors = $stmt->fetchAll();

foreach ($contractors as $contractor) {
    $services = json_decode($contractor['service_types'], true) ?: [];
    $areas = json_decode($contractor['service_areas'], true) ?: [];
    
    // Check if they have plumbing service (assuming it's ID 6 based on common setup)
    $has_plumbing = false;
    foreach ($plumbing_categories as $plumbing_cat) {
        if (in_array($plumbing_cat['id'], $services) || in_array((string)$plumbing_cat['id'], $services)) {
            $has_plumbing = true;
            break;
        }
    }
    
    if ($has_plumbing || $contractor['email'] === '<EMAIL>') {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo "<h4>{$contractor['business_name']} ({$contractor['email']})</h4>";
        echo "<p><strong>Services:</strong> " . implode(', ', $services) . "</p>";
        echo "<p><strong>Areas:</strong> " . implode(', ', $areas) . "</p>";
        echo "<p><strong>Has Plumbing:</strong> " . ($has_plumbing ? '✅ YES' : '❌ NO') . "</p>";
        echo "</div>";
    }
}
?>
