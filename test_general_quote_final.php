<?php
session_start();
require_once 'config/database.php';

echo "<h2>🧪 Final General Quote Test</h2>";

try {
    // Step 1: Check contractors
    $stmt = $pdo->query("
        SELECT u.id, cp.business_name, cp.service_areas, cp.service_types
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        AND cp.service_areas IS NOT NULL AND cp.service_areas != '' AND cp.service_areas != '[]'
        AND cp.service_types IS NOT NULL AND cp.service_types != '' AND cp.service_types != '[]'
    ");
    $contractors = $stmt->fetchAll();
    
    echo "<h3>Available Contractors (" . count($contractors) . ")</h3>";
    if (count($contractors) == 0) {
        echo "<p style='color: red;'>❌ No contractors found. <a href='fix_contractor_data.php'>Fix contractor data first</a></p>";
        exit;
    }
    
    foreach ($contractors as $contractor) {
        $areas = json_decode($contractor['service_areas'], true);
        $services = json_decode($contractor['service_types'], true);
        echo "<p><strong>" . htmlspecialchars($contractor['business_name']) . "</strong><br>";
        echo "Areas: " . implode(', ', $areas) . " | Services: " . implode(', ', $services) . "</p>";
    }
    
    // Step 2: Get customer
    $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' AND status = 'approved' LIMIT 1");
    $customer_id = $stmt->fetchColumn();
    
    if (!$customer_id) {
        echo "<p style='color: red;'>❌ No customer found. Creating test customer...</p>";
        $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, 'customer', 'approved')");
        $stmt->execute(['<EMAIL>', password_hash('password', PASSWORD_DEFAULT)]);
        $customer_id = $pdo->lastInsertId();
        
        $stmt = $pdo->prepare("INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$customer_id, 'Test', 'Customer', '0771234567', 'Colombo']);
        echo "<p style='color: green;'>✅ Created test customer</p>";
    }
    
    // Step 3: Test quote submission
    echo "<h3>Testing Quote Submission</h3>";
    
    $test_district = 'Colombo';
    $test_service_id = 1;
    
    echo "<p>Testing: District = <strong>$test_district</strong>, Service = <strong>$test_service_id</strong></p>";
    
    $_SESSION['user_id'] = $customer_id;
    $_SESSION['user_type'] = 'customer';
    
    $pdo->beginTransaction();
    
    // Create quote request
    $stmt = $pdo->prepare("
        INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, specific_contractor_id, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'open')
    ");
    
    $title = 'Final Test Quote - ' . date('H:i:s');
    $stmt->execute([
        $customer_id, 
        $test_service_id, 
        $title, 
        'Test description', 
        'Test Location', 
        $test_district, 
        1000000, 
        '3 months', 
        null
    ]);
    
    $quote_request_id = $pdo->lastInsertId();
    echo "<p style='color: green;'>✅ Quote created (ID: $quote_request_id)</p>";
    
    // Find matching contractors
    $contractors_to_notify = [];
    foreach ($contractors as $contractor) {
        $service_areas = json_decode($contractor['service_areas'], true);
        $service_types = json_decode($contractor['service_types'], true);

        $has_area = is_array($service_areas) && in_array($test_district, $service_areas);
        $has_service = is_array($service_types) && (
            in_array($test_service_id, $service_types) ||
            in_array((string)$test_service_id, $service_types)
        );

        if ($has_area && $has_service) {
            $contractors_to_notify[] = $contractor;
            echo "<p>✅ Match: " . htmlspecialchars($contractor['business_name']) . "</p>";
        } else {
            echo "<p>❌ No match: " . htmlspecialchars($contractor['business_name']) . " (Area: " . ($has_area ? "✓" : "✗") . ", Service: " . ($has_service ? "✓" : "✗") . ")</p>";
        }
    }
    
    // Create notifications
    foreach ($contractors_to_notify as $contractor) {
        $stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, title, message, type, related_id) 
            VALUES (?, ?, ?, 'quote_received', ?)
        ");
        $stmt->execute([
            $contractor['id'], 
            'New Quote Request', 
            "You have received a new quote request for: $title", 
            $quote_request_id
        ]);
    }
    
    $pdo->commit();
    
    echo "<h3>Results</h3>";
    echo "<p style='color: green; font-size: 18px;'><strong>✅ " . count($contractors_to_notify) . " contractors notified!</strong></p>";
    
    if (count($contractors_to_notify) > 0) {
        echo "<h4>Next Steps:</h4>";
        echo "<ul>";
        echo "<li><a href='customer/request_quote.php?debug=1' target='_blank'>Test real quote submission (with debug)</a></li>";
        echo "<li><a href='contractor/quotes.php' target='_blank'>Check contractor quotes page</a></li>";
        echo "<li><a href='debug_general_quotes.php' target='_blank'>View debug information</a></li>";
        echo "</ul>";
        
        echo "<h4>Contractor Login Info:</h4>";
        echo "<p>Email: <EMAIL> | Password: password</p>";
    }
    
} catch (PDOException $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
