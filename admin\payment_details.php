<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    http_response_code(403);
    echo 'Access denied';
    exit();
}

$payment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$payment_id) {
    echo '<div class="alert alert-danger">Invalid payment ID</div>';
    exit();
}

try {
    // Get detailed payment information
    $stmt = $pdo->prepare("
        SELECT pp.*,
               qr.title as project_title,
               qr.description as project_description,
               qr.estimated_budget,
               qr.location,
               qr.district,
               qr.created_at as request_date,
               cp.business_name,
               cp.contact_person,
               cp.phone as contractor_phone,
               cust.first_name as customer_first_name,
               cust.last_name as customer_last_name,
               cust.phone as customer_phone,
               qres.quoted_amount,
               qres.estimated_timeline,
               qres.created_at as quote_date,
               u_contractor.email as contractor_email,
               u_customer.email as customer_email,
               sc.name_en as service_category
        FROM project_payments pp
        LEFT JOIN quote_responses qres ON pp.quote_response_id = qres.id
        LEFT JOIN quote_requests qr ON qres.quote_request_id = qr.id
        LEFT JOIN contractor_profiles cp ON pp.contractor_id = cp.user_id
        LEFT JOIN customer_profiles cust ON pp.customer_id = cust.user_id
        LEFT JOIN users u_contractor ON pp.contractor_id = u_contractor.id
        LEFT JOIN users u_customer ON pp.customer_id = u_customer.id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE pp.id = ?
    ");
    $stmt->execute([$payment_id]);
    $payment = $stmt->fetch();
    
    if (!$payment) {
        echo '<div class="alert alert-danger">Payment not found</div>';
        exit();
    }
    
    // Parse payment details JSON if exists
    $payment_details = [];
    if (!empty($payment['payment_details'])) {
        $payment_details = json_decode($payment['payment_details'], true) ?? [];
    }
    
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">Database error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    exit();
}
?>

<div class="payment-details">
    <!-- Payment Summary -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless mb-0">
                        <tr>
                            <td><strong>Payment ID:</strong></td>
                            <td>#<?php echo $payment['id']; ?></td>
                        </tr>
                        <tr>
                            <td><strong>Amount:</strong></td>
                            <td><span class="text-success fw-bold">Rs. <?php echo number_format($payment['amount']); ?></span></td>
                        </tr>
                        <tr>
                            <td><strong>Payment Type:</strong></td>
                            <td><span class="badge bg-info"><?php echo ucfirst(str_replace('_', ' ', $payment['payment_type'])); ?></span></td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                <span class="badge bg-<?php 
                                    echo $payment['payment_status'] === 'completed' ? 'success' : 
                                        ($payment['payment_status'] === 'pending' ? 'warning' : 
                                        ($payment['payment_status'] === 'failed' ? 'danger' : 'secondary')); 
                                ?>">
                                    <?php echo ucfirst($payment['payment_status']); ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Payment Date:</strong></td>
                            <td>
                                <?php if (!empty($payment['payment_date'])): ?>
                                    <?php echo date('F j, Y g:i A', strtotime($payment['payment_date'])); ?>
                                <?php else: ?>
                                    <span class="text-muted">Not paid yet</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php if (!empty($payment['transaction_id'])): ?>
                        <tr>
                            <td><strong>Transaction ID:</strong></td>
                            <td><code><?php echo htmlspecialchars($payment['transaction_id']); ?></code></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-project-diagram me-2"></i>Project Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless mb-0">
                        <tr>
                            <td><strong>Project:</strong></td>
                            <td><?php echo htmlspecialchars($payment['project_title'] ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Service:</strong></td>
                            <td><?php echo htmlspecialchars($payment['service_category'] ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Location:</strong></td>
                            <td><?php echo htmlspecialchars($payment['location'] ?? ''); ?>, <?php echo htmlspecialchars($payment['district'] ?? ''); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Budget:</strong></td>
                            <td>Rs. <?php echo number_format($payment['estimated_budget'] ?? 0); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Quoted Amount:</strong></td>
                            <td>Rs. <?php echo number_format($payment['quoted_amount'] ?? 0); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Timeline:</strong></td>
                            <td><?php echo htmlspecialchars($payment['estimated_timeline'] ?? 'N/A'); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Customer and Contractor Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Customer Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless mb-0">
                        <tr>
                            <td><strong>Name:</strong></td>
                            <td><?php echo htmlspecialchars(($payment['customer_first_name'] ?? 'Unknown') . ' ' . ($payment['customer_last_name'] ?? 'Customer')); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Email:</strong></td>
                            <td><?php echo htmlspecialchars($payment['customer_email'] ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Phone:</strong></td>
                            <td><?php echo htmlspecialchars($payment['customer_phone'] ?? 'N/A'); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-hard-hat me-2"></i>Contractor Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless mb-0" style="table-layout: fixed; word-wrap: break-word;">
                        <tr>
                            <td style="width: 40%; vertical-align: top;"><strong>Business:</strong></td>
                            <td style="width: 60%; word-break: break-word;"><?php echo htmlspecialchars($payment['business_name'] ?? 'Unknown Business'); ?></td>
                        </tr>
                        <tr>
                            <td style="width: 40%; vertical-align: top;"><strong>Contact Person:</strong></td>
                            <td style="width: 60%; word-break: break-word;"><?php echo htmlspecialchars($payment['contact_person'] ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <td style="width: 40%; vertical-align: top;"><strong>Email:</strong></td>
                            <td style="width: 60%; word-break: break-word;"><?php echo htmlspecialchars($payment['contractor_email'] ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <td style="width: 40%; vertical-align: top;"><strong>Phone:</strong></td>
                            <td style="width: 60%; word-break: break-word;"><?php echo htmlspecialchars($payment['contractor_phone'] ?? 'N/A'); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Project Description -->
    <?php if (!empty($payment['project_description'])): ?>
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-file-alt me-2"></i>Project Description</h6>
        </div>
        <div class="card-body">
            <p class="mb-0"><?php echo nl2br(htmlspecialchars($payment['project_description'])); ?></p>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Payment Details -->
    <?php if (!empty($payment_details)): ?>
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Payment Details</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <?php foreach ($payment_details as $key => $value): ?>
                    <?php if (!empty($value)): ?>
                    <div class="col-md-6 mb-2">
                        <strong><?php echo ucfirst(str_replace('_', ' ', $key)); ?>:</strong>
                        <?php echo htmlspecialchars($value); ?>
                    </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Timeline -->
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Timeline</h6>
        </div>
        <div class="card-body">
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-marker bg-primary"></div>
                    <div class="timeline-content">
                        <h6>Quote Request Created</h6>
                        <p class="text-muted mb-0"><?php echo date('F j, Y g:i A', strtotime($payment['request_date'])); ?></p>
                    </div>
                </div>
                
                <?php if (!empty($payment['quote_date'])): ?>
                <div class="timeline-item">
                    <div class="timeline-marker bg-info"></div>
                    <div class="timeline-content">
                        <h6>Quote Submitted</h6>
                        <p class="text-muted mb-0"><?php echo date('F j, Y g:i A', strtotime($payment['quote_date'])); ?></p>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="timeline-item">
                    <div class="timeline-marker bg-warning"></div>
                    <div class="timeline-content">
                        <h6>Payment Record Created</h6>
                        <p class="text-muted mb-0"><?php echo date('F j, Y g:i A', strtotime($payment['created_at'])); ?></p>
                    </div>
                </div>
                
                <?php if (!empty($payment['payment_date'])): ?>
                <div class="timeline-item">
                    <div class="timeline-marker bg-success"></div>
                    <div class="timeline-content">
                        <h6>Payment Completed</h6>
                        <p class="text-muted mb-0"><?php echo date('F j, Y g:i A', strtotime($payment['payment_date'])); ?></p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-weight: 600;
}
</style>
