<?php
// Include authentication and authorization check
require_once 'includes/auth_check.php';

// Contractor data is now available in $contractor variable

// Filter parameters - default to 'pending' to match dashboard behavior
$status_filter = $_GET['status'] ?? 'pending';
$search = $_GET['search'] ?? '';

// Build query conditions - Show quotes for this contractor only
// 1. Direct quotes sent specifically to this contractor
// 2. General quotes (no specific contractor) that match their services and areas
$where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
$params = [$_SESSION['user_id']];

if ($status_filter !== 'all') {
    if ($status_filter === 'responded') {
        $where_conditions[] = "qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?)";
        $params[] = $_SESSION['user_id'];
    } elseif ($status_filter === 'pending') {
        $where_conditions[] = "qr.status = 'open' AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)";
        $params[] = $_SESSION['user_id'];
    } elseif ($status_filter === 'completed') {
        // Show quotes where customer has made downpayment
        $where_conditions[] = "qr.id IN (
            SELECT qres.quote_request_id
            FROM quote_responses qres
            JOIN project_payments pp ON qres.id = pp.quote_response_id
            WHERE qres.contractor_id = ?
            AND pp.payment_type = 'down_payment'
            AND pp.payment_status = 'completed'
        )";
        $params[] = $_SESSION['user_id'];
    } elseif ($status_filter === 'cancelled') {
        // Show quotes where contractor's response was rejected by customer
        $where_conditions[] = "qr.id IN (
            SELECT quote_request_id
            FROM quote_responses
            WHERE contractor_id = ?
            AND status = 'rejected'
        )";
        $params[] = $_SESSION['user_id'];
    }
} else {
    // Show all quotes including accepted ones when "all" is selected
    $where_conditions[] = "(qr.status IN ('open', 'completed', 'cancelled') OR qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?))";
    $params[] = $_SESSION['user_id'];
}

if (!empty($search)) {
    $where_conditions[] = "(qr.title LIKE ? OR qr.description LIKE ? OR cp.first_name LIKE ? OR cp.last_name LIKE ? OR sc.name_en LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

$where_clause = implode(' AND ', $where_conditions);

// Get quote requests - show quotes for this contractor only
try {
    // First ensure specific_contractor_id column exists
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
        $column_exists = $stmt->fetch();

        if (!$column_exists) {
            $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        }
    } catch (PDOException $e) {
        // Column might already exist, continue
    }

    // Contractor data is already available from auth_check.php as $contractor
    $contractor_data = $contractor;

    // Get all quotes that could be relevant to this contractor
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, u.email, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               (SELECT estimated_timeline FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_timeline,
               (SELECT description FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_description,
               CASE WHEN qr.specific_contractor_id = ? THEN 'direct' ELSE 'general' END as quote_type
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        JOIN users u ON qr.customer_id = u.id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");

    // Add contractor ID parameters for the subqueries
    $query_params = array_merge([$_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id']], $params);
    $stmt->execute($query_params);
    $all_quotes_raw = $stmt->fetchAll();

    // Filter quotes in PHP using the same logic as dashboard
    $all_quotes = [];
    foreach ($all_quotes_raw as $quote) {
        // Always include direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $_SESSION['user_id']) {
            $all_quotes[] = $quote;
            continue;
        }

        // For general quotes (no specific contractor), check service and area match
        if ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($contractor_data['service_types'], true) ?: [];
            $contractor_areas = json_decode($contractor_data['service_areas'], true) ?: [];

            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);

            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);

            if ($has_service && $has_area) {
                $all_quotes[] = $quote;
            }
        }
    }

    // Quotes are already filtered above, so just assign them
    $quotes = $all_quotes;
} catch (PDOException $e) {
    $quotes = [];
    $error = 'Database error: ' . $e->getMessage();
    // Log the error for debugging
    error_log("Quotes page error: " . $e->getMessage());
}

// Get quote counts for tabs - use same logic as dashboard
try {
    // Get all quotes for this contractor
    $stmt = $pdo->prepare("
        SELECT qr.id, qr.status, qr.specific_contractor_id, qr.service_category_id, qr.district,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT COUNT(*) FROM quote_responses qres
                JOIN project_payments pp ON qres.id = pp.quote_response_id
                WHERE qres.quote_request_id = qr.id AND qres.contractor_id = ?
                AND pp.payment_type = 'down_payment' AND pp.payment_status = 'completed') as has_payment,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? AND status = 'rejected') as is_rejected
        FROM quote_requests qr
        WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND (qr.status IN ('open', 'completed', 'cancelled') OR qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?))
    ");
    $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id'], $_SESSION['user_id']]);
    $all_count_quotes = $stmt->fetchAll();

    // Filter and count in PHP
    $total = 0;
    $pending = 0;
    $responded = 0;
    $completed = 0;
    $cancelled = 0;

    foreach ($all_count_quotes as $quote) {
        $should_include = false;

        // Always include direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $_SESSION['user_id']) {
            $should_include = true;
        }
        // For general quotes, check service and area match
        elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($contractor_data['service_types'], true) ?: [];
            $contractor_areas = json_decode($contractor_data['service_areas'], true) ?: [];

            // Check if contractor provides this service (handle both int and string formats)
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);

            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);

            $should_include = ($has_service && $has_area);
        }

        if ($should_include) {
            $total++;

            if ($quote['status'] === 'open' && $quote['has_responded'] == 0) {
                $pending++;
            }
            if ($quote['has_responded'] > 0) {
                $responded++;
            }
            if ($quote['has_payment'] > 0) {
                $completed++;
            }
            if ($quote['is_rejected'] > 0) {
                $cancelled++;
            }
        }
    }

    $counts = [
        'total' => $total,
        'pending' => $pending,
        'responded' => $responded,
        'completed' => $completed,
        'cancelled' => $cancelled
    ];
} catch (PDOException $e) {
    $counts = ['total' => 0, 'pending' => 0, 'responded' => 0, 'completed' => 0, 'cancelled' => 0];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Quote Requests - BrickClick Contractor</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet"> 
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF6B35;
            --accent-yellow: #F7931E;
            --success-green: #28a745;
            --info-blue: #17a2b8;
            --warning-orange: #ffc107;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --dark-gray: #343a40;
        }
        
        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e9ecef 100%);
            font-family: 'Open Sans', sans-serif;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            z-index: 1000;
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            flex-shrink: 0;
        }

        .sidebar-header h3 {
            color: var(--accent-orange);
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }

        .sidebar-header p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }

        .sidebar-nav {
            flex: 1;
            padding: 0.5rem 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .nav-item {
            margin: 0.15rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,107,53,0.1);
            color: var(--accent-orange);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--accent-orange), var(--primary-red));
            color: white;
            box-shadow: 0 3px 10px rgba(255,107,53,0.3);
        }

        .nav-link i {
            margin-right: 0.8rem;
            width: 18px;
            text-align: center;
            font-size: 1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }

        .page-header {
            background: linear-gradient(135deg, var(--info-blue), #138496);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .page-title {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 2rem;
        }

        .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }

        .filters-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .status-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .status-tab {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .status-tab.all {
            background: linear-gradient(135deg, var(--medium-gray), #5a6268);
            color: white;
        }

        .status-tab.pending {
            background: linear-gradient(135deg, var(--warning-orange), #e0a800);
            color: white;
        }

        .status-tab.responded {
            background: linear-gradient(135deg, var(--info-blue), #138496);
            color: white;
        }

        .status-tab.completed {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            color: white;
        }

        .status-tab.cancelled {
            background: linear-gradient(135deg, var(--primary-red), #bd2130);
            color: white;
        }

        .status-tab.active {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .search-box {
            position: relative;
        }

        .search-input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem 0.75rem 3rem;
            width: 100%;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: var(--accent-orange);
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--medium-gray);
        }

        .quote-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .quote-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .quote-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 1.5rem;
        }

        .quote-title {
            color: var(--primary-dark);
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-size: 1.3rem;
        }

        .quote-customer {
            color: var(--medium-gray);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .quote-budget {
            text-align: right;
        }

        .budget-amount {
            color: var(--accent-orange);
            font-weight: 700;
            font-size: 1.2rem;
        }

        .quote-date {
            color: var(--medium-gray);
            font-size: 0.9rem;
        }

        .quote-description {
            color: var(--dark-gray);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .quote-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: var(--light-gray);
            border-radius: 10px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .detail-item i {
            color: var(--accent-orange);
            width: 16px;
        }

        .quote-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn-respond {
            background: linear-gradient(135deg, var(--accent-orange), #e55a2b);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-respond:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }

        .btn-view {
            background: linear-gradient(135deg, var(--info-blue), #138496);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-view:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .status-badge.responded {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            color: #0c5460;
        }

        .status-badge.accepted {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }

        .status-badge.rejected {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }

        .status-badge.pending {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
        }

        .my-quote-info {
            background: linear-gradient(135deg, #e2f3ff, #cce7ff);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--medium-gray);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>BrickClick</h3>
            <p>Contractor Portal</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link active">
                    <i class="fas fa-file-invoice-dollar"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="portfolio.php" class="nav-link">
                    <i class="fas fa-images"></i>Portfolio
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews & Ratings
                </a>
            </div>
            <div class="nav-item">
                <a href="profile.php" class="nav-link">
                    <i class="fas fa-user-edit"></i>Profile & Services
                </a>
            </div>
            <div class="nav-item">
                <a href="payment_history.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payment History
                </a>
            </div>
            <div class="nav-item">
                <a href="notifications.php" class="nav-link">
                    <i class="fas fa-bell"></i>Notifications
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Quote Requests</h1>
            <p class="page-subtitle">Manage and respond to customer quote requests</p>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <div class="status-tabs">
                <a href="?status=all&search=<?php echo urlencode($search); ?>"
                   class="status-tab all <?php echo $status_filter === 'all' ? 'active' : ''; ?>">
                    All Quotes (<?php echo $counts['total']; ?>)
                </a>
                <a href="?status=pending&search=<?php echo urlencode($search); ?>"
                   class="status-tab pending <?php echo $status_filter === 'pending' ? 'active' : ''; ?>">
                    Pending (<?php echo $counts['pending']; ?>)
                </a>
                <a href="?status=responded&search=<?php echo urlencode($search); ?>"
                   class="status-tab responded <?php echo $status_filter === 'responded' ? 'active' : ''; ?>">
                    Responded (<?php echo $counts['responded']; ?>)
                </a>
                <a href="?status=completed&search=<?php echo urlencode($search); ?>"
                   class="status-tab completed <?php echo $status_filter === 'completed' ? 'active' : ''; ?>">
                    Completed (<?php echo $counts['completed']; ?>)
                </a>
                <a href="?status=cancelled&search=<?php echo urlencode($search); ?>"
                   class="status-tab cancelled <?php echo $status_filter === 'cancelled' ? 'active' : ''; ?>">
                    Cancelled (<?php echo $counts['cancelled']; ?>)
                </a>
            </div>

            <div class="search-box">
                <i class="fas fa-search search-icon"></i>
                <form method="GET" action="">
                    <input type="hidden" name="status" value="<?php echo htmlspecialchars($status_filter); ?>">
                    <input type="text" class="search-input" name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="Search by service, description, or customer name...">
                </form>
            </div>
        </div>

        <!-- Quote Requests List -->
        <?php if (empty($quotes)): ?>
            <div class="empty-state">
                <i class="fas fa-file-invoice-dollar"></i>
                <h3>No quote requests found</h3>
                <p>No quote requests match your current filters. New requests will appear here when customers request quotes for your services.</p>
            </div>
        <?php else: ?>
            <?php foreach ($quotes as $quote): ?>
                <div class="quote-card">
                    <div class="quote-header">
                        <div>
                            <h4 class="quote-title">
                                <?php echo htmlspecialchars($quote['service_category'] ?? 'Quote Request'); ?>
                                <?php if ($quote['quote_type'] === 'direct'): ?>
                                    <span class="badge bg-primary ms-2">
                                        <i class="fas fa-star me-1"></i>Direct Request
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-info ms-2">
                                        <i class="fas fa-broadcast-tower me-1"></i>General Request
                                    </span>
                                <?php endif; ?>
                            </h4>
                            <p class="quote-customer">
                                <span>
                                    <i class="fas fa-user me-1"></i>
                                    <?php echo htmlspecialchars($quote['first_name'] . ' ' . $quote['last_name']); ?>
                                </span>
                                <span>
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo htmlspecialchars($quote['district']); ?>
                                </span>
                                <span>
                                    <i class="fas fa-phone me-1"></i>
                                    <?php echo htmlspecialchars($quote['phone']); ?>
                                </span>
                                <span>
                                    <i class="fas fa-envelope me-1"></i>
                                    <?php echo htmlspecialchars($quote['email']); ?>
                                </span>
                            </p>
                        </div>
                        <div class="quote-budget">
                            <div class="budget-amount">Rs. <?php echo number_format($quote['estimated_budget']); ?></div>
                            <div class="quote-date"><?php echo date('M j, Y', strtotime($quote['created_at'])); ?></div>
                        </div>
                    </div>

                    <?php if ($quote['has_responded'] > 0): ?>
                        <div class="my-quote-info">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong>Your Quote: Rs. <?php echo number_format($quote['my_quote_amount']); ?></strong>
                                </div>
                                <span class="status-badge <?php echo $quote['my_quote_status']; ?>">
                                    <?php echo ucfirst($quote['my_quote_status']); ?>
                                </span>
                            </div>
                            <?php if ($quote['my_quote_timeline']): ?>
                                <div class="mb-1">
                                    <small><strong>Timeline:</strong> <?php echo htmlspecialchars($quote['my_quote_timeline']); ?></small>
                                </div>
                            <?php endif; ?>
                            <?php if ($quote['my_quote_description']): ?>
                                <div>
                                    <small><strong>Description:</strong> <?php echo htmlspecialchars(substr($quote['my_quote_description'], 0, 100)) . (strlen($quote['my_quote_description']) > 100 ? '...' : ''); ?></small>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <p class="quote-description">
                        <?php echo htmlspecialchars($quote['description']); ?>
                    </p>

                    <div class="quote-details">
                        <div class="detail-item">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Timeline: <?php echo htmlspecialchars($quote['project_timeline']); ?></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Location: <?php echo htmlspecialchars($quote['location']); ?></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>Posted: <?php echo date('M j, Y g:i A', strtotime($quote['created_at'])); ?></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-info-circle"></i>
                            <span>Status: <?php echo ucfirst($quote['status']); ?></span>
                        </div>
                    </div>

                    <div class="quote-actions">
                        <?php if ($quote['has_responded'] == 0 && $quote['status'] === 'open'): ?>
                            <a href="respond_quote.php?id=<?php echo $quote['id']; ?>" class="btn-respond">
                                <i class="fas fa-reply me-2"></i>Respond to Quote
                            </a>
                        <?php else: ?>
                            <a href="view_quote.php?id=<?php echo $quote['id']; ?>" class="btn-view">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto-submit search form on input
        document.querySelector('.search-input').addEventListener('input', function() {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    </script>
</body>
</html>
