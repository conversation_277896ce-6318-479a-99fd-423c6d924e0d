<?php
require_once 'database.php';

echo "<h2>🔧 Fix Contractor Status for Testing</h2>";

try {
    // Get all contractors
    $stmt = $pdo->query("SELECT id, email, user_type, status FROM users WHERE user_type = 'contractor'");
    $contractors = $stmt->fetchAll();
    
    if (empty($contractors)) {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3>❌ No Contractors Found</h3>";
        echo "<p>Please add a sample contractor first:</p>";
        echo "<p><a href='add_sample_contractor.php'>Add Sample Contractor</a></p>";
        echo "</div>";
        exit();
    }
    
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📋 Current Contractors</h3>";
    
    foreach ($contractors as $contractor) {
        $status_color = [
            'pending' => 'orange',
            'approved' => 'green', 
            'rejected' => 'red',
            'suspended' => 'gray'
        ][$contractor['status']] ?? 'black';
        
        echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<p><strong>ID:</strong> " . $contractor['id'] . "</p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($contractor['email']) . "</p>";
        echo "<p><strong>Current Status:</strong> <span style='color: $status_color; font-weight: bold;'>" . htmlspecialchars($contractor['status']) . "</span></p>";
        
        // Add buttons to change status
        echo "<form method='POST' style='margin: 10px 0;'>";
        echo "<input type='hidden' name='contractor_id' value='" . $contractor['id'] . "'>";
        echo "<button type='submit' name='set_pending' style='background: orange; color: white; padding: 8px 12px; border: none; border-radius: 3px; margin: 2px;'>Set to Pending</button>";
        echo "<button type='submit' name='set_approved' style='background: green; color: white; padding: 8px 12px; border: none; border-radius: 3px; margin: 2px;'>Set to Approved</button>";
        echo "<button type='submit' name='set_rejected' style='background: red; color: white; padding: 8px 12px; border: none; border-radius: 3px; margin: 2px;'>Set to Rejected</button>";
        echo "</form>";
        echo "</div>";
    }
    echo "</div>";
    
    // Handle status changes
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $contractor_id = (int)$_POST['contractor_id'];
        $new_status = '';
        
        if (isset($_POST['set_pending'])) {
            $new_status = 'pending';
        } elseif (isset($_POST['set_approved'])) {
            $new_status = 'approved';
        } elseif (isset($_POST['set_rejected'])) {
            $new_status = 'rejected';
        }
        
        if ($new_status) {
            try {
                $stmt = $pdo->prepare("UPDATE users SET status = ? WHERE id = ? AND user_type = 'contractor'");
                $result = $stmt->execute([$new_status, $contractor_id]);
                $affected_rows = $stmt->rowCount();
                
                echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
                echo "<h3>✅ Status Updated</h3>";
                echo "<p><strong>Contractor ID:</strong> $contractor_id</p>";
                echo "<p><strong>New Status:</strong> $new_status</p>";
                echo "<p><strong>Affected Rows:</strong> $affected_rows</p>";
                
                if ($affected_rows > 0) {
                    echo "<p style='color: green;'>Status updated successfully!</p>";
                } else {
                    echo "<p style='color: red;'>No rows were updated. Check contractor ID.</p>";
                }
                
                echo "<p><a href='fix_contractor_status.php'>Refresh Page</a></p>";
                echo "</div>";
                
            } catch (PDOException $e) {
                echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
                echo "<h3>❌ Database Error</h3>";
                echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
        }
    }
    
    // Instructions
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📝 Testing Instructions</h3>";
    echo "<ol>";
    echo "<li><strong>Set contractor to 'pending'</strong> using the button above</li>";
    echo "<li><strong>Go to admin contractors page:</strong> <a href='../admin/contractors.php' target='_blank'>Admin Contractors</a></li>";
    echo "<li><strong>Try to approve the contractor</strong> using the approve button</li>";
    echo "<li><strong>Check if the status changes</strong> by refreshing this page</li>";
    echo "<li><strong>Test contractor login:</strong> <a href='../login.php' target='_blank'>Login Page</a></li>";
    echo "</ol>";
    echo "</div>";
    
    // Debug admin login
    echo "<div style='background: #e2e3e5; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🔑 Admin Login Info</h3>";
    echo "<p><strong>Admin Email:</strong> <EMAIL></p>";
    echo "<p><strong>Admin Password:</strong> admin123</p>";
    echo "<p><a href='../admin/login.php' target='_blank'>Admin Login Page</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Fix Contractor Status</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h2, h3 { color: #333; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>🔧 BrickClick Contractor Status Manager</h1>
    <p>Use this tool to test the contractor approval workflow.</p>
</body>
</html>
