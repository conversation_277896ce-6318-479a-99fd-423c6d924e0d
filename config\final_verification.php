<?php
require_once 'database.php';

echo "<h2>✅ Final System Verification</h2>";

try {
    // 1. Check Service Categories
    echo "<h3>1. Service Categories</h3>";
    $stmt = $pdo->prepare("SELECT id, name_en FROM service_categories WHERE is_active = 1 ORDER BY id");
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Service Name</th></tr>";
    foreach ($categories as $cat) {
        echo "<tr><td>{$cat['id']}</td><td>{$cat['name_en']}</td></tr>";
    }
    echo "</table>";
    
    // 2. Check Contractor Service Types (should be IDs now)
    echo "<h3>2. Contractor Service Types (Sample)</h3>";
    $stmt = $pdo->prepare("
        SELECT cp.business_name, cp.service_types, cp.service_areas 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        LIMIT 5
    ");
    $stmt->execute();
    $contractors = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Business Name</th><th>Service Type IDs</th><th>Service Names</th><th>Service Areas</th></tr>";
    
    foreach ($contractors as $contractor) {
        $service_ids = json_decode($contractor['service_types'], true);
        $areas = json_decode($contractor['service_areas'], true);
        
        // Convert IDs to names
        $service_names = [];
        if (is_array($service_ids)) {
            foreach ($service_ids as $id) {
                foreach ($categories as $cat) {
                    if ($cat['id'] == $id) {
                        $service_names[] = $cat['name_en'];
                        break;
                    }
                }
            }
        }
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
        echo "<td>" . implode(', ', $service_ids ?: []) . "</td>";
        echo "<td>" . implode(', ', $service_names) . "</td>";
        echo "<td>" . implode(', ', $areas ?: []) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 3. Test Search Queries
    echo "<h3>3. Search Query Tests</h3>";
    
    // Test House Construction search
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        AND JSON_CONTAINS(cp.service_types, ?)
    ");
    $stmt->execute([json_encode(1)]);
    $house_construction_count = $stmt->fetchColumn();
    
    // Test Vavuniya location search
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        AND JSON_CONTAINS(cp.service_areas, ?)
    ");
    $stmt->execute([json_encode('Vavuniya')]);
    $vavuniya_count = $stmt->fetchColumn();
    
    // Test combined search
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        AND JSON_CONTAINS(cp.service_types, ?)
        AND JSON_CONTAINS(cp.service_areas, ?)
    ");
    $stmt->execute([json_encode(1), json_encode('Vavuniya')]);
    $combined_count = $stmt->fetchColumn();
    
    echo "<ul>";
    echo "<li><strong>House Construction (ID: 1):</strong> $house_construction_count contractors</li>";
    echo "<li><strong>Vavuniya Location:</strong> $vavuniya_count contractors</li>";
    echo "<li><strong>House Construction + Vavuniya:</strong> $combined_count contractors</li>";
    echo "</ul>";
    
    // 4. Check if search should work
    echo "<h3>4. Search Functionality Status</h3>";
    
    if ($house_construction_count > 0) {
        echo "<p style='color: green;'>✅ <strong>House Construction search should work</strong> - Found $house_construction_count contractors</p>";
    } else {
        echo "<p style='color: red;'>❌ <strong>House Construction search will not work</strong> - No contractors found</p>";
    }
    
    if ($vavuniya_count > 0) {
        echo "<p style='color: green;'>✅ <strong>Vavuniya location search should work</strong> - Found $vavuniya_count contractors</p>";
    } else {
        echo "<p style='color: red;'>❌ <strong>Vavuniya location search will not work</strong> - No contractors found</p>";
    }
    
    // 5. Sample contractor with services
    echo "<h3>5. Sample Contractor Profile</h3>";
    $stmt = $pdo->prepare("
        SELECT cp.business_name, cp.service_types, cp.service_areas, u.id
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        AND JSON_CONTAINS(cp.service_types, ?)
        LIMIT 1
    ");
    $stmt->execute([json_encode(1)]);
    $sample = $stmt->fetch();
    
    if ($sample) {
        echo "<p><strong>Sample Contractor:</strong> " . htmlspecialchars($sample['business_name']) . "</p>";
        echo "<p><strong>Profile URL:</strong> <a href='../customer/contractor_profile.php?id={$sample['id']}' target='_blank'>View Profile</a></p>";
        
        $service_ids = json_decode($sample['service_types'], true);
        $service_names = [];
        if (is_array($service_ids)) {
            foreach ($service_ids as $id) {
                foreach ($categories as $cat) {
                    if ($cat['id'] == $id) {
                        $service_names[] = $cat['name_en'];
                        break;
                    }
                }
            }
        }
        echo "<p><strong>Services:</strong> " . implode(', ', $service_names) . "</p>";
    }
    
    echo "<h3>🎉 System Status: READY</h3>";
    echo "<p>The search functionality should now work correctly with:</p>";
    echo "<ul>";
    echo "<li>✅ Service categories properly mapped to IDs</li>";
    echo "<li>✅ Contractor profiles using service IDs</li>";
    echo "<li>✅ Search queries using JSON_CONTAINS with IDs</li>";
    echo "<li>✅ Customer profile pages displaying service names</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
