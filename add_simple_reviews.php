<?php
require_once 'config/database.php';

echo "<h2>🌟 Adding Simple Reviews</h2>";

try {
    // Get contractor and customer IDs
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $contractor = $stmt->fetch();
    
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $customer = $stmt->fetch();
    
    if (!$contractor || !$customer) {
        echo "<p>❌ Contractor or customer not found. Please run setup_test_data.php first.</p>";
        exit;
    }
    
    $contractor_id = $contractor['id'];
    $customer_id = $customer['id'];
    
    echo "<p>Using Contractor ID: $contractor_id, Customer ID: $customer_id</p>";
    
    // Check reviews table structure
    $stmt = $pdo->prepare("DESCRIBE reviews");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $column_names = array_column($columns, 'Field');
    echo "<p>Available columns: " . implode(', ', $column_names) . "</p>";
    
    // Try different approaches based on table structure
    
    if (in_array('payment_id', $column_names) && in_array('quote_response_id', $column_names)) {
        echo "<h3>Approach 1: Complex Reviews Table (with payment_id and quote_response_id)</h3>";
        
        // Get quote responses
        $stmt = $pdo->prepare("SELECT id, quote_request_id FROM quote_responses WHERE contractor_id = ? LIMIT 2");
        $stmt->execute([$contractor_id]);
        $quote_responses = $stmt->fetchAll();
        
        if (empty($quote_responses)) {
            echo "<p>❌ No quote responses found. Cannot create reviews.</p>";
        } else {
            echo "<p>Found " . count($quote_responses) . " quote responses</p>";
            
            // Check if project_payments table exists
            $stmt = $pdo->prepare("SHOW TABLES LIKE 'project_payments'");
            $stmt->execute();
            $payments_table_exists = $stmt->fetch();
            
            if (!$payments_table_exists) {
                echo "<p>❌ project_payments table doesn't exist. Creating it...</p>";
                
                // Create a simple project_payments table
                $stmt = $pdo->prepare("
                    CREATE TABLE project_payments (
                        id INT NOT NULL AUTO_INCREMENT,
                        quote_response_id INT NOT NULL,
                        customer_id INT NOT NULL,
                        contractor_id INT NOT NULL,
                        amount DECIMAL(12,2) NOT NULL,
                        payment_type ENUM('down_payment', 'milestone', 'final_payment') DEFAULT 'down_payment',
                        payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
                        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (id),
                        FOREIGN KEY (quote_response_id) REFERENCES quote_responses(id) ON DELETE CASCADE,
                        FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
                        FOREIGN KEY (contractor_id) REFERENCES users(id) ON DELETE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ");
                $stmt->execute();
                echo "<p>✅ Created project_payments table</p>";
            }
            
            // Create payments for each quote response
            foreach ($quote_responses as $response) {
                // Check if payment already exists
                $stmt = $pdo->prepare("SELECT id FROM project_payments WHERE quote_response_id = ?");
                $stmt->execute([$response['id']]);
                $existing_payment = $stmt->fetch();
                
                if (!$existing_payment) {
                    $stmt = $pdo->prepare("
                        INSERT INTO project_payments (
                            quote_response_id, customer_id, contractor_id, amount, payment_type, payment_status
                        ) VALUES (?, ?, ?, ?, 'down_payment', 'completed')
                    ");
                    $stmt->execute([
                        $response['id'],
                        $customer_id,
                        $contractor_id,
                        500000.00 // Sample payment amount
                    ]);
                    $payment_id = $pdo->lastInsertId();
                    echo "<p>✅ Created payment ID: $payment_id for quote response: {$response['id']}</p>";
                } else {
                    $payment_id = $existing_payment['id'];
                    echo "<p>✅ Using existing payment ID: $payment_id</p>";
                }
                
                // Now create review
                $stmt = $pdo->prepare("
                    INSERT INTO reviews (
                        customer_id, contractor_id, payment_id, quote_response_id, rating, review_text, 
                        status, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, 'approved', NOW(), NOW())
                    ON DUPLICATE KEY UPDATE rating = VALUES(rating), review_text = VALUES(review_text)
                ");
                
                $rating = ($response === $quote_responses[0]) ? 5 : 4;
                $review_text = ($response === $quote_responses[0]) ? 
                    'Excellent work! The construction quality is outstanding and completed on time.' :
                    'Very professional team. Good quality work and reasonable pricing.';
                
                $stmt->execute([
                    $customer_id,
                    $contractor_id,
                    $payment_id,
                    $response['id'],
                    $rating,
                    $review_text
                ]);
                
                echo "<p>✅ Created review for quote response: {$response['id']}</p>";
            }
        }
        
    } else if (in_array('quote_request_id', $column_names)) {
        echo "<h3>Approach 2: Simple Reviews Table (with quote_request_id)</h3>";
        
        // Get quote requests
        $stmt = $pdo->prepare("SELECT id FROM quote_requests WHERE customer_id = ? LIMIT 2");
        $stmt->execute([$customer_id]);
        $quote_requests = $stmt->fetchAll();
        
        if (empty($quote_requests)) {
            echo "<p>❌ No quote requests found</p>";
        } else {
            foreach ($quote_requests as $index => $request) {
                $rating = ($index === 0) ? 5 : 4;
                $review_text = ($index === 0) ? 
                    'Excellent work! The construction quality is outstanding and completed on time.' :
                    'Very professional team. Good quality work and reasonable pricing.';
                
                if (in_array('is_approved', $column_names)) {
                    $stmt = $pdo->prepare("
                        INSERT INTO reviews (
                            customer_id, contractor_id, quote_request_id, rating, review_text, is_approved, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())
                        ON DUPLICATE KEY UPDATE rating = VALUES(rating), review_text = VALUES(review_text)
                    ");
                } else {
                    $stmt = $pdo->prepare("
                        INSERT INTO reviews (
                            customer_id, contractor_id, quote_request_id, rating, review_text, status, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, 'approved', NOW(), NOW())
                        ON DUPLICATE KEY UPDATE rating = VALUES(rating), review_text = VALUES(review_text)
                    ");
                }
                
                $stmt->execute([
                    $customer_id,
                    $contractor_id,
                    $request['id'],
                    $rating,
                    $review_text
                ]);
                
                echo "<p>✅ Created review for quote request: {$request['id']}</p>";
            }
        }
        
    } else {
        echo "<h3>Approach 3: Basic Reviews Table</h3>";
        echo "<p>❌ Cannot determine how to create reviews with this table structure</p>";
        echo "<p>Available columns: " . implode(', ', $column_names) . "</p>";
    }
    
    // Check final review count
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM reviews WHERE contractor_id = ?");
    $stmt->execute([$contractor_id]);
    $review_count = $stmt->fetchColumn();
    
    echo "<h3>✅ Review Creation Complete!</h3>";
    echo "<p>Total reviews for contractor: $review_count</p>";
    
    if ($review_count > 0) {
        echo "<p><a href='contractor/dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Check Dashboard</a></p>";
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Error Creating Reviews</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Add Simple Reviews</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h2, h3 { color: #333; }
        a { text-decoration: none; }
        a:hover { opacity: 0.8; }
    </style>
</head>
<body>
</body>
</html>
