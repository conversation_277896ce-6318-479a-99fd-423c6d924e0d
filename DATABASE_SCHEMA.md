# Brick & Click Database Schema

## Overview
This document describes the complete database schema for the Brick & Click construction contractor platform. The database is designed for MariaDB/MySQL and uses proper AUTO_INCREMENT syntax.

## Database Configuration
- **Database Name**: `brick_click`
- **Character Set**: `utf8mb4`
- **Collation**: `utf8mb4_unicode_ci`
- **Engine**: `InnoDB`

## Core Tables

### 1. users
Main user table for all user types (customers, contractors, admins)
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- email (VARCHAR(255), UNIQUE, NOT NULL)
- password (VARCHAR(255), NOT NULL) -- Hashed passwords
- user_type (ENUM: 'customer', 'contractor', 'admin')
- status (ENUM: 'pending', 'approved', 'rejected', 'suspended')
- created_at, updated_at (TIMESTAMP)
```

### 2. customer_profiles
Extended profile information for customers
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- user_id (INT, FOREIG<PERSON> KEY -> users.id)
- first_name, last_name (VARCHAR)
- phone (VARCHAR(20))
- district (VARCHAR(50))
- address (TEXT)
- profile_image (VARCHAR(255))
- language_preference (ENUM: 'en', 'si', 'ta')
- created_at, updated_at (TIMESTAMP)
```

### 3. contractor_profiles
Extended profile information for contractors
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- user_id (INT, FOREIGN KEY -> users.id)
- business_name (VARCHAR(255))
- contact_person (VARCHAR(100))
- phone (VARCHAR(20))
- business_address (TEXT)
- service_areas (JSON) -- Array of districts
- service_types (JSON) -- Array of service categories
- cida_registration (VARCHAR(100))
- cida_grade (ENUM: 'C1' to 'C10')
- cida_document, license_document (VARCHAR(255)) -- File paths
- business_description (TEXT)
- website (VARCHAR(255))
- profile_image (VARCHAR(255))
- average_rating (DECIMAL(3,2))
- total_reviews, total_projects (INT)
- language_preference (ENUM: 'en', 'si', 'ta')
- created_at, updated_at (TIMESTAMP)
```

### 4. service_categories
Available service categories
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- name_en, name_si, name_ta (VARCHAR(100)) -- Multilingual names
- description_en, description_si, description_ta (TEXT)
- icon (VARCHAR(100)) -- FontAwesome icon class
- is_active (BOOLEAN)
- created_at (TIMESTAMP)
```

## Quote Management

### 5. quote_requests
Customer quote requests
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- customer_id (INT, FOREIGN KEY -> users.id)
- service_category_id (INT, FOREIGN KEY -> service_categories.id)
- title (VARCHAR(255))
- description (TEXT)
- location, district (VARCHAR)
- estimated_budget (DECIMAL(12,2))
- project_timeline (VARCHAR(100))
- status (ENUM: 'open', 'closed', 'cancelled')
- created_at, updated_at (TIMESTAMP)
```

### 6. quote_responses
Contractor responses to quote requests
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- quote_request_id (INT, FOREIGN KEY -> quote_requests.id)
- contractor_id (INT, FOREIGN KEY -> users.id)
- quoted_amount (DECIMAL(12,2))
- estimated_timeline (VARCHAR(100))
- description, terms_conditions (TEXT)
- status (ENUM: 'pending', 'accepted', 'rejected')
- created_at, updated_at (TIMESTAMP)
```

## Social Features

### 7. customer_favorites
Customer favorite contractors
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- customer_id (INT, FOREIGN KEY -> users.id)
- contractor_id (INT, FOREIGN KEY -> users.id)
- created_at (TIMESTAMP)
- UNIQUE(customer_id, contractor_id)
```

### 8. reviews
Customer reviews and ratings
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- customer_id (INT, FOREIGN KEY -> users.id)
- contractor_id (INT, FOREIGN KEY -> users.id)
- quote_request_id (INT, FOREIGN KEY -> quote_requests.id)
- rating (INT, 1-5, CHECK constraint)
- review_text (TEXT)
- is_verified, is_approved (BOOLEAN)
- created_at, updated_at (TIMESTAMP)
- UNIQUE(customer_id, contractor_id, quote_request_id)
```

### 9. contractor_portfolios
Contractor project portfolios
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- contractor_id (INT, FOREIGN KEY -> users.id)
- project_name (VARCHAR(255))
- project_description (TEXT)
- project_location (VARCHAR(255))
- completion_date (DATE)
- project_value (DECIMAL(12,2))
- project_images (JSON) -- Array of image paths
- is_featured (BOOLEAN)
- created_at, updated_at (TIMESTAMP)
```

## System Features

### 10. notifications
System notifications
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- user_id (INT, FOREIGN KEY -> users.id)
- title (VARCHAR(255))
- message (TEXT)
- type (ENUM: 'quote_received', 'quote_response', 'approval_status', 'new_review', 'verification_update', 'general')
- is_read (BOOLEAN)
- related_id (INT) -- Related record ID
- created_at (TIMESTAMP)
```

### 11. project_payments
Payment tracking
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- quote_response_id (INT, FOREIGN KEY -> quote_responses.id)
- customer_id, contractor_id (INT, FOREIGN KEY -> users.id)
- amount (DECIMAL(12,2))
- payment_type (ENUM: 'down_payment', 'milestone', 'final_payment')
- payment_status (ENUM: 'pending', 'completed', 'failed', 'refunded')
- payment_method (VARCHAR(50))
- transaction_id (VARCHAR(255))
- payment_date (TIMESTAMP)
- created_at, updated_at (TIMESTAMP)
```

### 12. cost_estimator_data
Cost estimation data
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- service_category_id (INT, FOREIGN KEY -> service_categories.id)
- area_range (VARCHAR(50)) -- e.g., '0-500', '500-1000'
- building_size (ENUM: 'small', 'medium', 'large', 'extra_large')
- min_cost, max_cost (DECIMAL(12,2))
- cost_per_sqft (DECIMAL(8,2))
- description (TEXT)
- last_updated (TIMESTAMP)
```

### 13. translations
Multilingual support
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- translation_key (VARCHAR(255))
- language_code (ENUM: 'en', 'si', 'ta')
- translation_value (TEXT)
- context (VARCHAR(100)) -- Page/section context
- created_at, updated_at (TIMESTAMP)
- UNIQUE(translation_key, language_code)
```

### 14. user_sessions
Remember me functionality
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- user_id (INT, FOREIGN KEY -> users.id)
- session_token (VARCHAR(255), UNIQUE)
- expires_at (TIMESTAMP)
- created_at (TIMESTAMP)
```

## Admin Features

### 15. admin_logs
Admin activity logging
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- admin_id (INT, FOREIGN KEY -> users.id)
- action (VARCHAR(100))
- target_type (VARCHAR(50)) -- 'user', 'contractor', 'review', etc.
- target_id (INT)
- description (TEXT)
- ip_address (VARCHAR(45))
- user_agent (TEXT)
- created_at (TIMESTAMP)
```

### 16. system_reports
System analytics and reports
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- report_type (VARCHAR(50))
- report_data (JSON)
- generated_by (INT, FOREIGN KEY -> users.id)
- generated_at (TIMESTAMP)
- report_period_start, report_period_end (DATE)
```

### 17. admin_settings
System configuration
```sql
- id (INT, AUTO_INCREMENT, PRIMARY KEY)
- setting_key (VARCHAR(100), UNIQUE)
- setting_value (TEXT)
- description (TEXT)
- updated_at (TIMESTAMP)
```

## Key Features

### Indexes
All tables include appropriate indexes for:
- Primary keys (AUTO_INCREMENT)
- Foreign keys
- Frequently queried columns
- Unique constraints

### JSON Support
Modern JSON columns for:
- Service areas and types
- Project images
- Report data
- Translation values

### Multilingual Support
- Service categories in English, Sinhala, Tamil
- Translation system for UI elements
- Language preferences per user

### Security Features
- Password hashing
- Session management
- Admin activity logging
- User status management

### Performance Optimizations
- Proper indexing strategy
- InnoDB engine for ACID compliance
- UTF8MB4 for full Unicode support
- Optimized queries with foreign keys

## Setup Instructions

1. Run `setup_database.php` in your browser
2. Or execute `config/create_database.sql` directly in MySQL
3. Default admin login: `<EMAIL>` / `admin123`

## Sample Data Included

- 8 service categories with multilingual names
- Cost estimator data for common project types
- Basic UI translations
- Admin user account
- System configuration settings
