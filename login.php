<?php
session_start();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Login - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, login" name="keywords">
    <meta content="Login to Brick & Click - Sri Lanka's leading construction contractor platform" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">

    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #212529 0%, #34495e 100%);
            display: flex;
            align-items: center;
            padding: 2rem 0;
        }
        
        .login-card {
            background: white;
            border-radius: 25px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.15);
            overflow: hidden;
            max-width: 500px;
            margin: 0 auto;
            border: 1px solid rgba(197, 23, 46, 0.1);
            position: relative;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #C5172E, #FF6B35, #F7931E);
        }
        
        .login-header {
            background: linear-gradient(135deg, #fd7e14, #ff9500);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-form {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 1rem 1.25rem;
            font-size: 1rem;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #C5172E;
            box-shadow: 0 0 0 0.2rem rgba(197, 23, 46, 0.25);
            background: white;
            outline: none;
        }

        .form-control::placeholder {
            color: #6c757d;
            font-weight: 400;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #C5172E, #FF6B35);
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            font-weight: 700;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            box-shadow: 0 8px 25px rgba(197, 23, 46, 0.3);
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(197, 23, 46, 0.4);
            color: white;
            background: linear-gradient(135deg, #A01426, #E55A2B);
        }

        .btn-login:active {
            transform: translateY(-1px);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }
        
        .divider span {
            background: white;
            padding: 0 1rem;
            color: #6c757d;
        }
    </style>
</head>

<body>
    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
        <a href="index.php" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <div class="navbar-nav ms-auto p-4 p-lg-0 d-flex align-items-center">
            <a href="index.php" class="nav-item nav-link">Home</a>
            <a href="signup.php" class="btn btn-primary ms-3" style="padding: 0.5rem 1.5rem; border-radius: 25px; font-weight: 600;">Sign Up</a>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Login Container Start -->
    <div class="login-container">
        <div class="container">
            <div class="login-card">
                <div class="login-header">
                    <i class="fas fa-user-circle fa-3x mb-3"></i>
                    <h2>Welcome Back</h2>
                    <p class="mb-0">Sign in to your Brick & Click account</p>
                </div>
                
                <div class="login-form">
                    <?php
                    if (isset($_SESSION['error'])) {
                        echo '<div class="alert alert-danger">' . $_SESSION['error'] . '</div>';
                        unset($_SESSION['error']);
                    }
                    if (isset($_SESSION['success'])) {
                        echo '<div class="alert alert-success">' . $_SESSION['success'] . '</div>';
                        unset($_SESSION['success']);
                    }
                    ?>
                    
                    <form action="process_login.php" method="POST">
                        <div class="mb-3">
                            <label class="form-label">Email Address</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0" style="border-radius: 10px 0 0 10px; border-color: #e9ecef;">
                                    <i class="fas fa-envelope text-muted"></i>
                                </span>
                                <input type="email" class="form-control border-start-0" name="email" required style="border-radius: 0 10px 10px 0;">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0" style="border-radius: 10px 0 0 10px; border-color: #e9ecef;">
                                    <i class="fas fa-lock text-muted"></i>
                                </span>
                                <input type="password" class="form-control border-start-0" name="password" required style="border-radius: 0 10px 10px 0;">
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" name="remember_me">
                            <label class="form-check-label">Remember me</label>
                        </div>
                        
                        <button type="submit" class="btn btn-login mb-3">
                            <i class="fas fa-sign-in-alt"></i>
                            Sign In
                        </button>
                    </form>
                    
                    <div class="text-center">
                        <a href="#" class="text-primary text-decoration-none">Forgot your password?</a>
                    </div>
                    
                    <div class="divider">
                        <span>Don't have an account?</span>
                    </div>
                    
                    <div class="text-center">
                        <a href="signup.php" class="btn btn-outline-primary" style="border-radius: 10px; width: 100%;">Create New Account</a>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <small class="text-muted">
                            By signing in, you agree to our 
                            <a href="#" class="text-primary">Terms of Service</a> and 
                            <a href="#" class="text-primary">Privacy Policy</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Login Container End -->

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
