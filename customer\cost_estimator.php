<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

// Get customer profile
try {
    $stmt = $pdo->prepare("SELECT cp.*, u.email FROM customer_profiles cp JOIN users u ON cp.user_id = u.id WHERE cp.user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $customer = $stmt->fetch();
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: ../login.php');
    exit();
}

// Get unread notifications count
$unread_count = 0;
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$_SESSION['user_id']]);
    $unread_count = $stmt->fetchColumn();
} catch (PDOException $e) {
    // Ignore error, keep count as 0
}

// Get service categories
try {
    $stmt = $pdo->prepare("SELECT * FROM service_categories WHERE is_active = 1 ORDER BY name_en");
    $stmt->execute();
    $service_categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $service_categories = [];
}

// Handle AJAX request for cost estimation (backup server-side calculation)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax'])) {
    header('Content-Type: application/json');

    $service_id = (int)$_POST['service_id'];
    $square_footage = (int)$_POST['square_footage'];
    $floors = $_POST['floors'];
    $location = $_POST['location'];
    $timeline = $_POST['timeline'];
    $material_quality = $_POST['material_quality'];
    $complexity = $_POST['complexity'];
    $features = $_POST['features'] ?? [];

    // Base costs per square foot for different services
    $base_costs = [
        1 => ['min' => 8000, 'max' => 15000, 'name' => 'House Construction'],
        2 => ['min' => 5000, 'max' => 12000, 'name' => 'Building Renovation'],
        3 => ['min' => 10000, 'max' => 20000, 'name' => 'Commercial Construction'],
        4 => ['min' => 3000, 'max' => 8000, 'name' => 'Interior Design & Finishing'],
        5 => ['min' => 2500, 'max' => 6000, 'name' => 'Roofing & Waterproofing'],
        6 => ['min' => 1500, 'max' => 4000, 'name' => 'Electrical Work'],
        7 => ['min' => 2000, 'max' => 5000, 'name' => 'Plumbing & Sanitation'],
        8 => ['min' => 1000, 'max' => 3000, 'name' => 'Landscaping & Gardening'],
        9 => ['min' => 15000, 'max' => 30000, 'name' => 'Swimming Pool Construction'],
        10 => ['min' => 5000, 'max' => 12000, 'name' => 'Road & Infrastructure']
    ];

    if (isset($base_costs[$service_id]) && $square_footage > 0) {
        $base_cost = $base_costs[$service_id];
        $min_cost = $base_cost['min'] * $square_footage;
        $max_cost = $base_cost['max'] * $square_footage;

        // Apply basic multipliers (simplified server-side version)
        $location_multiplier = ($location === 'colombo') ? 1.3 : 1.0;
        $material_multiplier = ($material_quality === 'premium') ? 1.3 : (($material_quality === 'luxury') ? 1.6 : 1.0);

        $min_cost *= $location_multiplier * $material_multiplier;
        $max_cost *= $location_multiplier * $material_multiplier;

        echo json_encode([
            'success' => true,
            'min_cost' => round($min_cost),
            'max_cost' => round($max_cost),
            'cost_per_sqft' => $base_cost['min'] . ' - ' . $base_cost['max'],
            'description' => $base_cost['name'] . ' estimate for ' . $square_footage . ' sq ft'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid service or area specified'
        ]);
    }
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Cost Estimator - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, cost estimator" name="keywords">
    <meta content="Cost Estimator - Brick & Click" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <style>
        .estimator-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .estimator-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
            z-index: 1;
        }

        .estimator-header .container {
            position: relative;
            z-index: 2;
        }

        .estimator-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 30px;
            box-shadow: 0 25px 70px rgba(0,0,0,0.15);
            overflow: hidden;
            margin-top: -3rem;
            position: relative;
            z-index: 10;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .estimator-form {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem 1.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.8);
        }

        .form-control:focus {
            border-color: #fd7e14;
            box-shadow: 0 0 0 0.3rem rgba(253, 126, 20, 0.15);
            background: white;
            transform: translateY(-2px);
        }

        .form-control:hover {
            border-color: #fd7e14;
            background: white;
        }

        .btn-estimate {
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            border: none;
            border-radius: 15px;
            padding: 1.25rem 3rem;
            font-weight: 700;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 1.2rem;
            position: relative;
            overflow: hidden;
        }

        .btn-estimate::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-estimate:hover::before {
            left: 100%;
        }

        .btn-estimate:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(253, 126, 20, 0.4);
            color: white;
        }
        
        .service-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .service-option {
            border: 2px solid #e9ecef;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            position: relative;
            overflow: hidden;
        }

        .service-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .service-option:hover::before {
            opacity: 0.1;
        }

        .service-option:hover {
            border-color: #fd7e14;
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
        }

        .service-option.selected {
            border-color: #fd7e14;
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            color: white;
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 20px 50px rgba(253, 126, 20, 0.3);
        }

        .service-option * {
            position: relative;
            z-index: 2;
        }

        .service-icon {
            font-size: 2.5rem;
            margin-bottom: 1.5rem;
            color: #fd7e14;
            transition: all 0.3s ease;
        }

        .service-option:hover .service-icon {
            transform: scale(1.1);
        }

        .service-option.selected .service-icon {
            color: white;
            transform: scale(1.1);
        }
        
        .size-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .size-option {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .size-option:hover {
            border-color: #fd7e14;
        }
        
        .size-option.selected {
            border-color: #fd7e14;
            background: #fff5f0;
        }
        
        .results-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
            display: none;
        }
        
        .cost-range {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .cost-details {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #fd7e14;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .info-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .info-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .info-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #fd7e14, #ff9500);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
        }
    </style>
</head>

<body>
    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
        <a href="../index.php" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="../img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="contractors.php" class="nav-item nav-link">Find Contractors</a>
                <a href="quotes.php" class="nav-item nav-link">My Quotes</a>
                <a href="cost_estimator.php" class="nav-item nav-link active">Cost Estimator</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
            </div>
            <div class="dropdown">
                <a href="#" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($customer['first_name']); ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="notifications.php"><i class="fas fa-bell me-2"></i>Notifications <?php if ($unread_count > 0): ?><span class="badge bg-danger"><?php echo $unread_count; ?></span><?php endif; ?></a></li>
                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Estimator Header Start -->
    <div class="estimator-header">
        <div class="container text-center">
            <h1 class="display-4 mb-3">Project Cost Estimator</h1>
            <p class="fs-5 mb-0">Get realistic budget estimates for your construction project</p>
        </div>
    </div>
    <!-- Estimator Header End -->

    <!-- Estimator Form Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <div class="estimator-card">
                <div class="estimator-form">
                    <h3 class="mb-4 text-center">Calculate Your Project Cost</h3>
                    
                    <form id="estimatorForm">
                        <!-- Service Type Selection -->
                        <div class="mb-4">
                            <h5 class="mb-3">1. Select Service Type</h5>
                            <div class="service-selector">
                                <div class="service-option" data-service-id="1">
                                    <div class="service-icon">
                                        <i class="fas fa-home"></i>
                                    </div>
                                    <h6>House Construction</h6>
                                </div>
                                <div class="service-option" data-service-id="2">
                                    <div class="service-icon">
                                        <i class="fas fa-tools"></i>
                                    </div>
                                    <h6>Building Renovation</h6>
                                </div>
                                <div class="service-option" data-service-id="3">
                                    <div class="service-icon">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <h6>Commercial Construction</h6>
                                </div>
                                <div class="service-option" data-service-id="4">
                                    <div class="service-icon">
                                        <i class="fas fa-paint-roller"></i>
                                    </div>
                                    <h6>Interior Design & Finishing</h6>
                                </div>
                                <div class="service-option" data-service-id="5">
                                    <div class="service-icon">
                                        <i class="fas fa-warehouse"></i>
                                    </div>
                                    <h6>Roofing & Waterproofing</h6>
                                </div>
                                <div class="service-option" data-service-id="6">
                                    <div class="service-icon">
                                        <i class="fas fa-bolt"></i>
                                    </div>
                                    <h6>Electrical Work</h6>
                                </div>
                                <div class="service-option" data-service-id="7">
                                    <div class="service-icon">
                                        <i class="fas fa-wrench"></i>
                                    </div>
                                    <h6>Plumbing & Sanitation</h6>
                                </div>
                                <div class="service-option" data-service-id="8">
                                    <div class="service-icon">
                                        <i class="fas fa-seedling"></i>
                                    </div>
                                    <h6>Landscaping & Gardening</h6>
                                </div>
                                <div class="service-option" data-service-id="9">
                                    <div class="service-icon">
                                        <i class="fas fa-swimming-pool"></i>
                                    </div>
                                    <h6>Swimming Pool Construction</h6>
                                </div>
                                <div class="service-option" data-service-id="10">
                                    <div class="service-icon">
                                        <i class="fas fa-road"></i>
                                    </div>
                                    <h6>Road & Infrastructure</h6>
                                </div>
                            </div>
                            <input type="hidden" id="selectedService" name="service_id" required>
                        </div>

                        <!-- Project Details -->
                        <div class="mb-4">
                            <h5 class="mb-3">2. Project Details</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="squareFootage" class="form-label">Square Footage</label>
                                    <input type="number" class="form-control" id="squareFootage" name="square_footage"
                                           placeholder="Enter area in sq ft" min="1" max="50000" required>
                                    <div class="form-text">Enter the total area of your project</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="floors" class="form-label">Number of Floors</label>
                                    <select class="form-control" id="floors" name="floors" required>
                                        <option value="">Select floors</option>
                                        <option value="1">1 Floor</option>
                                        <option value="2">2 Floors</option>
                                        <option value="3">3 Floors</option>
                                        <option value="4">4+ Floors</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="location" class="form-label">Location</label>
                                    <select class="form-control" id="location" name="location" required>
                                        <option value="">Select location</option>
                                        <option value="colombo">Colombo</option>
                                        <option value="gampaha">Gampaha</option>
                                        <option value="kalutara">Kalutara</option>
                                        <option value="kandy">Kandy</option>
                                        <option value="galle">Galle</option>
                                        <option value="matara">Matara</option>
                                        <option value="hambantota">Hambantota</option>
                                        <option value="jaffna">Jaffna</option>
                                        <option value="kilinochchi">Kilinochchi</option>
                                        <option value="mannar">Mannar</option>
                                        <option value="vavuniya">Vavuniya</option>
                                        <option value="mullaitivu">Mullaitivu</option>
                                        <option value="batticaloa">Batticaloa</option>
                                        <option value="ampara">Ampara</option>
                                        <option value="trincomalee">Trincomalee</option>
                                        <option value="kurunegala">Kurunegala</option>
                                        <option value="puttalam">Puttalam</option>
                                        <option value="anuradhapura">Anuradhapura</option>
                                        <option value="polonnaruwa">Polonnaruwa</option>
                                        <option value="badulla">Badulla</option>
                                        <option value="moneragala">Moneragala</option>
                                        <option value="ratnapura">Ratnapura</option>
                                        <option value="kegalle">Kegalle</option>
                                        <option value="nuwara_eliya">Nuwara Eliya</option>
                                        <option value="matale">Matale</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="timeline" class="form-label">Project Timeline</label>
                                    <select class="form-control" id="timeline" name="timeline" required>
                                        <option value="">Select timeline</option>
                                        <option value="urgent">Urgent (1-2 months)</option>
                                        <option value="normal">Normal (3-6 months)</option>
                                        <option value="flexible">Flexible (6+ months)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Material Quality & Project Scale -->
                        <div class="mb-4">
                            <h5 class="mb-3">3. Material Quality & Project Scale</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="materialQuality" class="form-label">Material Quality</label>
                                    <select class="form-control" id="materialQuality" name="material_quality" required>
                                        <option value="">Select quality</option>
                                        <option value="basic">Basic - Standard materials</option>
                                        <option value="standard">Standard - Good quality materials</option>
                                        <option value="premium">Premium - High-end materials</option>
                                        <option value="luxury">Luxury - Top-tier materials</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="complexity" class="form-label">Project Complexity</label>
                                    <select class="form-control" id="complexity" name="complexity" required>
                                        <option value="">Select complexity</option>
                                        <option value="simple">Simple - Basic design</option>
                                        <option value="moderate">Moderate - Some custom features</option>
                                        <option value="complex">Complex - Many custom features</option>
                                        <option value="very_complex">Very Complex - Highly customized</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Features -->
                        <div class="mb-4">
                            <h5 class="mb-3">4. Additional Features (Optional)</h5>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="airConditioning" name="features[]" value="air_conditioning">
                                        <label class="form-check-label" for="airConditioning">
                                            Air Conditioning
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="solarPanels" name="features[]" value="solar_panels">
                                        <label class="form-check-label" for="solarPanels">
                                            Solar Panels
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="swimmingPool" name="features[]" value="swimming_pool">
                                        <label class="form-check-label" for="swimmingPool">
                                            Swimming Pool
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="garden" name="features[]" value="garden">
                                        <label class="form-check-label" for="garden">
                                            Landscaped Garden
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="garage" name="features[]" value="garage">
                                        <label class="form-check-label" for="garage">
                                            Garage/Parking
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="security" name="features[]" value="security_system">
                                        <label class="form-check-label" for="security">
                                            Security System
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-estimate">
                            <i class="fas fa-calculator me-2"></i>Calculate Estimate
                        </button>
                    </form>

                    <!-- Loading -->
                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        <p>Calculating your estimate...</p>
                    </div>

                    <!-- Results -->
                    <div class="results-card" id="results">
                        <h4><i class="fas fa-chart-line me-2"></i>Estimated Project Cost</h4>
                        <div class="cost-range" id="costRange"></div>
                        <div class="cost-details" id="costDetails"></div>
                        <div class="mt-3">
                            <a href="request_quote.php" class="btn btn-light">
                                <i class="fas fa-paper-plane me-2"></i>Request Detailed Quotes
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Info Cards -->
            <div class="info-cards">
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <h5>How It Works</h5>
                    <p>Our cost estimator uses real market data from completed projects to provide accurate budget ranges for your construction project.</p>
                </div>
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h5>Accurate Estimates</h5>
                    <p>Estimates are based on current market rates, material costs, and labor charges in Sri Lanka's construction industry.</p>
                </div>
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h5>Get Detailed Quotes</h5>
                    <p>Use this estimate as a starting point and request detailed quotes from verified contractors for precise pricing.</p>
                </div>
            </div>
        </div>
    </div>
    <!-- Estimator Form End -->

    <script>
        let selectedServiceId = null;

        // Base cost per square foot for different services (in LKR)
        const baseCosts = {
            1: { min: 8000, max: 15000, name: 'House Construction' },
            2: { min: 5000, max: 12000, name: 'Building Renovation' },
            3: { min: 10000, max: 20000, name: 'Commercial Construction' },
            4: { min: 3000, max: 8000, name: 'Interior Design & Finishing' },
            5: { min: 2500, max: 6000, name: 'Roofing & Waterproofing' },
            6: { min: 1500, max: 4000, name: 'Electrical Work' },
            7: { min: 2000, max: 5000, name: 'Plumbing & Sanitation' },
            8: { min: 1000, max: 3000, name: 'Landscaping & Gardening' },
            9: { min: 15000, max: 30000, name: 'Swimming Pool Construction' },
            10: { min: 5000, max: 12000, name: 'Road & Infrastructure' }
        };

        // Multipliers for different factors
        const multipliers = {
            floors: { 1: 1.0, 2: 1.2, 3: 1.4, 4: 1.6 },
            location: {
                colombo: 1.3, gampaha: 1.1, kalutara: 1.0, kandy: 1.1, galle: 1.0,
                matara: 0.9, hambantota: 0.9, jaffna: 0.9, kilinochchi: 0.8, mannar: 0.8,
                vavuniya: 0.8, mullaitivu: 0.8, batticaloa: 0.9, ampara: 0.8, trincomalee: 0.9,
                kurunegala: 0.9, puttalam: 0.9, anuradhapura: 0.8, polonnaruwa: 0.8,
                badulla: 0.9, moneragala: 0.8, ratnapura: 0.9, kegalle: 0.9, nuwara_eliya: 1.0, matale: 0.9
            },
            timeline: { urgent: 1.2, normal: 1.0, flexible: 0.9 },
            materialQuality: { basic: 0.8, standard: 1.0, premium: 1.3, luxury: 1.6 },
            complexity: { simple: 0.9, moderate: 1.0, complex: 1.2, very_complex: 1.4 },
            features: {
                air_conditioning: 150000, solar_panels: 300000, swimming_pool: 800000,
                garden: 100000, garage: 200000, security_system: 80000
            }
        };

        // Service selection
        document.querySelectorAll('.service-option').forEach(option => {
            option.addEventListener('click', function() {
                // Remove selection from all options
                document.querySelectorAll('.service-option').forEach(opt => opt.classList.remove('selected'));

                // Add selection to clicked option
                this.classList.add('selected');

                // Store selection
                selectedServiceId = this.dataset.serviceId;
                document.getElementById('selectedService').value = selectedServiceId;
            });
        });

        // Real-time cost calculation
        function calculateEstimate() {
            const serviceId = parseInt(document.getElementById('selectedService').value);
            const squareFootage = parseInt(document.getElementById('squareFootage').value);
            const floors = document.getElementById('floors').value;
            const location = document.getElementById('location').value;
            const timeline = document.getElementById('timeline').value;
            const materialQuality = document.getElementById('materialQuality').value;
            const complexity = document.getElementById('complexity').value;

            if (!serviceId || !squareFootage || !floors || !location || !timeline || !materialQuality || !complexity) {
                return null;
            }

            const baseCost = baseCosts[serviceId];
            if (!baseCost) return null;

            // Calculate base cost
            let minCost = baseCost.min * squareFootage;
            let maxCost = baseCost.max * squareFootage;

            // Apply multipliers
            const floorMultiplier = multipliers.floors[floors] || 1;
            const locationMultiplier = multipliers.location[location] || 1;
            const timelineMultiplier = multipliers.timeline[timeline] || 1;
            const materialMultiplier = multipliers.materialQuality[materialQuality] || 1;
            const complexityMultiplier = multipliers.complexity[complexity] || 1;

            minCost *= floorMultiplier * locationMultiplier * timelineMultiplier * materialMultiplier * complexityMultiplier;
            maxCost *= floorMultiplier * locationMultiplier * timelineMultiplier * materialMultiplier * complexityMultiplier;

            // Add feature costs
            const selectedFeatures = document.querySelectorAll('input[name="features[]"]:checked');
            let featureCost = 0;
            selectedFeatures.forEach(feature => {
                featureCost += multipliers.features[feature.value] || 0;
            });

            minCost += featureCost;
            maxCost += featureCost;

            return {
                min: Math.round(minCost),
                max: Math.round(maxCost),
                baseCostPerSqft: baseCost.min + ' - ' + baseCost.max,
                serviceName: baseCost.name
            };
        }

        // Form submission
        document.getElementById('estimatorForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';

            // Calculate estimate using client-side logic
            setTimeout(() => {
                const estimate = calculateEstimate();
                document.getElementById('loading').style.display = 'none';

                if (estimate) {
                    const minCost = estimate.min.toLocaleString();
                    const maxCost = estimate.max.toLocaleString();

                    document.getElementById('costRange').textContent = `LKR ${minCost} - ${maxCost}`;

                    const squareFootage = document.getElementById('squareFootage').value;
                    const location = document.getElementById('location').value;
                    const materialQuality = document.getElementById('materialQuality').value;
                    const complexity = document.getElementById('complexity').value;

                    let details = `<div class="row g-3">
                        <div class="col-md-6">
                            <p><strong>Service:</strong> ${estimate.serviceName}</p>
                            <p><strong>Area:</strong> ${squareFootage} sq ft</p>
                            <p><strong>Base Cost per sq ft:</strong> LKR ${estimate.baseCostPerSqft}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Location:</strong> ${location.charAt(0).toUpperCase() + location.slice(1)}</p>
                            <p><strong>Material Quality:</strong> ${materialQuality.charAt(0).toUpperCase() + materialQuality.slice(1)}</p>
                            <p><strong>Complexity:</strong> ${complexity.charAt(0).toUpperCase() + complexity.slice(1)}</p>
                        </div>
                    </div>`;

                    const selectedFeatures = document.querySelectorAll('input[name="features[]"]:checked');
                    if (selectedFeatures.length > 0) {
                        details += '<p><strong>Additional Features:</strong> ';
                        const featureNames = Array.from(selectedFeatures).map(f => f.value.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()));
                        details += featureNames.join(', ') + '</p>';
                    }

                    details += `<div class="alert alert-info mt-3">
                        <small><i class="fas fa-info-circle me-2"></i>This estimate includes all major construction costs based on current market rates in Sri Lanka. Final costs may vary based on specific requirements, site conditions, and contractor selection.</small>
                    </div>`;

                    document.getElementById('costDetails').innerHTML = details;
                    document.getElementById('results').style.display = 'block';
                } else {
                    alert('Please fill in all required fields to calculate the estimate.');
                }
            }, 1000);
        });

        // Add real-time validation and preview
        document.getElementById('squareFootage').addEventListener('input', function() {
            const value = parseInt(this.value);
            if (value > 0) {
                const estimate = calculateEstimate();
                if (estimate) {
                    // Could add a preview here
                }
            }
        });
    </script>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
</body>
</html>
