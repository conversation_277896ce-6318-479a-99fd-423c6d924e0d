<?php
require_once 'database.php';

try {
    // Start transaction
    $pdo->beginTransaction();

    // Insert sample contractor user
    $email = '<EMAIL>';
    $password = password_hash('password', PASSWORD_DEFAULT);

    $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status, created_at, updated_at) VALUES (?, ?, 'contractor', 'approved', NOW(), NOW())");
    $stmt->execute([$email, $password]);
    $contractor_user_id = $pdo->lastInsertId();

    // Insert contractor profile
    $stmt = $pdo->prepare("
        INSERT INTO contractor_profiles (
            user_id, business_name, contact_person, phone, business_address,
            service_areas, service_types, cida_registration, cida_grade,
            business_description, website, profile_image, average_rating,
            total_reviews, total_projects, language_preference, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    ");

    $stmt->execute([
        $contractor_user_id,
        'Elite Construction Solutions',
        '<PERSON><PERSON>',
        '+94 77 123 4567',
        'No. 45, Galle Road, Colombo 03, Sri Lanka',
        json_encode(['Colombo', 'Gampaha', 'Kalutara', 'Kandy']),
        json_encode(['House Construction', 'Building Renovation', 'Commercial Construction', 'Interior Design & Finishing']),
        'CIDA/REG/2023/001234',
        'C5',
        'Elite Construction Solutions is a leading construction company in Sri Lanka with over 15 years of experience in residential and commercial construction. We specialize in modern villa construction, office buildings, and luxury interior finishing. Our team of certified engineers and skilled craftsmen ensure quality workmanship and timely project completion. We have successfully completed over 200 projects across the Western and Central provinces.',
        'https://www.eliteconstructionlk.com',
        NULL,
        4.7,
        23,
        47,
        'en'
    ]);

    // Insert sample portfolio projects
    $portfolio_projects = [
        [
            'Modern Villa - Colombo 07',
            'A stunning 4-bedroom modern villa featuring contemporary architecture, smart home automation, and luxury finishes. The project included a swimming pool, landscaped garden, and a 3-car garage. Built with eco-friendly materials and energy-efficient systems.',
            'Colombo 07, Sri Lanka',
            '2023-08-15',
            25000000.00,
            1
        ],
        [
            'Commercial Office Complex',
            'A 5-story commercial office building with modern amenities including central air conditioning, high-speed elevators, and underground parking. The building features a glass facade with energy-efficient design and LEED certification.',
            'Gampaha, Sri Lanka',
            '2023-06-30',
            45000000.00,
            1
        ],
        [
            'Luxury Apartment Renovation',
            'Complete renovation of a 3-bedroom apartment including kitchen remodeling, bathroom upgrades, flooring replacement, and interior design. The project transformed an old apartment into a modern living space with premium finishes.',
            'Kandy, Sri Lanka',
            '2023-09-20',
            3500000.00,
            0
        ],
        [
            'Restaurant Interior Design',
            'Interior design and construction for a fine dining restaurant featuring custom woodwork, ambient lighting, and modern kitchen setup. The project included custom furniture, decorative elements, and complete electrical and plumbing work.',
            'Kalutara, Sri Lanka',
            '2023-07-10',
            2800000.00,
            0
        ]
    ];

    $stmt = $pdo->prepare("
        INSERT INTO contractor_portfolios (
            contractor_id, project_name, project_description, project_location,
            completion_date, project_value, project_images, is_featured, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, '[]', ?, NOW(), NOW())
    ");

    foreach ($portfolio_projects as $project) {
        $stmt->execute([
            $contractor_user_id,
            $project[0], // project_name
            $project[1], // project_description
            $project[2], // project_location
            $project[3], // completion_date
            $project[4], // project_value
            $project[5]  // is_featured
        ]);
    }

    // Commit transaction
    $pdo->commit();

    echo "<h2>✅ Sample Contractor Data Added Successfully!</h2>";
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🔑 Login Credentials:</h3>";
    echo "<p><strong>Email:</strong> <EMAIL></p>";
    echo "<p><strong>Password:</strong> password</p>";
    echo "<p><strong>User Type:</strong> Contractor</p>";
    echo "<p><strong>Status:</strong> Approved</p>";
    echo "<p><strong>Business:</strong> Elite Construction Solutions</p>";
    echo "</div>";

    echo "<div style='background: #cce5ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📊 Sample Data Included:</h3>";
    echo "<ul>";
    echo "<li>✅ Complete contractor profile with CIDA registration</li>";
    echo "<li>✅ 4 portfolio projects with different categories</li>";
    echo "<li>✅ Business information and contact details</li>";
    echo "<li>✅ Service areas: Colombo, Gampaha, Kalutara, Kandy</li>";
    echo "<li>✅ Service types: House Construction, Building Renovation, Commercial Construction, Interior Design</li>";
    echo "<li>✅ CIDA Grade: C5</li>";
    echo "</ul>";
    echo "</div>";

    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🚀 Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Go to <a href='../login.php' target='_blank'>login.php</a></li>";
    echo "<li>Use the credentials above to login</li>";
    echo "<li>You'll be redirected to the contractor dashboard</li>";
    echo "<li>Explore all the contractor features!</li>";
    echo "</ol>";
    echo "</div>";

} catch (PDOException $e) {
    $pdo->rollback();

    if ($e->getCode() == 23000) {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3>⚠️ Contractor Already Exists</h3>";
        echo "<p>The sample contractor data has already been added to the database.</p>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>🔑 Use These Login Credentials:</h4>";
        echo "<p><strong>Email:</strong> <EMAIL></p>";
        echo "<p><strong>Password:</strong> password</p>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3>❌ Error Adding Sample Data</h3>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Add Sample Contractor Data</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h2, h3 { color: #333; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>🏗️ BrickClick Sample Contractor Data</h1>
    <p>This script adds sample contractor data to your database for testing purposes.</p>
</body>
</html>