<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

// Handle review deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['delete_review'])) {
        $review_id = (int)$_POST['review_id'];
        try {
            // Check if using status field or is_approved field
            $stmt = $pdo->prepare("SHOW COLUMNS FROM reviews LIKE 'status'");
            $stmt->execute();
            $has_status_field = $stmt->fetch();

            if ($has_status_field) {
                // Use status field - mark as deleted instead of actually deleting
                $stmt = $pdo->prepare("UPDATE reviews SET status = 'deleted' WHERE id = ?");
                $stmt->execute([$review_id]);
            } else {
                // Use is_approved field - mark as rejected (deleted equivalent)
                $stmt = $pdo->prepare("UPDATE reviews SET is_approved = 0 WHERE id = ?");
                $stmt->execute([$review_id]);
            }
            $_SESSION['success'] = 'Review deleted successfully!';
        } catch (PDOException $e) {
            $_SESSION['error'] = 'Error deleting review: ' . $e->getMessage();
        }
    }
    header('Location: reviews.php');
    exit();
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : 'all';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Check which field structure we're using
$stmt = $pdo->prepare("SHOW COLUMNS FROM reviews LIKE 'status'");
$stmt->execute();
$has_status_field = $stmt->fetch();

// Build query
$where_conditions = ["1=1"];
$params = [];

if ($status_filter === 'deleted') {
    if ($has_status_field) {
        $where_conditions[] = "r.status = 'deleted'";
    } else {
        $where_conditions[] = "r.is_approved = 0";
    }
} elseif ($status_filter === 'all') {
    // Show all non-deleted reviews
    if ($has_status_field) {
        $where_conditions[] = "(r.status IS NULL OR r.status != 'deleted')";
    } else {
        $where_conditions[] = "(r.is_approved IS NULL OR r.is_approved != 0)";
    }
}

if (!empty($search)) {
    $where_conditions[] = "(COALESCE(cp.business_name, '') LIKE ? OR COALESCE(cust.first_name, '') LIKE ? OR COALESCE(cust.last_name, '') LIKE ? OR COALESCE(r.review_text, '') LIKE ? OR CONCAT(COALESCE(cust.first_name, ''), ' ', COALESCE(cust.last_name, '')) LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

$where_clause = implode(' AND ', $where_conditions);

// Get reviews
try {
    $stmt = $pdo->prepare("
        SELECT r.*, 
               cp.business_name,
               cust.first_name, cust.last_name
        FROM reviews r
        LEFT JOIN contractor_profiles cp ON r.contractor_id = cp.user_id
        LEFT JOIN customer_profiles cust ON r.customer_id = cust.user_id
        WHERE $where_clause
        ORDER BY r.created_at DESC
    ");
    $stmt->execute($params);
    $reviews = $stmt->fetchAll();
} catch (PDOException $e) {
    $reviews = [];
    $error = 'Database error: ' . $e->getMessage();
}

// Get status counts
try {
    if ($has_status_field) {
        // Using status field
        $stmt = $pdo->query("
            SELECT
                CASE
                    WHEN status = 'deleted' THEN 'deleted'
                    ELSE 'active'
                END as status,
                COUNT(*) as count
            FROM reviews
            GROUP BY CASE
                WHEN status = 'deleted' THEN 'deleted'
                ELSE 'active'
            END
        ");
    } else {
        // Using is_approved field
        $stmt = $pdo->query("
            SELECT
                CASE
                    WHEN is_approved = 0 THEN 'deleted'
                    ELSE 'active'
                END as status,
                COUNT(*) as count
            FROM reviews
            GROUP BY CASE
                WHEN is_approved = 0 THEN 'deleted'
                ELSE 'active'
            END
        ");
    }
    $status_counts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

    // Calculate total active reviews
    $total_active = ($status_counts['active'] ?? 0);
    $total_deleted = ($status_counts['deleted'] ?? 0);
    $status_counts['all'] = $total_active;

} catch (PDOException $e) {
    $status_counts = ['all' => 0, 'deleted' => 0];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Reviews Management - Admin Panel</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF8C00;
            --accent-yellow: #FFD700;
            --light-gray: #F8F9FA;
            --medium-gray: #6C757D;
            --success-green: #28A745;
            --warning-orange: #FFC107;
            --info-blue: #17A2B8;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f5f6fa;
            color: var(--primary-dark);
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-header h3 {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }
        
        .sidebar-header p {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 15px rgba(197, 23, 46, 0.3);
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--accent-yellow), var(--accent-orange));
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
        }
        
        .page-title {
            font-weight: 700;
            margin: 0;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .page-subtitle {
            margin: 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .filters-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .status-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .status-tab {
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .status-tab.all {
            background: linear-gradient(135deg, var(--medium-gray), #5a6268);
            color: white;
        }
        
        .status-tab.pending {
            background: linear-gradient(135deg, var(--warning-orange), #e0a800);
            color: white;
        }
        
        .status-tab.approved {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            color: white;
        }
        
        .status-tab.deleted {
            background: linear-gradient(135deg, var(--primary-red), #bd2130);
            color: white;
        }
        
        .status-tab.active {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .review-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .review-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .review-header {
            display: flex;
            justify-content: between;
            align-items: start;
            margin-bottom: 1.5rem;
        }
        
        .review-info h5 {
            color: var(--primary-dark);
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .review-info p {
            color: var(--medium-gray);
            margin: 0;
        }
        
        .rating-display {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .rating-stars {
            color: var(--accent-yellow);
            margin-right: 0.5rem;
        }
        
        .rating-number {
            font-weight: 700;
            color: var(--primary-dark);
        }
        
        .review-text {
            background: var(--light-gray);
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-style: italic;
            color: var(--primary-dark);
        }
        
        .review-meta {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .review-date {
            color: var(--medium-gray);
            font-size: 0.9rem;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .status-badge.pending {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
        }
        
        .status-badge.approved {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }
        
        .status-badge.deleted {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .btn-action {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-approve {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            color: white;
        }
        
        .btn-delete {
            background: linear-gradient(135deg, var(--primary-red), #bd2130);
            color: white;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .empty-state i {
            font-size: 4rem;
            color: var(--medium-gray);
            margin-bottom: 1rem;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-shield-alt me-2"></i>Admin Panel</h3>
            <p>Brick & Click Management</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="contractors.php" class="nav-link">
                    <i class="fas fa-hard-hat"></i>Contractors
                </a>
            </div>
            <div class="nav-item">
                <a href="customers.php" class="nav-link">
                    <i class="fas fa-users"></i>Customers
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link active">
                    <i class="fas fa-star"></i>Reviews
                </a>
            </div>
            <div class="nav-item">
                <a href="payments.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payments
                </a>
            </div>
            <div class="nav-item">
                <a href="reports.php" class="nav-link">
                    <i class="fas fa-chart-bar"></i>Reports
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Reviews Management</h1>
            <p class="page-subtitle">Moderate and manage customer reviews</p>
        </div>

        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Filters -->
        <div class="filters-card">
            <div class="status-tabs">
                <a href="reviews.php" class="status-tab all <?php echo $status_filter === 'all' ? 'active' : ''; ?>">
                    All Reviews (<?php echo $status_counts['all'] ?? 0; ?>)
                </a>
                <a href="reviews.php?status=deleted" class="status-tab deleted <?php echo $status_filter === 'deleted' ? 'active' : ''; ?>">
                    Deleted Reviews (<?php echo $status_counts['deleted'] ?? 0; ?>)
                </a>
            </div>

            <form method="GET" class="row g-3">
                <input type="hidden" name="status" value="<?php echo htmlspecialchars($status_filter); ?>">
                <div class="col-md-8">
                    <input type="text" class="form-control" name="search" placeholder="Search by contractor name, customer name, or review text..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                </div>
            </form>
        </div>

        <!-- Reviews List -->
        <?php if (empty($reviews)): ?>
            <div class="empty-state">
                <i class="fas fa-star"></i>
                <h3>No reviews found</h3>
                <p class="text-muted">No reviews match your current filters.</p>
            </div>
        <?php else: ?>
            <?php foreach ($reviews as $review): ?>
                <div class="review-card">
                    <div class="review-header">
                        <div class="review-info">
                            <h5>Review for <?php echo htmlspecialchars($review['business_name'] ?? 'Unknown Contractor'); ?></h5>
                            <p>By: <?php echo htmlspecialchars(($review['first_name'] ?? '') . ' ' . ($review['last_name'] ?? '')); ?></p>
                        </div>
                        <?php if ($status_filter === 'deleted'): ?>
                            <span class="status-badge deleted">Deleted</span>
                        <?php endif; ?>
                    </div>

                    <div class="rating-display">
                        <div class="rating-stars">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star<?php echo $i <= $review['rating'] ? '' : '-o'; ?>"></i>
                            <?php endfor; ?>
                        </div>
                        <span class="rating-number"><?php echo $review['rating']; ?>/5</span>
                    </div>

                    <div class="review-text">
                        "<?php echo htmlspecialchars($review['review_text']); ?>"
                    </div>

                    <div class="review-meta">
                        <span class="review-date">
                            <i class="fas fa-calendar me-1"></i>
                            <?php echo date('M j, Y \a\t g:i A', strtotime($review['created_at'])); ?>
                        </span>
                    </div>

                    <?php if ($status_filter !== 'deleted'): ?>
                        <div class="action-buttons">
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="review_id" value="<?php echo $review['id']; ?>">
                                <button type="submit" name="delete_review" class="btn-action btn-delete" onclick="return confirm('Are you sure you want to delete this review? This action cannot be undone.')">
                                    <i class="fas fa-trash me-1"></i>Delete Review
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
