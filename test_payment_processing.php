<?php
session_start();
require_once 'config/database.php';

echo "<h2>Payment Processing Test</h2>";

try {
    // Check if project_payments table exists
    echo "<h3>1. Checking Project Payments Table</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'project_payments'");
    $pp_exists = $stmt->fetch();
    
    if ($pp_exists) {
        echo "<p style='color: green;'>✓ project_payments table exists</p>";
        
        // Show table structure
        $stmt = $pdo->query("DESCRIBE project_payments");
        $columns = $stmt->fetchAll();
        
        echo "<h4>Table Structure:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ project_payments table does not exist</p>";
        echo "<p><a href='fix_database_issues.php' style='background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Create Table</a></p>";
        exit;
    }
    
    echo "<h3>2. Testing Payment Session Setup</h3>";
    
    // Get test data
    $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' AND status = 'approved' LIMIT 1");
    $customer_id = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT id FROM quote_requests WHERE customer_id = ? LIMIT 1");
    $stmt->execute([$customer_id]);
    $quote_id = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT * FROM quote_responses WHERE quote_request_id = ? LIMIT 1");
    $stmt->execute([$quote_id]);
    $quote_response = $stmt->fetch();
    
    if ($quote_response) {
        echo "<p>Found test quote response ID: " . $quote_response['id'] . "</p>";
        echo "<p>Quoted amount: Rs. " . number_format($quote_response['quoted_amount'], 2) . "</p>";
        
        // Set up payment session data
        $_SESSION['user_id'] = $customer_id;
        $_SESSION['user_type'] = 'customer';
        $_SESSION['payment_data'] = [
            'quote_response_id' => $quote_response['id'],
            'amount' => $quote_response['quoted_amount'] * 0.3, // 30% down payment
            'payment_type' => 'down_payment',
            'payment_method' => 'card'
        ];
        
        echo "<p style='color: green;'>✓ Payment session data set up</p>";
        echo "<p>Down payment amount: Rs. " . number_format($_SESSION['payment_data']['amount'], 2) . "</p>";
        
        echo "<h3>3. Test Payment Processing</h3>";
        echo "<p><a href='customer/payment_portal.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Open Payment Portal</a></p>";
        
        echo "<h3>4. Simulate Direct Payment Processing</h3>";
        
        // Simulate payment processing
        $quote_response_id = $quote_response['id'];
        $amount = $_SESSION['payment_data']['amount'];
        $payment_type = 'down_payment';
        $payment_method = 'card';
        
        // Generate transaction ID
        $transaction_id = 'TEST_TXN_' . time() . '_' . rand(1000, 9999);
        
        // Payment details
        $payment_details_json = json_encode([
            'method' => 'card',
            'last_four' => '4321',
            'test_payment' => true
        ]);
        
        try {
            $pdo->beginTransaction();
            
            // Insert payment record
            $stmt = $pdo->prepare("
                INSERT INTO project_payments (
                    quote_response_id, customer_id, contractor_id, amount, 
                    payment_type, payment_status, payment_method, transaction_id, 
                    payment_date, payment_details
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $quote_response_id,
                $customer_id,
                $quote_response['contractor_id'],
                $amount,
                $payment_type,
                'completed',
                $payment_method,
                $transaction_id,
                date('Y-m-d H:i:s'),
                $payment_details_json
            ]);
            
            $payment_id = $pdo->lastInsertId();
            
            $pdo->commit();
            
            echo "<p style='color: green;'>✓ Test payment processed successfully!</p>";
            echo "<p>Payment ID: $payment_id</p>";
            echo "<p>Transaction ID: $transaction_id</p>";
            echo "<p>Amount: Rs. " . number_format($amount, 2) . "</p>";
            
            echo "<p><a href='customer/payment_success.php?payment_id=$payment_id' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Payment Success Page</a></p>";
            
        } catch (PDOException $e) {
            $pdo->rollBack();
            echo "<p style='color: red;'>✗ Payment processing failed: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>No test quote response found</p>";
        echo "<p><a href='fix_database_issues.php' style='background: #ffc107; color: #212529; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Create Test Data</a></p>";
    }
    
    echo "<h3>5. Check Existing Payments</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM project_payments");
    $payment_count = $stmt->fetchColumn();
    
    echo "<p>Total payments in database: $payment_count</p>";
    
    if ($payment_count > 0) {
        echo "<h4>Recent Payments:</h4>";
        $stmt = $pdo->query("
            SELECT pp.*, cp.business_name, qr.title 
            FROM project_payments pp
            LEFT JOIN quote_responses qres ON pp.quote_response_id = qres.id
            LEFT JOIN quote_requests qr ON qres.quote_request_id = qr.id
            LEFT JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
            ORDER BY pp.created_at DESC 
            LIMIT 5
        ");
        $payments = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Project</th><th>Contractor</th><th>Amount</th><th>Status</th><th>Date</th></tr>";
        foreach ($payments as $payment) {
            echo "<tr>";
            echo "<td>" . $payment['id'] . "</td>";
            echo "<td>" . htmlspecialchars($payment['title'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($payment['business_name'] ?? 'N/A') . "</td>";
            echo "<td>Rs. " . number_format($payment['amount'], 2) . "</td>";
            echo "<td>" . $payment['payment_status'] . "</td>";
            echo "<td>" . $payment['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>
