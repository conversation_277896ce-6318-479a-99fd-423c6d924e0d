<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔍 Debug General Quote Issue</h2>";

try {
    // Step 1: Check service categories
    echo "<h3>Step 1: Service Categories</h3>";
    $stmt = $pdo->query("SELECT * FROM service_categories ORDER BY id");
    $categories = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name (EN)</th><th>Name (SI)</th><th>Name (TA)</th></tr>";
    foreach ($categories as $cat) {
        echo "<tr>";
        echo "<td>" . $cat['id'] . "</td>";
        echo "<td>" . htmlspecialchars($cat['name_en']) . "</td>";
        echo "<td>" . htmlspecialchars($cat['name_si'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($cat['name_ta'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 2: Check contractors and their service types
    echo "<h3>Step 2: Contractors and Their Services</h3>";
    $stmt = $pdo->query("
        SELECT u.id, u.email, cp.business_name, cp.service_types, cp.service_areas, u.status
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor'
        ORDER BY u.id
    ");
    $contractors = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr><th>ID</th><th>Business Name</th><th>Status</th><th>Service Types</th><th>Service Areas</th></tr>";
    foreach ($contractors as $contractor) {
        echo "<tr>";
        echo "<td>" . $contractor['id'] . "</td>";
        echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
        echo "<td>" . $contractor['status'] . "</td>";
        echo "<td>" . htmlspecialchars($contractor['service_types']) . "</td>";
        echo "<td>" . htmlspecialchars($contractor['service_areas']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 3: Check recent quote requests
    echo "<h3>Step 3: Recent Quote Requests</h3>";
    $stmt = $pdo->query("
        SELECT qr.*, sc.name_en as service_name, cp.first_name, cp.last_name
        FROM quote_requests qr
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        LEFT JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        ORDER BY qr.created_at DESC
        LIMIT 10
    ");
    $quotes = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr><th>ID</th><th>Title</th><th>Service</th><th>District</th><th>Specific Contractor</th><th>Status</th><th>Created</th></tr>";
    foreach ($quotes as $quote) {
        echo "<tr>";
        echo "<td>" . $quote['id'] . "</td>";
        echo "<td>" . htmlspecialchars($quote['title']) . "</td>";
        echo "<td>" . htmlspecialchars($quote['service_name'] ?? 'Unknown') . " (ID: " . $quote['service_category_id'] . ")</td>";
        echo "<td>" . htmlspecialchars($quote['district']) . "</td>";
        echo "<td>" . ($quote['specific_contractor_id'] ? $quote['specific_contractor_id'] : 'General') . "</td>";
        echo "<td>" . $quote['status'] . "</td>";
        echo "<td>" . $quote['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 4: Test the matching logic for the most recent quote
    if (!empty($quotes)) {
        $latest_quote = $quotes[0];
        echo "<h3>Step 4: Test Matching Logic for Latest Quote</h3>";
        echo "<p><strong>Latest Quote:</strong> " . htmlspecialchars($latest_quote['title']) . "</p>";
        echo "<p><strong>Service Category ID:</strong> " . $latest_quote['service_category_id'] . "</p>";
        echo "<p><strong>District:</strong> " . $latest_quote['district'] . "</p>";
        echo "<p><strong>Is General Quote:</strong> " . ($latest_quote['specific_contractor_id'] ? 'No' : 'Yes') . "</p>";
        
        if (!$latest_quote['specific_contractor_id']) {
            echo "<h4>Testing which contractors should see this quote:</h4>";
            
            foreach ($contractors as $contractor) {
                if ($contractor['status'] !== 'approved') {
                    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; background: #f8f8f8;'>";
                    echo "<strong>" . htmlspecialchars($contractor['business_name']) . "</strong> - ❌ Not approved (Status: " . $contractor['status'] . ")";
                    echo "</div>";
                    continue;
                }
                
                $service_areas = json_decode($contractor['service_areas'], true) ?: [];
                $service_types = json_decode($contractor['service_types'], true) ?: [];
                
                $has_service = in_array((int)$latest_quote['service_category_id'], $service_types) ||
                              in_array((string)$latest_quote['service_category_id'], $service_types);
                $has_area = in_array($latest_quote['district'], $service_areas);
                
                echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
                echo "<strong>" . htmlspecialchars($contractor['business_name']) . "</strong><br>";
                echo "Service Types: " . htmlspecialchars($contractor['service_types']) . "<br>";
                echo "Service Areas: " . htmlspecialchars($contractor['service_areas']) . "<br>";
                echo "Has Service (" . $latest_quote['service_category_id'] . "): " . ($has_service ? '✅ Yes' : '❌ No') . "<br>";
                echo "Has Area (" . $latest_quote['district'] . "): " . ($has_area ? '✅ Yes' : '❌ No') . "<br>";
                echo "<strong>Should See Quote: " . ($has_service && $has_area ? '✅ YES' : '❌ NO') . "</strong>";
                echo "</div>";
            }
        }
    }
    
    // Step 5: Check notifications for the latest quote
    if (!empty($quotes)) {
        $latest_quote = $quotes[0];
        echo "<h3>Step 5: Check Notifications for Latest Quote</h3>";
        
        $stmt = $pdo->prepare("
            SELECT n.*, u.email, cp.business_name
            FROM notifications n
            JOIN users u ON n.user_id = u.id
            LEFT JOIN contractor_profiles cp ON u.id = cp.user_id
            WHERE n.related_id = ? AND n.type = 'quote_received'
            ORDER BY n.created_at DESC
        ");
        $stmt->execute([$latest_quote['id']]);
        $notifications = $stmt->fetchAll();
        
        if (empty($notifications)) {
            echo "<p style='color: red;'>❌ No notifications found for this quote request!</p>";
        } else {
            echo "<p style='color: green;'>✅ Found " . count($notifications) . " notifications:</p>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Contractor</th><th>Email</th><th>Title</th><th>Message</th><th>Created</th></tr>";
            foreach ($notifications as $notif) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($notif['business_name']) . "</td>";
                echo "<td>" . htmlspecialchars($notif['email']) . "</td>";
                echo "<td>" . htmlspecialchars($notif['title']) . "</td>";
                echo "<td>" . htmlspecialchars($notif['message']) . "</td>";
                echo "<td>" . $notif['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // Step 6: Test the exact query from process_quote_request.php
    if (!empty($quotes)) {
        $latest_quote = $quotes[0];
        echo "<h3>Step 6: Test Exact Query from process_quote_request.php</h3>";
        
        $district = $latest_quote['district'];
        $service_category_id = $latest_quote['service_category_id'];
        
        echo "<p><strong>Testing with:</strong> District = '$district', Service = $service_category_id</p>";
        
        // Use the exact same query as in process_quote_request.php
        $stmt = $pdo->prepare("
            SELECT u.id, cp.business_name, cp.contact_person, cp.service_areas, cp.service_types
            FROM users u
            JOIN contractor_profiles cp ON u.id = cp.user_id
            WHERE u.status = 'approved'
            AND u.user_type = 'contractor'
            AND cp.service_areas IS NOT NULL
            AND cp.service_types IS NOT NULL
            AND (
                JSON_CONTAINS(cp.service_areas, ?)
                OR cp.service_areas LIKE ?
            )
            AND (
                JSON_CONTAINS(cp.service_types, ?)
                OR cp.service_types LIKE ?
            )
        ");

        $district_json = json_encode($district);
        $service_json = json_encode($service_category_id);
        $district_like = "%\"$district\"%";
        $service_like = "%\"$service_category_id\"%";

        echo "<p><strong>Query parameters:</strong></p>";
        echo "<ul>";
        echo "<li>district_json: " . htmlspecialchars($district_json) . "</li>";
        echo "<li>district_like: " . htmlspecialchars($district_like) . "</li>";
        echo "<li>service_json: " . htmlspecialchars($service_json) . "</li>";
        echo "<li>service_like: " . htmlspecialchars($service_like) . "</li>";
        echo "</ul>";

        $stmt->execute([
            $district_json,
            $district_like,
            $service_json,
            $service_like
        ]);

        $potential_contractors = $stmt->fetchAll();
        
        echo "<p><strong>SQL Query returned:</strong> " . count($potential_contractors) . " contractors</p>";
        
        if (empty($potential_contractors)) {
            echo "<p style='color: red;'>❌ No contractors found by SQL query!</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Business Name</th><th>Service Areas</th><th>Service Types</th></tr>";
            foreach ($potential_contractors as $contractor) {
                echo "<tr>";
                echo "<td>" . $contractor['id'] . "</td>";
                echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
                echo "<td>" . htmlspecialchars($contractor['service_areas']) . "</td>";
                echo "<td>" . htmlspecialchars($contractor['service_types']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Apply PHP filtering (same as process_quote_request.php)
        $contractors_to_notify = [];
        foreach ($potential_contractors as $contractor) {
            $service_areas = json_decode($contractor['service_areas'], true);
            $service_types = json_decode($contractor['service_types'], true);

            $has_area = false;
            $has_service = false;

            // Check service areas
            if (is_array($service_areas)) {
                $has_area = in_array($district, $service_areas);
            }

            // Check service types (handle both string and integer formats)
            if (is_array($service_types)) {
                $has_service = in_array($service_category_id, $service_types) ||
                              in_array((string)$service_category_id, $service_types);
            }

            if ($has_area && $has_service) {
                $contractors_to_notify[] = [
                    'id' => $contractor['id'],
                    'business_name' => $contractor['business_name'],
                    'contact_person' => $contractor['contact_person']
                ];
            }
        }
        
        echo "<p><strong>After PHP filtering:</strong> " . count($contractors_to_notify) . " contractors should be notified</p>";
        
        if (empty($contractors_to_notify)) {
            echo "<p style='color: red;'>❌ No contractors passed PHP filtering!</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Business Name</th><th>Contact Person</th></tr>";
            foreach ($contractors_to_notify as $contractor) {
                echo "<tr>";
                echo "<td>" . $contractor['id'] . "</td>";
                echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
                echo "<td>" . htmlspecialchars($contractor['contact_person']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
