<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

// Handle search and filters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$payment_type_filter = isset($_GET['payment_type']) ? $_GET['payment_type'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';

// Build query conditions
$conditions = [];
$params = [];

if (!empty($search)) {
    $conditions[] = "(cp.business_name LIKE ? OR cp.contact_person LIKE ? OR cust.first_name LIKE ? OR cust.last_name LIKE ? OR CONCAT(cust.first_name, ' ', cust.last_name) LIKE ? OR qr.title LIKE ? OR pp.transaction_id LIKE ? OR u_contractor.email LIKE ? OR u_customer.email LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param, $search_param, $search_param, $search_param, $search_param]);
}

if (!empty($status_filter)) {
    $conditions[] = "pp.payment_status = ?";
    $params[] = $status_filter;
}

if (!empty($payment_type_filter)) {
    $conditions[] = "pp.payment_type = ?";
    $params[] = $payment_type_filter;
}

if (!empty($date_from)) {
    $conditions[] = "DATE(pp.payment_date) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $conditions[] = "DATE(pp.payment_date) <= ?";
    $params[] = $date_to;
}

$where_clause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';

try {
    // Get payment statistics
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_payments,
            SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END) as total_completed_amount,
            SUM(CASE WHEN payment_status = 'pending' THEN amount ELSE 0 END) as total_pending_amount,
            COUNT(CASE WHEN payment_status = 'completed' THEN 1 END) as completed_payments,
            COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_payments,
            COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_payments
        FROM project_payments
    ");
    $stats = $stmt->fetch();
    
    // Get payments with related information
    $stmt = $pdo->prepare("
        SELECT pp.*, 
               qr.title as project_title,
               qr.description as project_description,
               qr.estimated_budget,
               qr.location,
               qr.district,
               cp.business_name,
               cp.contact_person,
               cp.phone as contractor_phone,
               cust.first_name as customer_first_name,
               cust.last_name as customer_last_name,
               cust.phone as customer_phone,
               qres.quoted_amount,
               qres.estimated_timeline,
               u_contractor.email as contractor_email,
               u_customer.email as customer_email
        FROM project_payments pp
        LEFT JOIN quote_responses qres ON pp.quote_response_id = qres.id
        LEFT JOIN quote_requests qr ON qres.quote_request_id = qr.id
        LEFT JOIN contractor_profiles cp ON pp.contractor_id = cp.user_id
        LEFT JOIN customer_profiles cust ON pp.customer_id = cust.user_id
        LEFT JOIN users u_contractor ON pp.contractor_id = u_contractor.id
        LEFT JOIN users u_customer ON pp.customer_id = u_customer.id
        $where_clause
        ORDER BY pp.created_at DESC
        LIMIT 50
    ");
    $stmt->execute($params);
    $payments = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = 'Database error: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Payment Management - Admin Panel</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #C5172E;
            --primary-dark: #06202B;
            --secondary-color: #FFA500;
            --accent-color: #FFD700;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f5f6fa;
            color: var(--primary-dark);
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-header h3 {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }
        
        .sidebar-header p {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }
        
        .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 15px rgba(197, 23, 46, 0.3);
        }
        
        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            border-left: 5px solid var(--primary-color);
        }
        
        .page-title {
            color: var(--primary-dark);
            font-weight: 700;
            font-size: 2rem;
            margin: 0;
        }
        
        .page-subtitle {
            color: var(--text-light);
            margin: 0.5rem 0 0 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card.total { border-left-color: var(--info-color); }
        .stat-card.completed { border-left-color: var(--success-color); }
        .stat-card.pending { border-left-color: var(--warning-color); }
        .stat-card.failed { border-left-color: var(--danger-color); }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: var(--text-light);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .filters-card {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .payments-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .table-header {
            background: var(--primary-dark);
            color: white;
            padding: 1.5rem;
        }
        
        .table-title {
            margin: 0;
            font-weight: 600;
        }
        
        .table {
            margin: 0;
        }
        
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: var(--primary-dark);
            padding: 1rem;
        }
        
        .table td {
            padding: 1rem;
            border-color: #f1f3f4;
            vertical-align: middle;
        }
        
        .status-badge {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-completed { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .status-refunded { background: #d1ecf1; color: #0c5460; }
        
        .payment-type-badge {
            padding: 0.3rem 0.6rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .type-down_payment { background: #e3f2fd; color: #1565c0; }
        .type-milestone { background: #f3e5f5; color: #7b1fa2; }
        .type-final_payment { background: #e8f5e8; color: #2e7d32; }
        
        .amount-display {
            font-weight: 600;
            color: var(--success-color);
        }
        
        .btn-custom {
            background: var(--primary-color);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            background: #a91429;
            color: white;
            transform: translateY(-2px);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(197, 23, 46, 0.25);
        }
        
        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(197, 23, 46, 0.25);
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-shield-alt me-2"></i>Admin Panel</h3>
            <p>Brick & Click Management</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="contractors.php" class="nav-link">
                    <i class="fas fa-hard-hat"></i>Contractors
                </a>
            </div>
            <div class="nav-item">
                <a href="customers.php" class="nav-link">
                    <i class="fas fa-users"></i>Customers
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews
                </a>
            </div>
            <div class="nav-item">
                <a href="payments.php" class="nav-link active">
                    <i class="fas fa-credit-card"></i>Payments
                </a>
            </div>
            <div class="nav-item">
                <a href="reports.php" class="nav-link">
                    <i class="fas fa-chart-bar"></i>Reports
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-credit-card me-3"></i>Payment Management
            </h1>
            <p class="page-subtitle">Monitor and manage all payment transactions between customers and contractors</p>
        </div>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-value text-info"><?php echo number_format($stats['total_payments'] ?? 0); ?></div>
                <div class="stat-label">Total Payments</div>
            </div>
            <div class="stat-card completed">
                <div class="stat-value text-success">Rs. <?php echo number_format($stats['total_completed_amount'] ?? 0); ?></div>
                <div class="stat-label">Completed Amount</div>
            </div>
            <div class="stat-card pending">
                <div class="stat-value text-warning">Rs. <?php echo number_format($stats['total_pending_amount'] ?? 0); ?></div>
                <div class="stat-label">Pending Amount</div>
            </div>
            <div class="stat-card failed">
                <div class="stat-value text-danger"><?php echo number_format($stats['failed_payments'] ?? 0); ?></div>
                <div class="stat-label">Failed Payments</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-card">
            <h5 class="mb-3"><i class="fas fa-filter me-2"></i>Filters</h5>
            <form method="GET" action="">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="Business name, customer, project...">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                            <option value="refunded" <?php echo $status_filter === 'refunded' ? 'selected' : ''; ?>>Refunded</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="payment_type" class="form-label">Payment Type</label>
                        <select class="form-select" id="payment_type" name="payment_type">
                            <option value="">All Types</option>
                            <option value="down_payment" <?php echo $payment_type_filter === 'down_payment' ? 'selected' : ''; ?>>Down Payment</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="date_from" name="date_from"
                               value="<?php echo htmlspecialchars($date_from); ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="date_to" name="date_to"
                               value="<?php echo htmlspecialchars($date_to); ?>">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-custom w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Payments Table -->
        <div class="payments-table">
            <div class="table-header">
                <h5 class="table-title">
                    <i class="fas fa-list me-2"></i>Payment Transactions
                    <span class="badge bg-light text-dark ms-2"><?php echo count($payments); ?> records</span>
                </h5>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Payment ID</th>
                            <th>Project</th>
                            <th>Customer</th>
                            <th>Contractor</th>
                            <th>Amount</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Payment Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($payments)): ?>
                            <?php foreach ($payments as $payment): ?>
                                <tr>
                                    <td>
                                        <strong>#<?php echo $payment['id']; ?></strong>
                                        <?php if (!empty($payment['transaction_id'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($payment['transaction_id']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($payment['project_title'] ?? 'N/A'); ?></strong>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($payment['location'] ?? ''); ?>, <?php echo htmlspecialchars($payment['district'] ?? ''); ?></small>
                                        <?php if (!empty($payment['estimated_budget'])): ?>
                                            <br><small class="text-info">Budget: Rs. <?php echo number_format($payment['estimated_budget']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars(($payment['customer_first_name'] ?? 'Unknown') . ' ' . ($payment['customer_last_name'] ?? 'Customer')); ?></strong>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($payment['customer_email'] ?? ''); ?></small>
                                        <?php if (!empty($payment['customer_phone'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($payment['customer_phone']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($payment['business_name'] ?? 'Unknown Business'); ?></strong>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($payment['contact_person'] ?? ''); ?></small>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($payment['contractor_email'] ?? ''); ?></small>
                                    </td>
                                    <td>
                                        <span class="amount-display">Rs. <?php echo number_format($payment['amount']); ?></span>
                                        <?php if (!empty($payment['quoted_amount'])): ?>
                                            <br><small class="text-muted">of Rs. <?php echo number_format($payment['quoted_amount']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="payment-type-badge type-<?php echo $payment['payment_type']; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $payment['payment_type'])); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo $payment['payment_status']; ?>">
                                            <?php echo ucfirst($payment['payment_status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($payment['payment_date'])): ?>
                                            <strong><?php echo date('M j, Y', strtotime($payment['payment_date'])); ?></strong>
                                            <br><small class="text-muted"><?php echo date('g:i A', strtotime($payment['payment_date'])); ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">Not paid</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical btn-group-sm">
                                            <button class="btn btn-outline-primary btn-sm" onclick="viewPaymentDetails(<?php echo $payment['id']; ?>)">
                                                <i class="fas fa-eye"></i> View
                                            </button>
                                            <?php if ($payment['payment_status'] === 'pending'): ?>
                                                <button class="btn btn-outline-success btn-sm" onclick="updatePaymentStatus(<?php echo $payment['id']; ?>, 'completed')">
                                                    <i class="fas fa-check"></i> Mark Paid
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No payments found</h5>
                                    <p class="text-muted">No payment transactions match your current filters.</p>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Payment Details Modal -->
    <div class="modal fade" id="paymentDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Payment Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="paymentDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function viewPaymentDetails(paymentId) {
            // Load payment details via AJAX
            fetch(`payment_details.php?id=${paymentId}`)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('paymentDetailsContent').innerHTML = data;
                    new bootstrap.Modal(document.getElementById('paymentDetailsModal')).show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading payment details');
                });
        }

        function updatePaymentStatus(paymentId, status) {
            if (confirm(`Are you sure you want to mark this payment as ${status}?`)) {
                fetch('update_payment_status.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        payment_id: paymentId,
                        status: status
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error updating payment status: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error updating payment status');
                });
            }
        }
    </script>
</body>
</html>
