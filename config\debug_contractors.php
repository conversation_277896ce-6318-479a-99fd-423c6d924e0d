<?php
require_once 'database.php';

echo "<h2>🔍 Contractor Database Debug</h2>";

try {
    // Check total contractors
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM contractor_profiles cp JOIN users u ON cp.user_id = u.id WHERE u.status = 'approved'");
    $stmt->execute();
    $total = $stmt->fetchColumn();
    
    echo "<p><strong>Total Approved Contractors:</strong> $total</p>";
    
    // Check recent contractors
    $stmt = $pdo->prepare("
        SELECT cp.business_name, cp.service_types, cp.service_areas, u.email, u.created_at 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        ORDER BY u.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $contractors = $stmt->fetchAll();
    
    echo "<h3>Recent Contractors:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Business Name</th><th>Email</th><th>Service Types</th><th>Service Areas</th><th>Created</th></tr>";
    
    foreach ($contractors as $contractor) {
        $services = json_decode($contractor['service_types'], true);
        $areas = json_decode($contractor['service_areas'], true);
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($contractor['business_name']) . "</td>";
        echo "<td>" . htmlspecialchars($contractor['email']) . "</td>";
        echo "<td>" . (is_array($services) ? implode(', ', $services) : 'None') . "</td>";
        echo "<td>" . (is_array($areas) ? implode(', ', $areas) : 'None') . "</td>";
        echo "<td>" . $contractor['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check service categories
    echo "<h3>Service Categories:</h3>";
    $stmt = $pdo->prepare("SELECT id, name_en FROM service_categories WHERE is_active = 1");
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    echo "<ul>";
    foreach ($categories as $cat) {
        echo "<li>ID: {$cat['id']} - {$cat['name_en']}</li>";
    }
    echo "</ul>";
    
    // Test search query
    echo "<h3>Test Search Query:</h3>";
    $test_category = 1; // House Construction
    $test_location = 'Colombo';
    
    $stmt = $pdo->prepare("
        SELECT cp.business_name, cp.service_types, cp.service_areas 
        FROM contractor_profiles cp 
        JOIN users u ON cp.user_id = u.id 
        WHERE u.status = 'approved' 
        AND (JSON_CONTAINS(cp.service_types, ?) OR JSON_CONTAINS(cp.service_areas, ?))
        LIMIT 5
    ");
    $stmt->execute([json_encode($test_category), json_encode($test_location)]);
    $test_results = $stmt->fetchAll();
    
    echo "<p>Testing search for Category ID: $test_category and Location: $test_location</p>";
    echo "<p>Results found: " . count($test_results) . "</p>";
    
    foreach ($test_results as $result) {
        echo "<p>- " . htmlspecialchars($result['business_name']) . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
