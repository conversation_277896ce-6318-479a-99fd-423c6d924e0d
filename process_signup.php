<?php
session_start();
require_once 'config/database.php';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: signup.php');
    exit();
}

$user_type = $_POST['user_type'];
$email = trim($_POST['email']);
$password = $_POST['password'];
$confirm_password = $_POST['confirm_password'];

// Basic validation
if (empty($email) || empty($password) || empty($confirm_password)) {
    $_SESSION['error'] = 'All required fields must be filled.';
    header('Location: signup.php');
    exit();
}

if ($password !== $confirm_password) {
    $_SESSION['error'] = 'Passwords do not match.';
    header('Location: signup.php');
    exit();
}

if (strlen($password) < 6) {
    $_SESSION['error'] = 'Password must be at least 6 characters long.';
    header('Location: signup.php');
    exit();
}

// Check if email already exists
try {
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->fetch()) {
        $_SESSION['error'] = 'Email address is already registered.';
        header('Location: signup.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error. Please try again.';
    header('Location: signup.php');
    exit();
}

// Hash password
$hashed_password = password_hash($password, PASSWORD_DEFAULT);

// Set status based on user type
$status = ($user_type === 'contractor') ? 'pending' : 'approved';

try {
    $pdo->beginTransaction();
    
    // Insert user
    $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status) VALUES (?, ?, ?, ?)");
    $stmt->execute([$email, $hashed_password, $user_type, $status]);
    $user_id = $pdo->lastInsertId();
    
    if ($user_type === 'customer') {
        // Insert customer profile
        $first_name = trim($_POST['first_name']);
        $last_name = trim($_POST['last_name']);
        $phone = trim($_POST['phone']);
        $district = $_POST['district'];
        $address = trim($_POST['address']);
        $language_preference = $_POST['language_preference'];
        
        $stmt = $pdo->prepare("INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district, address, language_preference) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$user_id, $first_name, $last_name, $phone, $district, $address, $language_preference]);
        
        $pdo->commit();
        
        // Set session and redirect to customer dashboard
        $_SESSION['user_id'] = $user_id;
        $_SESSION['user_type'] = 'customer';
        $_SESSION['user_email'] = $email;
        $_SESSION['success'] = 'Account created successfully! Welcome to Brick & Click.';
        
        header('Location: customer/dashboard.php');
        exit();
        
    } else if ($user_type === 'contractor') {
        // Handle file uploads
        $upload_dir = 'uploads/contractor_documents/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        
        $cida_document = null;
        $license_document = null;
        
        // Upload CIDA document
        if (isset($_FILES['cida_document']) && $_FILES['cida_document']['error'] === UPLOAD_ERR_OK) {
            $file_extension = pathinfo($_FILES['cida_document']['name'], PATHINFO_EXTENSION);
            $cida_document = 'cida_' . $user_id . '_' . time() . '.' . $file_extension;
            move_uploaded_file($_FILES['cida_document']['tmp_name'], $upload_dir . $cida_document);
        }
        
        // Upload license document
        if (isset($_FILES['license_document']) && $_FILES['license_document']['error'] === UPLOAD_ERR_OK) {
            $file_extension = pathinfo($_FILES['license_document']['name'], PATHINFO_EXTENSION);
            $license_document = 'license_' . $user_id . '_' . time() . '.' . $file_extension;
            move_uploaded_file($_FILES['license_document']['tmp_name'], $upload_dir . $license_document);
        }
        
        // Insert contractor profile
        $business_name = trim($_POST['business_name']);
        $contact_person = trim($_POST['contact_person']);
        $phone = trim($_POST['phone']);
        $business_address = trim($_POST['business_address']);
        $cida_registration = trim($_POST['cida_registration']);
        $cida_grade = $_POST['cida_grade'];
        $business_description = trim($_POST['business_description']);
        $website = trim($_POST['website']);
        $language_preference = $_POST['language_preference'];
        
        // Convert arrays to JSON
        $service_types = json_encode($_POST['service_types']);
        $service_areas = json_encode($_POST['service_areas']);
        
        $stmt = $pdo->prepare("INSERT INTO contractor_profiles (user_id, business_name, contact_person, phone, business_address, service_areas, service_types, cida_registration, cida_grade, cida_document, license_document, business_description, website, language_preference) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$user_id, $business_name, $contact_person, $phone, $business_address, $service_areas, $service_types, $cida_registration, $cida_grade, $cida_document, $license_document, $business_description, $website, $language_preference]);
        
        // Create notification for admin
        $stmt = $pdo->prepare("INSERT INTO notifications (user_id, title, message, type) SELECT id, 'New Contractor Registration', 'A new contractor has registered and requires approval: $business_name', 'verification_update' FROM users WHERE user_type = 'admin'");
        $stmt->execute();
        
        $pdo->commit();
        
        $_SESSION['success'] = 'Registration submitted successfully! Your account will be reviewed by our admin team. You will receive an email notification once approved.';
        header('Location: signup.php');
        exit();
    }
    
} catch (PDOException $e) {
    $pdo->rollBack();
    $_SESSION['error'] = 'Registration failed. Please try again.';
    header('Location: signup.php');
    exit();
}
?>
