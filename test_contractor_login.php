<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔐 Contractor Login Test</h2>";

// Check current session
echo "<h3>Current Session Status</h3>";
if (isset($_SESSION['user_id'])) {
    echo "<p>✅ Logged in as User ID: " . $_SESSION['user_id'] . "</p>";
    echo "<p>✅ User Type: " . ($_SESSION['user_type'] ?? 'not set') . "</p>";
    echo "<p><a href='contractor/dashboard.php'>Go to Contractor Dashboard</a></p>";
    echo "<p><a href='?logout=1'>Logout</a></p>";
} else {
    echo "<p>❌ Not logged in</p>";
    
    // Auto-login the contractor for testing
    if (isset($_GET['auto_login'])) {
        try {
            $stmt = $pdo->prepare("SELECT * FROM users WHERE email = '<EMAIL>' AND user_type = 'contractor'");
            $stmt->execute();
            $contractor = $stmt->fetch();
            
            if ($contractor) {
                $_SESSION['user_id'] = $contractor['id'];
                $_SESSION['user_type'] = $contractor['user_type'];
                $_SESSION['email'] = $contractor['email'];
                
                echo "<p>✅ Auto-logged in as contractor</p>";
                echo "<p><a href='contractor/dashboard.php'>Go to Contractor Dashboard</a></p>";
            } else {
                echo "<p>❌ Contractor not found</p>";
            }
        } catch (PDOException $e) {
            echo "<p>❌ Error: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p><a href='?auto_login=1'>Auto-Login as Contractor</a></p>";
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: test_contractor_login.php');
    exit;
}

// Show available contractors
echo "<h3>Available Contractors</h3>";
try {
    $stmt = $pdo->prepare("
        SELECT u.id, u.email, u.status, cp.business_name 
        FROM users u 
        LEFT JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor'
    ");
    $stmt->execute();
    $contractors = $stmt->fetchAll();
    
    if (!empty($contractors)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Email</th><th>Business Name</th><th>Status</th><th>Action</th></tr>";
        foreach ($contractors as $contractor) {
            echo "<tr>";
            echo "<td>" . $contractor['id'] . "</td>";
            echo "<td>" . htmlspecialchars($contractor['email']) . "</td>";
            echo "<td>" . htmlspecialchars($contractor['business_name'] ?? 'No profile') . "</td>";
            echo "<td>" . $contractor['status'] . "</td>";
            echo "<td><a href='?login_as=" . $contractor['id'] . "'>Login as this contractor</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No contractors found</p>";
        echo "<p><a href='config/add_sample_contractor.php'>Add Sample Contractor</a></p>";
    }
} catch (PDOException $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle login as specific contractor
if (isset($_GET['login_as'])) {
    $contractor_id = (int)$_GET['login_as'];
    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND user_type = 'contractor'");
        $stmt->execute([$contractor_id]);
        $contractor = $stmt->fetch();
        
        if ($contractor) {
            $_SESSION['user_id'] = $contractor['id'];
            $_SESSION['user_type'] = $contractor['user_type'];
            $_SESSION['email'] = $contractor['email'];
            
            echo "<p>✅ Logged in as: " . htmlspecialchars($contractor['email']) . "</p>";
            echo "<p><a href='contractor/dashboard.php'>Go to Contractor Dashboard</a></p>";
        } else {
            echo "<p>❌ Contractor not found</p>";
        }
    } catch (PDOException $e) {
        echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Contractor Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        table { margin: 20px 0; }
        th, td { padding: 8px; text-align: left; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
</body>
</html>
