<?php
require_once 'database.php';

try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Insert sample customer user
    $stmt = $pdo->prepare("
        INSERT INTO users (email, password, user_type, status, created_at, updated_at) 
        VALUES (?, ?, 'customer', 'approved', NOW(), NOW())
    ");
    $hashed_password = password_hash('password', PASSWORD_DEFAULT);
    $stmt->execute(['<EMAIL>', $hashed_password]);
    $customer_user_id = $pdo->lastInsertId();
    
    // Insert customer profile
    $stmt = $pdo->prepare("
        INSERT INTO customer_profiles (
            user_id, first_name, last_name, phone, district, address, 
            language_preference, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 'en', NOW(), NOW())
    ");
    $stmt->execute([
        $customer_user_id,
        'Yoshara',
        'Silva',
        '+94 77 987 6543',
        'Colombo',
        'No. 123, Main Street, Colombo 05, Sri Lanka'
    ]);
    
    // Get contractor ID for favorites
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = '<EMAIL>' AND user_type = 'contractor'");
    $stmt->execute();
    $contractor_id = $stmt->fetchColumn();
    
    if ($contractor_id) {
        // Add contractor to customer's favorites
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO customer_favorites (customer_id, contractor_id, created_at) 
            VALUES (?, ?, NOW())
        ");
        $stmt->execute([$customer_user_id, $contractor_id]);
    }
    
    // Add some sample quote requests
    $stmt = $pdo->prepare("SELECT id FROM service_categories WHERE name_en = 'House Construction' LIMIT 1");
    $stmt->execute();
    $service_category_id = $stmt->fetchColumn() ?: 1;
    
    $stmt = $pdo->prepare("
        INSERT INTO quote_requests (
            customer_id, service_category_id, title, description, location, district,
            estimated_budget, project_timeline, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'open', NOW(), NOW())
    ");
    
    $stmt->execute([
        $customer_user_id,
        $service_category_id,
        'Modern Villa Construction',
        'Looking to build a 3-bedroom modern villa with contemporary design. The plot size is 20 perches. Need complete construction including electrical, plumbing, and interior work. Prefer eco-friendly materials and energy-efficient design.',
        'Nugegoda, Colombo',
        'Colombo',
        8500000.00,
        '6-8 months'
    ]);
    
    $stmt->execute([
        $customer_user_id,
        $service_category_id,
        'House Renovation',
        'Complete renovation of a 2-story house built in 1985. Need to update electrical wiring, plumbing, flooring, and interior design. Also need to add a modern kitchen and upgrade bathrooms.',
        'Kandy, Central Province',
        'Kandy',
        4200000.00,
        '3-4 months'
    ]);
    
    // Commit transaction
    $pdo->commit();
    
    echo "<h2>✅ Sample Customer Data Added Successfully!</h2>";
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🔑 Customer Login Credentials:</h3>";
    echo "<p><strong>Email:</strong> <EMAIL></p>";
    echo "<p><strong>Password:</strong> password</p>";
    echo "<p><strong>User Type:</strong> Customer</p>";
    echo "<p><strong>Status:</strong> Approved</p>";
    echo "<p><strong>Name:</strong> Yoshara Silva</p>";
    echo "<p><strong>Favorites:</strong> 1 contractor added</p>";
    echo "<p><strong>Quote Requests:</strong> 2 sample requests added</p>";
    echo "</div>";
    
    echo "<div style='background: #cce5ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🔗 Quick Links:</h3>";
    echo "<p><a href='../login.php' style='color: #0066cc;'>Login Page</a></p>";
    echo "<p><a href='../customer/dashboard.php' style='color: #0066cc;'>Customer Dashboard</a></p>";
    echo "<p><a href='../customer/favorites.php' style='color: #0066cc;'>Customer Favorites</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    $pdo->rollBack();
    echo "<h2>❌ Error Adding Sample Customer Data</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
