<?php
session_start();
require_once 'config/database.php';

echo "<h2>✅ Final Test: Contractor Quotes Fix</h2>";

try {
    // Get test contractor
    $stmt = $pdo->query("
        SELECT u.id, u.email, cp.business_name 
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved' 
        LIMIT 1
    ");
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p style='color: red;'>❌ No contractors found. Please run fix_contractor_quotes_issue.php first.</p>";
        exit;
    }
    
    $contractor_id = $contractor['id'];
    echo "<p><strong>Testing with contractor:</strong> " . htmlspecialchars($contractor['business_name']) . " (ID: $contractor_id)</p>";
    
    // Test 1: Dashboard pending quotes (EXACT COPY FROM dashboard.php)
    echo "<h3>Test 1: Dashboard Pending Quotes</h3>";
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.id NOT IN (
            SELECT COALESCE(quote_request_id, 0)
            FROM quote_responses
            WHERE contractor_id = ?
        )
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
    $all_pending_quotes = $stmt->fetchAll();
    
    // Filter quotes in PHP (EXACT COPY FROM dashboard.php)
    $dashboard_pending = 0;
    foreach ($all_pending_quotes as $quote) {
        // Always count direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $dashboard_pending++;
            continue;
        }

        // For general quotes (no specific contractor), check service and area match
        if ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];

            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);

            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);

            if ($has_service && $has_area) {
                $dashboard_pending++;
            }
        }
    }
    
    echo "<p><strong>Dashboard pending quotes:</strong> $dashboard_pending</p>";
    
    // Test 2: Quotes page pending count (EXACT COPY FROM quotes.php)
    echo "<h3>Test 2: Quotes Page Pending Count</h3>";
    
    // Get all quotes for this contractor and filter in PHP (EXACT COPY)
    $stmt = $pdo->prepare("
        SELECT qr.id, qr.status, qr.specific_contractor_id, qr.service_category_id, qr.district,
               cp_contractor.service_types, cp_contractor.service_areas,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT COUNT(*) FROM quote_responses qres
                JOIN project_payments pp ON qres.id = pp.quote_response_id
                WHERE qres.quote_request_id = qr.id AND qres.contractor_id = ?
                AND pp.payment_type = 'down_payment' AND pp.payment_status = 'completed') as has_payment,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? AND status = 'rejected') as is_rejected
        FROM quote_requests qr
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND (qr.status IN ('open', 'completed', 'cancelled') OR qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?))
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id]);
    $all_count_quotes = $stmt->fetchAll();

    // Filter and count in PHP (EXACT COPY)
    $quotes_page_pending = 0;

    foreach ($all_count_quotes as $quote) {
        $should_include = false;

        // Always include direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $should_include = true;
        } elseif ($quote['specific_contractor_id'] === null) {
            // For general quotes, check service and area match
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];

            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);

            if ($has_service && $has_area) {
                $should_include = true;
            }
        }

        if ($should_include) {
            if ($quote['status'] === 'open' && $quote['has_responded'] == 0) {
                $quotes_page_pending++;
            }
        }
    }
    
    echo "<p><strong>Quotes page pending count:</strong> $quotes_page_pending</p>";
    
    // Test 3: Quotes page pending filter query (EXACT COPY)
    echo "<h3>Test 3: Quotes Page Pending Filter Query</h3>";
    
    $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
    $params = [$contractor_id];
    $where_conditions[] = "qr.status = 'open' AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)";
    $params[] = $contractor_id;
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
               cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");
    
    $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
    $stmt->execute($query_params);
    $all_quotes = $stmt->fetchAll();
    
    // Filter quotes in PHP (EXACT COPY)
    $quotes_page_filtered = 0;
    foreach ($all_quotes as $quote) {
        // Always show direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $quotes_page_filtered++;
        } elseif ($quote['specific_contractor_id'] === null) {
            // For general quotes, check service and area match
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];

            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);

            if ($has_service && $has_area) {
                $quotes_page_filtered++;
            }
        }
    }
    
    echo "<p><strong>Quotes page pending filter:</strong> $quotes_page_filtered</p>";
    
    // Final Results
    echo "<h3>🎯 Final Results</h3>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='padding: 10px;'>Source</th><th style='padding: 10px;'>Count</th><th style='padding: 10px;'>Status</th></tr>";
    echo "<tr><td style='padding: 10px;'>Dashboard Pending</td><td style='padding: 10px; text-align: center;'>$dashboard_pending</td><td style='padding: 10px;'>" . ($dashboard_pending > 0 ? '✅ Has quotes' : '❌ No quotes') . "</td></tr>";
    echo "<tr><td style='padding: 10px;'>Quotes Page Tab Count</td><td style='padding: 10px; text-align: center;'>$quotes_page_pending</td><td style='padding: 10px;'>" . ($quotes_page_pending > 0 ? '✅ Has quotes' : '❌ No quotes') . "</td></tr>";
    echo "<tr><td style='padding: 10px;'>Quotes Page Filter</td><td style='padding: 10px; text-align: center;'>$quotes_page_filtered</td><td style='padding: 10px;'>" . ($quotes_page_filtered > 0 ? '✅ Has quotes' : '❌ No quotes') . "</td></tr>";
    echo "</table>";
    
    if ($dashboard_pending == $quotes_page_pending && $quotes_page_pending == $quotes_page_filtered) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='margin: 0 0 10px 0;'>🎉 SUCCESS! All counts match perfectly!</h4>";
        echo "<p style='margin: 0;'>Dashboard and quotes page are now synchronized. Contractors should be able to see their quotes.</p>";
        echo "</div>";
        
        echo "<h4>✅ What's Fixed:</h4>";
        echo "<ul>";
        echo "<li>Dashboard pending count now matches quotes page pending count</li>";
        echo "<li>Quotes page now defaults to 'Pending' tab instead of 'All' tab</li>";
        echo "<li>Both use identical filtering logic for service areas and types</li>";
        echo "<li>LEFT JOIN used to prevent missing service categories from hiding quotes</li>";
        echo "</ul>";
        
        echo "<h4>🧪 Test the Fix:</h4>";
        echo "<ol>";
        echo "<li><a href='contractor/login.php' target='_blank' style='color: #007bff;'>Login as contractor</a> (<EMAIL> / password)</li>";
        echo "<li><a href='contractor/dashboard.php' target='_blank' style='color: #007bff;'>Check dashboard</a> - should show $dashboard_pending pending quotes</li>";
        echo "<li><a href='contractor/quotes.php' target='_blank' style='color: #007bff;'>Check quotes page</a> - should default to Pending tab with $quotes_page_filtered quotes</li>";
        echo "</ol>";
        
        if ($dashboard_pending > 0) {
            echo "<p style='color: green; font-weight: bold;'>✅ Contractors will now see $dashboard_pending quote(s) they can respond to!</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ No pending quotes found. Create some quote requests to test the system.</p>";
        }
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='margin: 0 0 10px 0;'>❌ Still a mismatch!</h4>";
        echo "<p style='margin: 0;'>Dashboard: $dashboard_pending, Tab Count: $quotes_page_pending, Filter: $quotes_page_filtered</p>";
        echo "</div>";
        
        echo "<p>There may be a subtle difference in the logic that needs further investigation.</p>";
    }
    
    // Show sample quotes if any exist
    if ($quotes_page_filtered > 0 && count($all_quotes) > 0) {
        echo "<h4>📋 Sample Quotes Available:</h4>";
        foreach (array_slice($all_quotes, 0, 3) as $quote) {
            if ($quote['specific_contractor_id'] == $contractor_id || 
                ($quote['specific_contractor_id'] === null && 
                 in_array((int)$quote['service_category_id'], json_decode($quote['service_types'], true) ?: []) &&
                 in_array($quote['district'], json_decode($quote['service_areas'], true) ?: []))) {
                
                echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; background: #f9f9f9;'>";
                echo "<strong>" . htmlspecialchars($quote['title'] ?? 'Untitled Quote') . "</strong><br>";
                echo "Service: " . htmlspecialchars($quote['service_category'] ?? 'Unknown') . "<br>";
                echo "District: " . $quote['district'] . "<br>";
                echo "Budget: Rs. " . number_format($quote['estimated_budget']) . "<br>";
                echo "Status: " . ucfirst($quote['status']) . "<br>";
                echo "</div>";
            }
        }
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Database Error</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
