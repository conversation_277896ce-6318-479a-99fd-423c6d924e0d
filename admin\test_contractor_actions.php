<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

echo "<h1>Contractor Actions Test</h1>";

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<div style='background: #f0f0f0; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>POST Data Received:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    if (isset($_POST['approve_contractor'])) {
        $contractor_id = (int)$_POST['contractor_id'];
        echo "<p><strong>Action:</strong> Approve Contractor</p>";
        echo "<p><strong>Contractor ID:</strong> $contractor_id</p>";
        
        try {
            $stmt = $pdo->prepare("UPDATE users SET status = 'approved' WHERE id = ? AND user_type = 'contractor'");
            $result = $stmt->execute([$contractor_id]);
            $affected_rows = $stmt->rowCount();
            
            echo "<p><strong>SQL Result:</strong> " . ($result ? 'Success' : 'Failed') . "</p>";
            echo "<p><strong>Affected Rows:</strong> $affected_rows</p>";
            
            if ($affected_rows > 0) {
                echo "<p style='color: green;'>✅ Contractor approved successfully!</p>";
            } else {
                echo "<p style='color: red;'>❌ No contractor found with ID: $contractor_id</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
        }
        
    } elseif (isset($_POST['reject_contractor'])) {
        $contractor_id = (int)$_POST['contractor_id'];
        echo "<p><strong>Action:</strong> Reject Contractor</p>";
        echo "<p><strong>Contractor ID:</strong> $contractor_id</p>";
        
        try {
            $stmt = $pdo->prepare("UPDATE users SET status = 'rejected' WHERE id = ? AND user_type = 'contractor'");
            $result = $stmt->execute([$contractor_id]);
            $affected_rows = $stmt->rowCount();
            
            echo "<p><strong>SQL Result:</strong> " . ($result ? 'Success' : 'Failed') . "</p>";
            echo "<p><strong>Affected Rows:</strong> $affected_rows</p>";
            
            if ($affected_rows > 0) {
                echo "<p style='color: green;'>✅ Contractor rejected successfully!</p>";
            } else {
                echo "<p style='color: red;'>❌ No contractor found with ID: $contractor_id</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
        }
        
    } elseif (isset($_POST['suspend_contractor'])) {
        $contractor_id = (int)$_POST['contractor_id'];
        echo "<p><strong>Action:</strong> Suspend Contractor</p>";
        echo "<p><strong>Contractor ID:</strong> $contractor_id</p>";
        
        try {
            $stmt = $pdo->prepare("UPDATE users SET status = 'suspended' WHERE id = ? AND user_type = 'contractor'");
            $result = $stmt->execute([$contractor_id]);
            $affected_rows = $stmt->rowCount();
            
            echo "<p><strong>SQL Result:</strong> " . ($result ? 'Success' : 'Failed') . "</p>";
            echo "<p><strong>Affected Rows:</strong> $affected_rows</p>";
            
            if ($affected_rows > 0) {
                echo "<p style='color: green;'>✅ Contractor suspended successfully!</p>";
            } else {
                echo "<p style='color: red;'>❌ No contractor found with ID: $contractor_id</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
        }
        
    } elseif (isset($_POST['unsuspend_contractor'])) {
        $contractor_id = (int)$_POST['contractor_id'];
        echo "<p><strong>Action:</strong> Unsuspend Contractor</p>";
        echo "<p><strong>Contractor ID:</strong> $contractor_id</p>";
        
        try {
            $stmt = $pdo->prepare("UPDATE users SET status = 'approved' WHERE id = ? AND user_type = 'contractor'");
            $result = $stmt->execute([$contractor_id]);
            $affected_rows = $stmt->rowCount();
            
            echo "<p><strong>SQL Result:</strong> " . ($result ? 'Success' : 'Failed') . "</p>";
            echo "<p><strong>Affected Rows:</strong> $affected_rows</p>";
            
            if ($affected_rows > 0) {
                echo "<p style='color: green;'>✅ Contractor unsuspended successfully!</p>";
            } else {
                echo "<p style='color: red;'>❌ No contractor found with ID: $contractor_id</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
}

// Get all contractors
try {
    $stmt = $pdo->prepare("
        SELECT u.*, cp.business_name, cp.contact_person 
        FROM users u
        LEFT JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.user_type = 'contractor'
        ORDER BY u.created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $contractors = $stmt->fetchAll();
} catch (PDOException $e) {
    $contractors = [];
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}

echo "<h2>Available Contractors for Testing:</h2>";
if (!empty($contractors)) {
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Business Name</th><th>Contact Person</th><th>Email</th><th>Status</th><th>Actions</th></tr>";
    
    foreach ($contractors as $contractor) {
        echo "<tr>";
        echo "<td>" . $contractor['id'] . "</td>";
        echo "<td>" . htmlspecialchars($contractor['business_name'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($contractor['contact_person'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($contractor['email']) . "</td>";
        echo "<td><strong>" . ucfirst($contractor['status']) . "</strong></td>";
        echo "<td>";
        
        if ($contractor['status'] === 'pending') {
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='contractor_id' value='" . $contractor['id'] . "'>";
            echo "<button type='submit' name='approve_contractor' style='background: green; color: white; padding: 5px 10px; border: none; border-radius: 3px; margin: 2px;'>Approve</button>";
            echo "</form>";
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='contractor_id' value='" . $contractor['id'] . "'>";
            echo "<button type='submit' name='reject_contractor' style='background: red; color: white; padding: 5px 10px; border: none; border-radius: 3px; margin: 2px;'>Reject</button>";
            echo "</form>";
        } elseif ($contractor['status'] === 'approved') {
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='contractor_id' value='" . $contractor['id'] . "'>";
            echo "<button type='submit' name='suspend_contractor' style='background: gray; color: white; padding: 5px 10px; border: none; border-radius: 3px; margin: 2px;'>Suspend</button>";
            echo "</form>";
        } elseif ($contractor['status'] === 'suspended') {
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='contractor_id' value='" . $contractor['id'] . "'>";
            echo "<button type='submit' name='unsuspend_contractor' style='background: blue; color: white; padding: 5px 10px; border: none; border-radius: 3px; margin: 2px;'>Unsuspend</button>";
            echo "</form>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No contractors found.</p>";
}

echo "<br><a href='contractors.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Contractors Page</a>";
?>
