<?php
require_once 'config/database.php';

echo "<h2>🎯 Final Test: General Quote System</h2>";

try {
    // Step 1: Database setup
    echo "<h3>Step 1: Database Setup</h3>";
    
    // Ensure specific_contractor_id column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        echo "<p style='color: green;'>✅ Added specific_contractor_id column</p>";
    } else {
        echo "<p style='color: green;'>✅ specific_contractor_id column exists</p>";
    }
    
    // Step 2: Clean up old test data
    echo "<h3>Step 2: Clean Up Test Data</h3>";
    
    $pdo->exec("DELETE FROM quote_requests WHERE title LIKE 'Test General Quote%'");
    $pdo->exec("DELETE FROM notifications WHERE message LIKE '%Test General Quote%'");
    echo "<p>✅ Cleaned up old test data</p>";
    
    // Step 3: Get test customer
    echo "<h3>Step 3: Test Customer</h3>";
    
    $stmt = $pdo->query("
        SELECT u.id, cp.first_name, cp.last_name 
        FROM users u 
        JOIN customer_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'customer' 
        LIMIT 1
    ");
    $customer = $stmt->fetch();
    
    if (!$customer) {
        $pdo->exec("INSERT INTO users (email, password, user_type, status) VALUES ('<EMAIL>', 'password', 'customer', 'approved')");
        $customer_id = $pdo->lastInsertId();
        $pdo->exec("INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district, address) VALUES ($customer_id, 'Test', 'Customer', '0771234567', 'Colombo', 'Test Address')");
        $customer = ['id' => $customer_id, 'first_name' => 'Test', 'last_name' => 'Customer'];
        echo "<p style='color: green;'>✅ Created test customer</p>";
    } else {
        echo "<p style='color: green;'>✅ Using customer: " . htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']) . "</p>";
    }
    
    // Step 4: Create general quote request (simulate customer request)
    echo "<h3>Step 4: Create General Quote Request</h3>";
    
    $service_category_id = 1; // House Construction
    $title = "Test General Quote - " . date('Y-m-d H:i:s');
    $description = "This is a test general quote request to verify the system works end-to-end.";
    $location = "Test Location, Colombo";
    $district = "Colombo";
    $estimated_budget = 2000000;
    $project_timeline = "6 months";
    
    $stmt = $pdo->prepare("
        INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, specific_contractor_id, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NULL, 'open')
    ");
    $stmt->execute([$customer['id'], $service_category_id, $title, $description, $location, $district, $estimated_budget, $project_timeline]);
    $quote_request_id = $pdo->lastInsertId();
    
    echo "<p style='color: green;'>✅ Created general quote request (ID: $quote_request_id)</p>";
    echo "<p><strong>Service:</strong> House Construction (ID: $service_category_id)</p>";
    echo "<p><strong>District:</strong> $district</p>";
    
    // Step 5: Find matching contractors (simulate process_quote_request.php logic)
    echo "<h3>Step 5: Find Matching Contractors</h3>";
    
    $stmt = $pdo->prepare("
        SELECT u.id, cp.business_name, cp.contact_person, cp.service_areas, cp.service_types
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.status = 'approved'
        AND u.user_type = 'contractor'
        AND cp.service_areas IS NOT NULL
        AND cp.service_areas != ''
        AND cp.service_areas != '[]'
        AND cp.service_types IS NOT NULL
        AND cp.service_types != ''
        AND cp.service_types != '[]'
    ");
    $stmt->execute();
    $all_contractors = $stmt->fetchAll();
    
    echo "<p>Found " . count($all_contractors) . " contractors with complete data</p>";
    
    $contractors_to_notify = [];
    foreach ($all_contractors as $contractor) {
        $service_areas = json_decode($contractor['service_areas'], true);
        $service_types = json_decode($contractor['service_types'], true);
        
        $has_area = is_array($service_areas) && in_array($district, $service_areas);
        $has_service = is_array($service_types) && (in_array($service_category_id, $service_types) || in_array((string)$service_category_id, $service_types));
        
        if ($has_area && $has_service) {
            $contractors_to_notify[] = $contractor;
            echo "<p style='color: green;'>✅ MATCHED: " . htmlspecialchars($contractor['business_name']) . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ NO MATCH: " . htmlspecialchars($contractor['business_name']) . " (Area: " . ($has_area ? 'YES' : 'NO') . ", Service: " . ($has_service ? 'YES' : 'NO') . ")</p>";
        }
    }
    
    echo "<p><strong>Total matching contractors:</strong> " . count($contractors_to_notify) . "</p>";
    
    // Step 6: Create notifications
    echo "<h3>Step 6: Create Notifications</h3>";
    
    foreach ($contractors_to_notify as $contractor) {
        $notification_title = "New Quote Request";
        $notification_message = "You have received a new quote request for: $title in $location, $district";
        
        $stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, title, message, type, related_id) 
            VALUES (?, ?, ?, 'quote_received', ?)
        ");
        $stmt->execute([$contractor['id'], $notification_title, $notification_message, $quote_request_id]);
        
        echo "<p>✅ Created notification for " . htmlspecialchars($contractor['business_name']) . "</p>";
    }
    
    // Step 7: Test contractor dashboard for each matching contractor
    echo "<h3>Step 7: Test Contractor Dashboards</h3>";
    
    foreach ($contractors_to_notify as $contractor) {
        echo "<div style='border: 2px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>Testing: " . htmlspecialchars($contractor['business_name']) . "</h4>";
        
        $contractor_id = $contractor['id'];
        
        // Test dashboard query
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, cp.district,
                   (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            WHERE qr.status = 'open'
            AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
            AND qr.id NOT IN (
                SELECT COALESCE(quote_request_id, 0)
                FROM quote_responses
                WHERE contractor_id = ?
            )
            ORDER BY qr.created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
        $all_recent_quotes = $stmt->fetchAll();
        
        echo "<p><strong>Dashboard query returned:</strong> " . count($all_recent_quotes) . " quotes</p>";
        
        // Apply filtering logic
        $recent_quotes = [];
        foreach ($all_recent_quotes as $quote) {
            if ($quote['specific_contractor_id'] == $contractor_id) {
                $recent_quotes[] = $quote;
                continue;
            }
            
            if ($quote['specific_contractor_id'] === null) {
                $contractor_services = json_decode($contractor['service_types'], true) ?: [];
                $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];
                
                $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                              in_array((string)$quote['service_category_id'], $contractor_services);
                $has_area = in_array($quote['district'], $contractor_areas);
                
                if ($has_service && $has_area) {
                    $recent_quotes[] = $quote;
                }
            }
            
            if (count($recent_quotes) >= 5) {
                break;
            }
        }
        
        echo "<p><strong>After filtering:</strong> " . count($recent_quotes) . " quotes</p>";
        
        if (count($recent_quotes) > 0) {
            echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS! Quotes are visible to this contractor</p>";
            foreach ($recent_quotes as $quote) {
                if ($quote['id'] == $quote_request_id) {
                    echo "<p style='color: green;'>🎯 Our test quote is visible: " . htmlspecialchars($quote['title']) . "</p>";
                }
            }
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ FAILED! No quotes visible to this contractor</p>";
        }
        
        echo "</div>";
    }
    
    // Step 8: Summary
    echo "<h3>Step 8: Summary</h3>";
    
    if (count($contractors_to_notify) > 0) {
        echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 SUCCESS!</p>";
        echo "<ul>";
        echo "<li>✅ General quote request created successfully</li>";
        echo "<li>✅ " . count($contractors_to_notify) . " matching contractors found</li>";
        echo "<li>✅ Notifications created for all matching contractors</li>";
        echo "<li>✅ Quotes should now be visible in contractor dashboards</li>";
        echo "</ul>";
        
        echo "<h4>Next Steps:</h4>";
        echo "<ol>";
        echo "<li>Login as a contractor and check the dashboard</li>";
        echo "<li>Verify the quote appears in the 'Recent Quote Requests' section</li>";
        echo "<li>Check the quotes page to see all available quotes</li>";
        echo "<li>Test responding to the quote</li>";
        echo "</ol>";
        
        echo "<p><strong>Test Quote Details:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Quote ID:</strong> $quote_request_id</li>";
        echo "<li><strong>Title:</strong> " . htmlspecialchars($title) . "</li>";
        echo "<li><strong>Service:</strong> House Construction</li>";
        echo "<li><strong>District:</strong> $district</li>";
        echo "</ul>";
        
    } else {
        echo "<p style='color: red; font-size: 18px; font-weight: bold;'>❌ FAILED!</p>";
        echo "<p>No contractors were found that match the service and area requirements.</p>";
        echo "<p>Please ensure contractors have:</p>";
        echo "<ul>";
        echo "<li>House Construction (service ID 1) in their service types</li>";
        echo "<li>Colombo in their service areas</li>";
        echo "<li>Approved status</li>";
        echo "</ul>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>
