<?php
require_once 'database.php';

try {
    echo "Starting database updates for reviews system...\n";
    
    // Update notifications table to add new notification types
    echo "Updating notifications table...\n";
    $stmt = $pdo->prepare("
        ALTER TABLE notifications 
        MODIFY COLUMN type ENUM('quote_received', 'quote_response', 'approval_status', 'new_review', 'verification_update', 'payment_received', 'payment_success', 'review_received', 'general') NOT NULL
    ");
    $stmt->execute();
    echo "✓ Notifications table updated\n";
    
    // Create reviews table
    echo "Creating reviews table...\n";
    $stmt = $pdo->prepare("
        CREATE TABLE IF NOT EXISTS reviews (
            id INT NOT NULL AUTO_INCREMENT,
            customer_id INT NOT NULL,
            contractor_id INT NOT NULL,
            payment_id INT NOT NULL,
            quote_response_id INT NOT NULL,
            rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
            review_text TEXT,
            quality_rating INT CHECK (quality_rating >= 1 AND quality_rating <= 5),
            communication_rating INT CHECK (communication_rating >= 1 AND communication_rating <= 5),
            timeliness_rating INT CHECK (timeliness_rating >= 1 AND timeliness_rating <= 5),
            value_rating INT CHECK (value_rating >= 1 AND value_rating <= 5),
            recommend BOOLEAN,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (contractor_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (payment_id) REFERENCES project_payments(id) ON DELETE CASCADE,
            FOREIGN KEY (quote_response_id) REFERENCES quote_responses(id) ON DELETE CASCADE,
            UNIQUE KEY unique_customer_contractor_payment (customer_id, contractor_id, payment_id),
            INDEX idx_contractor_id (contractor_id),
            INDEX idx_customer_id (customer_id),
            INDEX idx_rating (rating),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    $stmt->execute();
    echo "✓ Reviews table created\n";
    
    echo "\nDatabase updates completed successfully!\n";
    echo "✓ Payment notifications system ready\n";
    echo "✓ Rating and review system ready\n";
    echo "✓ Payment history tracking ready\n";
    
} catch (PDOException $e) {
    echo "Error updating database: " . $e->getMessage() . "\n";
    exit(1);
}
?>
