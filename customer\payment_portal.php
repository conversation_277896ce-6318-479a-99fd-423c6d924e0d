<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

// Get payment session data
if (!isset($_SESSION['payment_data'])) {
    $_SESSION['error'] = 'Invalid payment session.';
    header('Location: quotes.php');
    exit();
}

$payment_data = $_SESSION['payment_data'];
$quote_response_id = $payment_data['quote_response_id'];
$amount = $payment_data['amount'];
$payment_type = $payment_data['payment_type'];
$payment_method = $payment_data['payment_method'];

// Get quote response details for display
try {
    $stmt = $pdo->prepare("
        SELECT qres.*, qr.title, qr.description, qr.location, qr.district,
               cp.business_name, cp.contact_person, cp.phone,
               cust.first_name, cust.last_name
        FROM quote_responses qres
        JOIN quote_requests qr ON qres.quote_request_id = qr.id
        JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
        JOIN customer_profiles cust ON qr.customer_id = cust.user_id
        WHERE qres.id = ? AND qr.customer_id = ?
    ");
    $stmt->execute([$quote_response_id, $_SESSION['user_id']]);
    $quote_response = $stmt->fetch();
    
    if (!$quote_response) {
        $_SESSION['error'] = 'Quote response not found.';
        header('Location: quotes.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: quotes.php');
    exit();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Payment Portal - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Inter', sans-serif;
        }
        
        .payment-portal-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .portal-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 40px;
            border: 1px solid #e9ecef;
        }
        
        .payment-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .payment-summary {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            font-weight: 600;
            color: #343a40;
            margin-bottom: 8px;
        }
        
        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
            transition: border-color 0.2s;
        }
        
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        
        .card-input-group {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            gap: 15px;
        }
        
        .btn-process-payment {
            background: #007bff;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            color: white;
            width: 100%;
            transition: all 0.2s;
        }
        
        .btn-process-payment:hover {
            background: #0056b3;
            transform: translateY(-1px);
            color: white;
        }
        
        .btn-process-payment:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .security-badges {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .security-badge {
            display: flex;
            align-items: center;
            color: #28a745;
            font-size: 14px;
        }
        
        .payment-method-display {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
        }
        
        .loading-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 40px;
            border-radius: 12px;
            text-align: center;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>

<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h5>Processing Payment...</h5>
            <p class="text-muted mb-0">Please wait while we process your payment securely.</p>
        </div>
    </div>

    <div class="payment-portal-container">
        <div class="portal-card">
            <!-- Payment Header -->
            <div class="payment-header">
                <h2 class="mb-2">Secure Payment Portal</h2>
                <p class="text-muted mb-0">Complete your payment details below</p>
            </div>
            
            <!-- Payment Summary -->
            <div class="payment-summary">
                <h6 class="mb-3">Payment Summary</h6>
                <div class="d-flex justify-content-between mb-2">
                    <span>Project:</span>
                    <strong><?php echo htmlspecialchars($quote_response['title']); ?></strong>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>Contractor:</span>
                    <strong><?php echo htmlspecialchars($quote_response['business_name']); ?></strong>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>Payment Type:</span>
                    <strong><?php echo ucfirst(str_replace('_', ' ', $payment_type)); ?></strong>
                </div>
                <hr>
                <div class="d-flex justify-content-between">
                    <span class="h6">Total Amount:</span>
                    <span class="h5 text-primary">Rs. <?php echo number_format($amount, 2); ?></span>
                </div>
            </div>
            
            <!-- Payment Method Display -->
            <div class="payment-method-display">
                <i class="fas fa-<?php echo $payment_method === 'card' ? 'credit-card' : ($payment_method === 'bank' ? 'university' : 'mobile-alt'); ?> me-3 text-primary"></i>
                <span class="fw-bold">
                    <?php 
                    echo $payment_method === 'card' ? 'Credit/Debit Card' : 
                         ($payment_method === 'bank' ? 'Bank Transfer' : 'Mobile Payment'); 
                    ?>
                </span>
            </div>
            
            <!-- Payment Form -->
            <form action="process_payment_portal.php" method="POST" id="paymentPortalForm">
                <input type="hidden" name="quote_response_id" value="<?php echo $quote_response_id; ?>">
                <input type="hidden" name="amount" value="<?php echo $amount; ?>">
                <input type="hidden" name="payment_type" value="<?php echo $payment_type; ?>">
                <input type="hidden" name="payment_method" value="<?php echo $payment_method; ?>">
                
                <?php if ($payment_method === 'card'): ?>
                <!-- Card Payment Fields -->
                <div class="form-group">
                    <label class="form-label">Cardholder Name</label>
                    <input type="text" class="form-control" name="cardholder_name" required 
                           placeholder="Enter name as shown on card">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Card Number</label>
                    <input type="text" class="form-control" name="card_number" required 
                           placeholder="1234 5678 9012 3456" maxlength="19" id="cardNumber">
                </div>
                
                <div class="card-input-group">
                    <div class="form-group">
                        <label class="form-label">Expiry Date</label>
                        <input type="text" class="form-control" name="expiry_date" required 
                               placeholder="MM/YY" maxlength="5" id="expiryDate">
                    </div>
                    <div class="form-group">
                        <label class="form-label">CVV</label>
                        <input type="text" class="form-control" name="cvv" required 
                               placeholder="123" maxlength="4" id="cvv">
                    </div>
                </div>
                
                <?php elseif ($payment_method === 'bank'): ?>
                <!-- Bank Transfer Fields -->
                <div class="form-group">
                    <label class="form-label">Bank Name</label>
                    <select class="form-control" name="bank_name" required>
                        <option value="">Select your bank</option>
                        <option value="commercial_bank">Commercial Bank</option>
                        <option value="peoples_bank">People's Bank</option>
                        <option value="bank_of_ceylon">Bank of Ceylon</option>
                        <option value="sampath_bank">Sampath Bank</option>
                        <option value="hatton_national">Hatton National Bank</option>
                        <option value="seylan_bank">Seylan Bank</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Account Number</label>
                    <input type="text" class="form-control" name="account_number" required 
                           placeholder="Enter your account number">
                </div>
                
                <?php else: ?>
                <!-- Mobile Payment Fields -->
                <div class="form-group">
                    <label class="form-label">Mobile Payment Service</label>
                    <select class="form-control" name="mobile_service" required>
                        <option value="">Select service</option>
                        <option value="ez_cash">eZ Cash</option>
                        <option value="mcash">mCash</option>
                        <option value="dialog_pay">Dialog Pay</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Mobile Number</label>
                    <input type="text" class="form-control" name="mobile_number" required 
                           placeholder="07X XXX XXXX">
                </div>
                
                <div class="form-group">
                    <label class="form-label">PIN</label>
                    <input type="password" class="form-control" name="mobile_pin" required 
                           placeholder="Enter your mobile payment PIN">
                </div>
                <?php endif; ?>
                
                <!-- Submit Button -->
                <button type="submit" class="btn-process-payment" id="submitBtn">
                    <i class="fas fa-lock me-2"></i>
                    Process Payment - Rs. <?php echo number_format($amount, 2); ?>
                </button>
                
                <!-- Security Badges -->
                <div class="security-badges">
                    <div class="security-badge">
                        <i class="fas fa-shield-alt me-2"></i>
                        SSL Secured
                    </div>
                    <div class="security-badge">
                        <i class="fas fa-lock me-2"></i>
                        256-bit Encryption
                    </div>
                    <div class="security-badge">
                        <i class="fas fa-check-circle me-2"></i>
                        PCI Compliant
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Card number formatting
        document.getElementById('cardNumber')?.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            e.target.value = formattedValue;
        });
        
        // Expiry date formatting
        document.getElementById('expiryDate')?.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            e.target.value = value;
        });
        
        // CVV numeric only
        document.getElementById('cvv')?.addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/\D/g, '');
        });
        
        // Form submission with loading
        document.getElementById('paymentPortalForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading overlay
            document.getElementById('loadingOverlay').style.display = 'block';
            document.getElementById('submitBtn').disabled = true;
            
            // Simulate processing time (2-3 seconds)
            setTimeout(() => {
                this.submit();
            }, 2500);
        });
    </script>
</body>
</html>
