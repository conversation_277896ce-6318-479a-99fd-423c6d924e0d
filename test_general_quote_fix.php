<?php
require_once 'config/database.php';

echo "<h2>🔧 Test General Quote Fix</h2>";

try {
    // Step 1: Ensure specific_contractor_id column exists
    echo "<h3>Step 1: Database Setup</h3>";
    
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        echo "<p>Adding specific_contractor_id column...</p>";
        $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        echo "<p style='color: green;'>✅ Added specific_contractor_id column</p>";
    } else {
        echo "<p style='color: green;'>✅ specific_contractor_id column exists</p>";
    }
    
    // Step 2: Get or create test customer
    echo "<h3>Step 2: Test Customer</h3>";
    
    $stmt = $pdo->query("
        SELECT u.id, cp.first_name, cp.last_name 
        FROM users u 
        JOIN customer_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'customer' 
        LIMIT 1
    ");
    $customer = $stmt->fetch();
    
    if (!$customer) {
        echo "<p>Creating test customer...</p>";
        $pdo->exec("INSERT INTO users (email, password, user_type, status) VALUES ('<EMAIL>', 'password', 'customer', 'approved')");
        $customer_id = $pdo->lastInsertId();
        $pdo->exec("INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district, address) VALUES ($customer_id, 'Test', 'Customer', '0771234567', 'Colombo', 'Test Address')");
        $customer = ['id' => $customer_id, 'first_name' => 'Test', 'last_name' => 'Customer'];
        echo "<p style='color: green;'>✅ Created test customer</p>";
    } else {
        echo "<p style='color: green;'>✅ Using existing customer: " . htmlspecialchars($customer['first_name'] . ' ' . $customer['last_name']) . "</p>";
    }
    
    // Step 3: Get approved contractor
    echo "<h3>Step 3: Test Contractor</h3>";
    
    $stmt = $pdo->query("
        SELECT u.id, cp.business_name, cp.service_types, cp.service_areas
        FROM users u
        JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        LIMIT 1
    ");
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p style='color: red;'>❌ No approved contractors found</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Using contractor: " . htmlspecialchars($contractor['business_name']) . "</p>";
    
    $services = json_decode($contractor['service_types'], true) ?: [];
    $areas = json_decode($contractor['service_areas'], true) ?: [];
    echo "<p><strong>Services:</strong> " . implode(', ', $services) . "</p>";
    echo "<p><strong>Areas:</strong> " . implode(', ', $areas) . "</p>";
    
    // Step 4: Create a general quote that matches this contractor
    echo "<h3>Step 4: Create Test General Quote</h3>";
    
    if (count($services) > 0 && count($areas) > 0) {
        $test_service = $services[0];
        $test_area = $areas[0];
        
        $stmt = $pdo->prepare("
            INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, specific_contractor_id, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NULL, 'open')
        ");
        $stmt->execute([
            $customer['id'],
            $test_service,
            'Test General Quote - ' . date('Y-m-d H:i:s'),
            'This is a test general quote to verify the contractor dashboard shows general quotes correctly.',
            'Test Location, ' . $test_area,
            $test_area,
            1500000,
            '4 months'
        ]);
        
        $quote_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ Created general quote (ID: $quote_id) for service $test_service in $test_area</p>";
        
        // Step 5: Test contractor dashboard query
        echo "<h3>Step 5: Test Dashboard Query</h3>";
        
        $contractor_id = $contractor['id'];
        
        // Test the exact query from dashboard.php
        $stmt = $pdo->prepare("
            SELECT qr.*, cp.first_name, cp.last_name, cp.district,
                   (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
            FROM quote_requests qr
            JOIN customer_profiles cp ON qr.customer_id = cp.user_id
            WHERE qr.status = 'open'
            AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
            AND qr.id NOT IN (
                SELECT COALESCE(quote_request_id, 0)
                FROM quote_responses
                WHERE contractor_id = ?
            )
            ORDER BY qr.created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
        $all_recent_quotes = $stmt->fetchAll();
        
        echo "<p>Dashboard query returned " . count($all_recent_quotes) . " quotes</p>";
        
        // Test the filtering logic
        $recent_quotes = [];
        foreach ($all_recent_quotes as $quote) {
            // Always show direct quotes for this contractor
            if ($quote['specific_contractor_id'] == $contractor_id) {
                $recent_quotes[] = $quote;
                continue;
            }
            
            // For general quotes (no specific contractor), check service and area match
            if ($quote['specific_contractor_id'] === null) {
                $contractor_services = json_decode($contractor['service_types'], true) ?: [];
                $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];
                
                // Check if contractor provides this service
                $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                              in_array((string)$quote['service_category_id'], $contractor_services);
                
                // Check if contractor serves this area
                $has_area = in_array($quote['district'], $contractor_areas);
                
                if ($has_service && $has_area) {
                    $recent_quotes[] = $quote;
                }
            }
            
            // Limit to 5 quotes for dashboard
            if (count($recent_quotes) >= 5) {
                break;
            }
        }
        
        echo "<p><strong>After filtering:</strong> " . count($recent_quotes) . " quotes</p>";
        
        if (count($recent_quotes) > 0) {
            echo "<p style='color: green;'>✅ SUCCESS! Quotes are now visible to contractor</p>";
            echo "<ul>";
            foreach ($recent_quotes as $quote) {
                $type = $quote['specific_contractor_id'] ? 'Specific' : 'General';
                echo "<li><strong>ID {$quote['id']}:</strong> " . htmlspecialchars($quote['title']) . " ({$type})</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ FAILED! No quotes visible to contractor</p>";
        }
        
        // Step 6: Test pending quotes count
        echo "<h3>Step 6: Test Pending Quotes Count</h3>";
        
        $stmt = $pdo->prepare("
            SELECT qr.*
            FROM quote_requests qr
            WHERE qr.status = 'open'
            AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
            AND qr.id NOT IN (
                SELECT COALESCE(quote_request_id, 0)
                FROM quote_responses
                WHERE contractor_id = ?
            )
        ");
        $stmt->execute([$contractor_id, $contractor_id]);
        $all_pending_quotes = $stmt->fetchAll();
        
        $pending_quotes = 0;
        foreach ($all_pending_quotes as $quote) {
            // Always count direct quotes for this contractor
            if ($quote['specific_contractor_id'] == $contractor_id) {
                $pending_quotes++;
                continue;
            }
            
            // For general quotes (no specific contractor), check service and area match
            if ($quote['specific_contractor_id'] === null) {
                $contractor_services = json_decode($contractor['service_types'], true) ?: [];
                $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];
                
                // Check if contractor provides this service
                $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                              in_array((string)$quote['service_category_id'], $contractor_services);
                
                // Check if contractor serves this area
                $has_area = in_array($quote['district'], $contractor_areas);
                
                if ($has_service && $has_area) {
                    $pending_quotes++;
                }
            }
        }
        
        echo "<p><strong>Pending quotes count:</strong> $pending_quotes</p>";
        
        if ($pending_quotes > 0) {
            echo "<p style='color: green;'>✅ SUCCESS! Pending quotes count is working</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ No pending quotes found</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Contractor has no services or areas configured</p>";
    }
    
    echo "<h3>Summary</h3>";
    echo "<p>✅ Test completed! Check the contractor dashboard to see if quotes are now visible.</p>";
    echo "<p><a href='contractor/dashboard.php' target='_blank'>Open Contractor Dashboard</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>
