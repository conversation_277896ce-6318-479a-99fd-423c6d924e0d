<?php
require_once 'config/database.php';

echo "<h2>Fixing Database Issues</h2>";

try {
    echo "<h3>1. Checking and Adding Missing Columns</h3>";
    
    // Check if specific_contractor_id column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM quote_requests LIKE 'specific_contractor_id'");
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        echo "<p>Adding specific_contractor_id column...</p>";
        $pdo->exec("ALTER TABLE quote_requests ADD COLUMN specific_contractor_id INT NULL AFTER project_timeline");
        $pdo->exec("ALTER TABLE quote_requests ADD INDEX idx_specific_contractor_id (specific_contractor_id)");
        $pdo->exec("ALTER TABLE quote_requests ADD FOREIGN KEY (specific_contractor_id) REFERENCES users(id) ON DELETE SET NULL");
        echo "<p style='color: green;'>✓ Added specific_contractor_id column</p>";
    } else {
        echo "<p style='color: green;'>✓ specific_contractor_id column already exists</p>";
    }
    
    echo "<h3>2. Checking Reviews Table Structure</h3>";

    // Check if reviews table exists and has correct structure
    $stmt = $pdo->query("SHOW TABLES LIKE 'reviews'");
    $reviews_exists = $stmt->fetch();

    if ($reviews_exists) {
        // Check if payment_id column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'payment_id'");
        $has_payment_id = $stmt->fetch();

        if (!$has_payment_id) {
            echo "<p>Reviews table exists but missing payment_id column. Updating structure...</p>";

            // Check if it has quote_request_id instead
            $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'quote_request_id'");
            $has_quote_request_id = $stmt->fetch();

            if ($has_quote_request_id) {
                // Add payment_id column and quote_response_id if missing
                $pdo->exec("ALTER TABLE reviews ADD COLUMN payment_id INT NULL AFTER contractor_id");
                $pdo->exec("ALTER TABLE reviews ADD INDEX idx_payment_id (payment_id)");

                // Check if quote_response_id exists
                $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'quote_response_id'");
                $has_quote_response_id = $stmt->fetch();

                if (!$has_quote_response_id) {
                    $pdo->exec("ALTER TABLE reviews ADD COLUMN quote_response_id INT NULL AFTER payment_id");
                    $pdo->exec("ALTER TABLE reviews ADD INDEX idx_quote_response_id (quote_response_id)");
                }

                echo "<p style='color: green;'>✓ Updated reviews table structure</p>";
            }
        } else {
            echo "<p style='color: green;'>✓ Reviews table has correct structure</p>";
        }
    } else {
        echo "<p>Creating reviews table...</p>";
        $pdo->exec("
            CREATE TABLE reviews (
                id INT NOT NULL AUTO_INCREMENT,
                customer_id INT NOT NULL,
                contractor_id INT NOT NULL,
                payment_id INT NULL,
                quote_response_id INT NULL,
                quote_request_id INT NULL,
                rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
                review_text TEXT,
                quality_rating INT CHECK (quality_rating >= 1 AND quality_rating <= 5),
                communication_rating INT CHECK (communication_rating >= 1 AND communication_rating <= 5),
                timeliness_rating INT CHECK (timeliness_rating >= 1 AND timeliness_rating <= 5),
                value_rating INT CHECK (value_rating >= 1 AND value_rating <= 5),
                recommend BOOLEAN,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (contractor_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_customer_id (customer_id),
                INDEX idx_contractor_id (contractor_id),
                INDEX idx_payment_id (payment_id),
                INDEX idx_quote_response_id (quote_response_id),
                INDEX idx_quote_request_id (quote_request_id),
                INDEX idx_rating (rating),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p style='color: green;'>✓ Created reviews table with proper structure</p>";
    }

    // Check and add missing columns to existing reviews table
    echo "<h4>Checking for missing columns in reviews table...</h4>";
    $required_columns = [
        'quality_rating' => 'INT CHECK (quality_rating >= 1 AND quality_rating <= 5)',
        'communication_rating' => 'INT CHECK (communication_rating >= 1 AND communication_rating <= 5)',
        'timeliness_rating' => 'INT CHECK (timeliness_rating >= 1 AND timeliness_rating <= 5)',
        'value_rating' => 'INT CHECK (value_rating >= 1 AND value_rating <= 5)',
        'recommend' => 'BOOLEAN',
        'status' => "ENUM('pending', 'approved', 'rejected') DEFAULT 'pending'"
    ];

    foreach ($required_columns as $column => $definition) {
        try {
            $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE '$column'");
            $column_exists = $stmt->fetch();

            if (!$column_exists) {
                $pdo->exec("ALTER TABLE reviews ADD COLUMN $column $definition");
                echo "<p style='color: green;'>✓ Added missing column '$column' to reviews table</p>";
            } else {
                echo "<p style='color: blue;'>ℹ Column '$column' already exists in reviews table</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Error checking/adding column '$column': " . $e->getMessage() . "</p>";
        }
    }

    echo "<h3>3. Checking Project Payments Table</h3>";

    // Check if project_payments table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'project_payments'");
    $pp_exists = $stmt->fetch();

    if (!$pp_exists) {
        echo "<p>Creating project_payments table...</p>";
        $pdo->exec("
            CREATE TABLE project_payments (
                id INT NOT NULL AUTO_INCREMENT,
                quote_response_id INT NOT NULL,
                customer_id INT NOT NULL,
                contractor_id INT NOT NULL,
                amount DECIMAL(12,2) NOT NULL,
                payment_type ENUM('down_payment', 'milestone', 'final_payment') NOT NULL,
                payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
                payment_method VARCHAR(50),
                transaction_id VARCHAR(255),
                payment_date TIMESTAMP NULL,
                payment_details TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                INDEX idx_quote_response_id (quote_response_id),
                INDEX idx_customer_id (customer_id),
                INDEX idx_contractor_id (contractor_id),
                INDEX idx_payment_status (payment_status),
                INDEX idx_payment_type (payment_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p style='color: green;'>✓ Created project_payments table</p>";
    } else {
        echo "<p style='color: green;'>✓ Project payments table exists</p>";

        // Check if payment_details column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM project_payments LIKE 'payment_details'");
        $has_payment_details = $stmt->fetch();

        if (!$has_payment_details) {
            echo "<p>Adding missing payment_details column...</p>";
            $pdo->exec("ALTER TABLE project_payments ADD COLUMN payment_details TEXT AFTER payment_date");
            echo "<p style='color: green;'>✓ Added payment_details column</p>";
        } else {
            echo "<p style='color: green;'>✓ payment_details column exists</p>";
        }
    }

    echo "<h3>4. Checking Service Categories</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM service_categories");
    $service_count = $stmt->fetchColumn();
    
    if ($service_count == 0) {
        echo "<p>Adding service categories...</p>";
        $services = [
            ['House Construction', 'නිවාස ඉදිකිරීම', 'வீட்டு கட்டுமானம்'],
            ['Building Renovation', 'ගොඩනැගිලි අලුත්වැඩියා', 'கட்டிட புனரமைப்பு'],
            ['Commercial Construction', 'වාණිජ ඉදිකිරීම', 'வணிக கட்டுமானம்'],
            ['Interior Design & Finishing', 'අභ්‍යන්තර සැලසුම් සහ නිම කිරීම', 'உள்துறை வடிவமைப்பு மற்றும் முடித்தல்'],
            ['Roofing & Waterproofing', 'වහල සහ ජල ආරක්ෂණ', 'கூரை மற்றும் நீர்ப்புகாமை'],
            ['Electrical Work', 'විදුලි වැඩ', 'மின் வேலை'],
            ['Plumbing & Sanitation', 'ජල නල සහ සනීපාරක්ෂක', 'குழாய் மற்றும் சுகாதாரம்'],
            ['Landscaping & Gardening', 'භූමි අලංකරණ සහ උද්‍යාන', 'இயற்கை அலங்காரம் மற்றும் தோட்டக்கலை'],
            ['Swimming Pool Construction', 'පිහිනුම් තටාක ඉදිකිරීම', 'நீச்சல் குளம் கட்டுமானம்'],
            ['Road & Infrastructure', 'මාර්ග සහ යටිතල පහසුකම්', 'சாலை மற்றும் உள்கட்டமைப்பு']
        ];
        
        foreach ($services as $service) {
            $pdo->prepare("INSERT INTO service_categories (name_en, name_si, name_ta) VALUES (?, ?, ?)")->execute($service);
        }
        echo "<p style='color: green;'>✓ Added " . count($services) . " service categories</p>";
    } else {
        echo "<p style='color: green;'>✓ Service categories exist ($service_count)</p>";
    }
    
    echo "<h3>5. Checking Test Users</h3>";
    
    // Check for test customer
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'customer' AND status = 'approved'");
    $customer_count = $stmt->fetchColumn();
    
    if ($customer_count == 0) {
        echo "<p>Creating test customer...</p>";
        $pdo->exec("INSERT INTO users (email, password, user_type, status) VALUES ('<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 'customer', 'approved')");
        $customer_id = $pdo->lastInsertId();
        
        $pdo->prepare("INSERT INTO customer_profiles (user_id, first_name, last_name, phone, district, address) VALUES (?, 'Test', 'Customer', '0771234567', 'Colombo', 'Test Address')")->execute([$customer_id]);
        echo "<p style='color: green;'>✓ Created test customer (ID: $customer_id)</p>";
    } else {
        $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' AND status = 'approved' LIMIT 1");
        $customer_id = $stmt->fetchColumn();
        echo "<p style='color: green;'>✓ Test customer exists (ID: $customer_id)</p>";
    }
    
    // Check for test contractor
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'contractor' AND status = 'approved'");
    $contractor_count = $stmt->fetchColumn();
    
    if ($contractor_count == 0) {
        echo "<p>Creating test contractor...</p>";
        $pdo->exec("INSERT INTO users (email, password, user_type, status) VALUES ('<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 'contractor', 'approved')");
        $contractor_id = $pdo->lastInsertId();
        
        $pdo->prepare("INSERT INTO contractor_profiles (user_id, business_name, contact_person, phone, district, address, cida_grade, business_registration, average_rating, total_reviews) VALUES (?, 'Test Construction Ltd', 'John Contractor', '0771234568', 'Colombo', 'Test Contractor Address', 'A', 'REG123456', 4.5, 10)")->execute([$contractor_id]);
        echo "<p style='color: green;'>✓ Created test contractor (ID: $contractor_id)</p>";
    } else {
        $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'contractor' AND status = 'approved' LIMIT 1");
        $contractor_id = $stmt->fetchColumn();
        echo "<p style='color: green;'>✓ Test contractor exists (ID: $contractor_id)</p>";
    }
    
    echo "<h3>6. Creating Test Quote Request and Response</h3>";
    
    // Get first service category
    $stmt = $pdo->query("SELECT id FROM service_categories LIMIT 1");
    $service_id = $stmt->fetchColumn();
    
    // Check for test quote request
    $stmt = $pdo->prepare("SELECT id FROM quote_requests WHERE customer_id = ? LIMIT 1");
    $stmt->execute([$customer_id]);
    $quote_id = $stmt->fetchColumn();
    
    if (!$quote_id) {
        echo "<p>Creating test quote request...</p>";
        $pdo->prepare("
            INSERT INTO quote_requests (customer_id, service_category_id, title, description, location, district, estimated_budget, project_timeline, status) 
            VALUES (?, ?, 'Modern 3-Bedroom House Construction', 'Looking to build a modern 3-bedroom house with contemporary design. The plot size is 15 perches. Need complete construction including electrical, plumbing, and basic interior work. Prefer eco-friendly materials and energy-efficient design.', 'Nugegoda, Colombo', 'Colombo', 8000000, '6-8 months', 'open')
        ")->execute([$customer_id, $service_id]);
        $quote_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✓ Created test quote request (ID: $quote_id)</p>";
    } else {
        echo "<p style='color: green;'>✓ Test quote request exists (ID: $quote_id)</p>";
    }
    
    // Check for test quote response
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM quote_responses WHERE quote_request_id = ? AND contractor_id = ?");
    $stmt->execute([$quote_id, $contractor_id]);
    $response_exists = $stmt->fetchColumn();
    
    if ($response_exists == 0) {
        echo "<p>Creating test quote response...</p>";
        $pdo->prepare("
            INSERT INTO quote_responses (quote_request_id, contractor_id, quoted_amount, estimated_timeline, description, terms_conditions, status) 
            VALUES (?, ?, 7500000, '5-6 months', 'We can complete your house construction with high quality materials and experienced workers. Our team has over 10 years of experience in residential construction.', 'Payment terms: 30% advance, 40% at completion of structure, 30% on handover. All materials included. 1-year warranty on construction.', 'pending')
        ")->execute([$quote_id, $contractor_id]);
        echo "<p style='color: green;'>✓ Created test quote response</p>";
    } else {
        echo "<p style='color: green;'>✓ Test quote response exists</p>";
    }
    
    echo "<h3>7. Testing Quote Responses Page</h3>";
    echo "<p><a href='customer/quote_responses.php?id=$quote_id' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Quote Responses Page</a></p>";
    echo "<p><strong>Test Login:</strong> <EMAIL> / password</p>";

    echo "<h3>8. Summary</h3>";
    echo "<ul>";
    echo "<li>Customer ID: $customer_id</li>";
    echo "<li>Contractor ID: $contractor_id</li>";
    echo "<li>Quote Request ID: $quote_id</li>";
    echo "<li>Service Category ID: $service_id</li>";
    echo "</ul>";
    
    echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✓ Database setup completed successfully!</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
