<?php
session_start();
require_once 'config/database.php';

echo "<h2>🎯 Final Quotes Page Test</h2>";

try {
    // Step 1: Get contractor for testing
    $stmt = $pdo->query("
        SELECT u.id, u.email, cp.business_name, cp.service_areas, cp.service_types
        FROM users u 
        JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        AND cp.service_areas IS NOT NULL AND cp.service_areas != '' AND cp.service_areas != '[]'
        AND cp.service_types IS NOT NULL AND cp.service_types != '' AND cp.service_types != '[]'
        LIMIT 1
    ");
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p style='color: red;'>❌ No contractors with complete data found</p>";
        echo "<p>Run fix_quotes_page_display.php first to fix contractor data</p>";
        exit;
    }
    
    $contractor_id = $contractor['id'];
    echo "<p><strong>Testing contractor:</strong> " . htmlspecialchars($contractor['business_name']) . " (ID: $contractor_id)</p>";
    
    $service_areas = json_decode($contractor['service_areas'], true);
    $service_types = json_decode($contractor['service_types'], true);
    
    echo "<p><strong>Service Areas:</strong> " . implode(', ', $service_areas) . "</p>";
    echo "<p><strong>Service Types:</strong> " . implode(', ', $service_types) . "</p>";
    
    // Step 2: Check quotes in system
    $stmt = $pdo->query("SELECT COUNT(*) FROM quote_requests WHERE status = 'open'");
    $open_quotes = $stmt->fetchColumn();
    
    echo "<p><strong>Open quotes in system:</strong> $open_quotes</p>";
    
    if ($open_quotes == 0) {
        echo "<p style='color: orange;'>⚠️ No open quotes in system. Run fix_quotes_page_display.php to create test quotes.</p>";
    }
    
    // Step 3: Test quotes page URL with debug mode
    $quotes_url = "http://localhost/Brick2/contractor/quotes.php?debug=1";
    echo "<h3>Step 3: Test Quotes Page</h3>";
    echo "<p><strong>Quotes page URL with debug:</strong> <a href='$quotes_url' target='_blank'>$quotes_url</a></p>";
    
    // Step 4: Test different filter URLs
    echo "<h3>Step 4: Test Filter URLs</h3>";
    $filters = [
        'all' => 'All Quotes',
        'pending' => 'Pending Quotes',
        'responded' => 'Responded Quotes',
        'completed' => 'Completed Quotes',
        'cancelled' => 'Cancelled Quotes'
    ];
    
    echo "<ul>";
    foreach ($filters as $filter => $label) {
        $filter_url = "http://localhost/Brick2/contractor/quotes.php?status=$filter&debug=1";
        echo "<li><a href='$filter_url' target='_blank'>$label</a></li>";
    }
    echo "</ul>";
    
    // Step 5: Test search functionality
    echo "<h3>Step 5: Test Search</h3>";
    $search_url = "http://localhost/Brick2/contractor/quotes.php?search=test&debug=1";
    echo "<p><strong>Search test URL:</strong> <a href='$search_url' target='_blank'>Search for 'test'</a></p>";
    
    // Step 6: Simulate login and test backend logic
    echo "<h3>Step 6: Backend Logic Test</h3>";
    
    // Simulate session
    $_SESSION['user_id'] = $contractor_id;
    $_SESSION['user_type'] = 'contractor';
    
    // Test the exact query from quotes.php for pending quotes
    $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
    $params = [$contractor_id];
    $where_conditions[] = "qr.status = 'open' AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)";
    $params[] = $contractor_id;
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
               cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");
    
    $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
    $stmt->execute($query_params);
    $all_quotes = $stmt->fetchAll();
    
    echo "<p><strong>SQL query returned:</strong> " . count($all_quotes) . " quotes</p>";
    
    // Filter in PHP
    $filtered_quotes = [];
    foreach ($all_quotes as $quote) {
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $filtered_quotes[] = $quote;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $filtered_quotes[] = $quote;
            }
        }
    }
    
    echo "<p><strong>After PHP filtering:</strong> " . count($filtered_quotes) . " quotes</p>";
    
    if (count($filtered_quotes) > 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 1rem; border-radius: 5px; margin: 1rem 0;'>";
        echo "<h4 style='color: #155724; margin: 0 0 0.5rem 0;'>✅ SUCCESS!</h4>";
        echo "<p style='color: #155724; margin: 0;'>The quotes page should be working correctly. The contractor will see " . count($filtered_quotes) . " quotes.</p>";
        echo "</div>";
        
        echo "<p><strong>Sample quotes that will be visible:</strong></p>";
        foreach (array_slice($filtered_quotes, 0, 3) as $quote) {
            echo "<div style='border: 1px solid #28a745; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
            echo "<strong>Quote ID:</strong> " . $quote['id'] . "<br>";
            echo "<strong>Title:</strong> " . htmlspecialchars($quote['title']) . "<br>";
            echo "<strong>Customer:</strong> " . htmlspecialchars($quote['first_name'] . ' ' . $quote['last_name']) . "<br>";
            echo "<strong>District:</strong> " . $quote['district'] . "<br>";
            echo "<strong>Service:</strong> " . htmlspecialchars($quote['service_category']) . "<br>";
            echo "<strong>Type:</strong> " . $quote['quote_type'] . "<br>";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 1rem; border-radius: 5px; margin: 1rem 0;'>";
        echo "<h4 style='color: #721c24; margin: 0 0 0.5rem 0;'>❌ ISSUE DETECTED</h4>";
        echo "<p style='color: #721c24; margin: 0;'>No quotes will be visible to this contractor. This could be due to:</p>";
        echo "<ul style='color: #721c24;'>";
        echo "<li>No quotes match the contractor's service areas and types</li>";
        echo "<li>All quotes have already been responded to</li>";
        echo "<li>No open quotes in the system</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    // Step 7: Instructions
    echo "<h3>Step 7: Next Steps</h3>";
    echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 1rem; border-radius: 5px;'>";
    echo "<h4>To test the quotes page:</h4>";
    echo "<ol>";
    echo "<li>Click on the quotes page URL above to open it in a new tab</li>";
    echo "<li>You should see debug information at the top of the page</li>";
    echo "<li>Test each filter button (All, Pending, Responded, etc.)</li>";
    echo "<li>Test the search functionality</li>";
    echo "<li>Verify that quotes are displaying correctly</li>";
    echo "</ol>";
    echo "<p><strong>Note:</strong> The debug parameter (?debug=1) shows additional information to help troubleshoot issues.</p>";
    echo "</div>";
    
    // Step 8: Summary
    echo "<h3>🎯 Test Summary</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 1rem 0;'>";
    echo "<tr style='background: #f8f9fa;'><th>Component</th><th>Status</th><th>Details</th></tr>";
    echo "<tr><td>Contractor Data</td><td style='color: green;'>✅ Complete</td><td>Service areas and types properly configured</td></tr>";
    echo "<tr><td>Database Queries</td><td style='color: green;'>✅ Working</td><td>SQL queries executing successfully</td></tr>";
    echo "<tr><td>PHP Filtering</td><td style='color: green;'>✅ Working</td><td>Service/area matching logic functioning</td></tr>";
    echo "<tr><td>Quote Display</td><td style='color: " . (count($filtered_quotes) > 0 ? 'green;">✅ Working' : 'red;">❌ No Quotes') . "</td><td>" . count($filtered_quotes) . " quotes will be visible</td></tr>";
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
