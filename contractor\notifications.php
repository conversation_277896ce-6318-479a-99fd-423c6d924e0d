<?php
// Include authentication and authorization check
require_once 'includes/auth_check.php';

// Contractor data is now available in $contractor variable

// Get recent activities/notifications
$notifications = [];
$quote_notifications = [];
$review_notifications = [];
$response_notifications = [];
$payment_notifications = [];
$db_notifications = [];

try {
    // Recent quote requests - only show relevant quotes for this contractor
    $stmt = $pdo->prepare("
        SELECT 'quote_request' as type, qr.id, sc.name_en as service_category, qr.created_at,
               cp.first_name, cp.last_name, cp.district, qr.service_category_id, qr.specific_contractor_id,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY qr.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id'], $_SESSION['user_id']]);
    $all_quote_notifications = $stmt->fetchAll();

    // Filter quotes using the same logic as dashboard
    $quote_notifications = [];
    foreach ($all_quote_notifications as $quote) {
        // Always include direct quotes for this contractor
        if ($quote['specific_contractor_id'] == $_SESSION['user_id']) {
            $quote_notifications[] = $quote;
            continue;
        }

        // For general quotes (no specific contractor), check service and area match
        if ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($contractor['service_types'], true) ?: [];
            $contractor_areas = json_decode($contractor['service_areas'], true) ?: [];

            // Check if contractor provides this service
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);

            // Check if contractor serves this area
            $has_area = in_array($quote['district'], $contractor_areas);

            if ($has_service && $has_area) {
                $quote_notifications[] = $quote;
            }
        }

        // Limit to 10 notifications
        if (count($quote_notifications) >= 10) {
            break;
        }
    }

    // Recent reviews
    // Check which review field structure we're using
    $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'status'");
    $has_status = $stmt->fetch();

    if ($has_status) {
        $review_condition = "(r.status IS NULL OR r.status != 'deleted')";
    } else {
        $review_condition = "(r.is_approved IS NULL OR r.is_approved != 0)";
    }

    $stmt = $pdo->prepare("
        SELECT 'review' as type, r.id, r.rating, r.review_text as comment, r.created_at,
               cp.first_name, cp.last_name, sc.name_en as service_category
        FROM reviews r
        JOIN customer_profiles cp ON r.customer_id = cp.user_id
        LEFT JOIN quote_responses qres ON r.quote_response_id = qres.id
        LEFT JOIN quote_requests qr ON qres.quote_request_id = qr.id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE r.contractor_id = ? AND $review_condition
        AND r.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY r.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $review_notifications = $stmt->fetchAll();

    // Quote responses status updates
    $stmt = $pdo->prepare("
        SELECT 'quote_response' as type, qr.id, sc.name_en as service_category, qres.status, qres.updated_at as created_at,
               cp.first_name, cp.last_name, qres.quoted_amount
        FROM quote_responses qres
        JOIN quote_requests qr ON qres.quote_request_id = qr.id
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        JOIN service_categories sc ON qr.service_category_id = sc.id
        WHERE qres.contractor_id = ?
        AND qres.updated_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND qres.status != 'pending'
        ORDER BY qres.updated_at DESC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $response_notifications = $stmt->fetchAll();

    // Payment notifications
    $stmt = $pdo->prepare("
        SELECT 'payment' as type, pp.id, pp.amount, pp.payment_type, pp.payment_date as created_at,
               qr.title as service_category, cp.first_name, cp.last_name, pp.transaction_id
        FROM project_payments pp
        JOIN quote_responses qres ON pp.quote_response_id = qres.id
        JOIN quote_requests qr ON qres.quote_request_id = qr.id
        JOIN customer_profiles cp ON pp.customer_id = cp.user_id
        WHERE pp.contractor_id = ?
        AND pp.payment_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND pp.payment_status = 'completed'
        ORDER BY pp.payment_date DESC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $payment_notifications = $stmt->fetchAll();

    // Database notifications (from notifications table)
    $stmt = $pdo->prepare("
        SELECT 'notification' as type, n.id, n.title, n.message, n.created_at, n.type as notification_type
        FROM notifications n
        WHERE n.user_id = ?
        AND n.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND n.type IN ('payment_received', 'review_received', 'quote_received')
        ORDER BY n.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $db_notifications = $stmt->fetchAll();

    // Combine and sort all notifications
    $notifications = array_merge($quote_notifications, $review_notifications, $response_notifications, $payment_notifications, $db_notifications);
    
    // Sort by date
    usort($notifications, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    
    // Limit to 20 most recent
    $notifications = array_slice($notifications, 0, 20);
    
} catch (PDOException $e) {
    $notifications = [];
    // Log error for debugging
    error_log("Notifications error: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Notifications - BrickClick Contractor</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet"> 
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF6B35;
            --accent-yellow: #F7931E;
            --success-green: #28a745;
            --info-blue: #17a2b8;
            --warning-orange: #ffc107;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --dark-gray: #343a40;
        }
        
        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e9ecef 100%);
            font-family: 'Open Sans', sans-serif;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            z-index: 1000;
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            flex-shrink: 0;
        }

        .sidebar-header h3 {
            color: var(--accent-orange);
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }

        .sidebar-header p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }

        .sidebar-nav {
            flex: 1;
            padding: 0.5rem 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .nav-item {
            margin: 0.15rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,107,53,0.1);
            color: var(--accent-orange);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--accent-orange), var(--primary-red));
            color: white;
            box-shadow: 0 3px 10px rgba(255,107,53,0.3);
        }

        .nav-link i {
            margin-right: 0.8rem;
            width: 18px;
            text-align: center;
            font-size: 1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--info-blue), #138496);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .page-title {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 2rem;
        }
        
        .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }
        
        .notifications-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .notification-item {
            display: flex;
            align-items: start;
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .notification-item:hover {
            background: var(--light-gray);
        }
        
        .notification-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            margin-right: 1.5rem;
            flex-shrink: 0;
        }
        
        .notification-icon.quote {
            background: linear-gradient(135deg, var(--info-blue), #138496);
        }
        
        .notification-icon.review {
            background: linear-gradient(135deg, var(--accent-yellow), #e0a800);
        }
        
        .notification-icon.response {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
        }
        
        .notification-icon.response.rejected {
            background: linear-gradient(135deg, var(--primary-red), #bd2130);
        }

        .notification-icon.payment {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
        }

        .notification-icon.notification {
            background: linear-gradient(135deg, var(--info-blue), #138496);
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-title {
            color: var(--primary-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .notification-description {
            color: var(--medium-gray);
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }
        
        .notification-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.9rem;
            color: var(--medium-gray);
        }
        
        .notification-time {
            color: var(--medium-gray);
            font-size: 0.9rem;
            text-align: right;
            flex-shrink: 0;
        }
        
        .rating-stars {
            color: var(--accent-yellow);
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--medium-gray);
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .empty-state h3 {
            margin-bottom: 1rem;
        }
        
        .btn-action {
            background: linear-gradient(135deg, var(--accent-orange), #e55a2b);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: inline-block;
            margin-top: 0.5rem;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            color: white;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>BrickClick</h3>
            <p>Contractor Portal</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice-dollar"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="portfolio.php" class="nav-link">
                    <i class="fas fa-images"></i>Portfolio
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews & Ratings
                </a>
            </div>
            <div class="nav-item">
                <a href="profile.php" class="nav-link">
                    <i class="fas fa-user-edit"></i>Profile & Services
                </a>
            </div>
            <div class="nav-item">
                <a href="payment_history.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payment History
                </a>
            </div>
            <div class="nav-item">
                <a href="notifications.php" class="nav-link active">
                    <i class="fas fa-bell"></i>Notifications
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Notifications</h1>
            <p class="page-subtitle">Stay updated with recent activities and important updates</p>
        </div>



        <!-- Notifications Container -->
        <div class="notifications-container">
            <?php if (empty($notifications)): ?>
                <div class="empty-state">
                    <i class="fas fa-bell"></i>
                    <h3>No recent notifications</h3>
                    <p>You're all caught up! New notifications will appear here when there are updates to your quotes, reviews, or account.</p>
                </div>
            <?php else: ?>
                <?php foreach ($notifications as $notification): ?>
                    <div class="notification-item">
                        <?php if ($notification['type'] === 'quote_request'): ?>
                            <div class="notification-icon quote">
                                <i class="fas fa-file-invoice-dollar"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">New Quote Request</div>
                                <div class="notification-description">
                                    <?php echo htmlspecialchars($notification['first_name'] . ' ' . $notification['last_name']); ?> 
                                    requested a quote for <?php echo htmlspecialchars($notification['service_category']); ?>
                                </div>
                                <div class="notification-meta">
                                    <span>
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo htmlspecialchars($notification['district']); ?>
                                    </span>
                                </div>
                                <?php if ($notification['has_responded'] == 0): ?>
                                    <a href="respond_quote.php?id=<?php echo $notification['id']; ?>" class="btn-action">
                                        <i class="fas fa-reply me-1"></i>Respond
                                    </a>
                                <?php endif; ?>
                            </div>
                            
                        <?php elseif ($notification['type'] === 'review'): ?>
                            <div class="notification-icon review">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">New Review Received</div>
                                <div class="notification-description">
                                    <?php echo htmlspecialchars($notification['first_name'] . ' ' . $notification['last_name']); ?> 
                                    left you a <?php echo $notification['rating']; ?>-star review
                                    <?php if ($notification['service_category']): ?>
                                        for <?php echo htmlspecialchars($notification['service_category']); ?>
                                    <?php endif; ?>
                                </div>
                                <div class="notification-meta">
                                    <span class="rating-stars">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star<?php echo $i <= $notification['rating'] ? '' : '-o'; ?>"></i>
                                        <?php endfor; ?>
                                    </span>
                                </div>
                                <?php if ($notification['comment']): ?>
                                    <div style="font-style: italic; color: var(--dark-gray); margin-top: 0.5rem;">
                                        "<?php echo htmlspecialchars(substr($notification['comment'], 0, 100)) . (strlen($notification['comment']) > 100 ? '...' : ''); ?>"
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                        <?php elseif ($notification['type'] === 'quote_response'): ?>
                            <div class="notification-icon response <?php echo $notification['status'] === 'rejected' ? 'rejected' : ''; ?>">
                                <i class="fas fa-<?php echo $notification['status'] === 'accepted' ? 'check' : ($notification['status'] === 'rejected' ? 'times' : 'clock'); ?>"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">Quote Response Update</div>
                                <div class="notification-description">
                                    Your quote for <?php echo htmlspecialchars($notification['service_category']); ?>
                                    from <?php echo htmlspecialchars($notification['first_name'] . ' ' . $notification['last_name']); ?>
                                    was <?php echo $notification['status']; ?>
                                </div>
                                <div class="notification-meta">
                                    <span>
                                        <i class="fas fa-dollar-sign me-1"></i>
                                        Rs. <?php echo number_format($notification['quoted_amount']); ?>
                                    </span>
                                </div>
                            </div>

                        <?php elseif ($notification['type'] === 'payment'): ?>
                            <div class="notification-icon payment">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">Payment Received</div>
                                <div class="notification-description">
                                    You received a <?php echo ucfirst(str_replace('_', ' ', $notification['payment_type'])); ?>
                                    of Rs. <?php echo number_format($notification['amount'], 2); ?>
                                    from <?php echo htmlspecialchars($notification['first_name'] . ' ' . $notification['last_name']); ?>
                                    <?php if ($notification['service_category']): ?>
                                        for <?php echo htmlspecialchars($notification['service_category']); ?>
                                    <?php endif; ?>
                                </div>
                                <div class="notification-meta">
                                    <span>
                                        <i class="fas fa-receipt me-1"></i>
                                        <?php echo htmlspecialchars($notification['transaction_id']); ?>
                                    </span>
                                </div>
                            </div>

                        <?php elseif ($notification['type'] === 'notification'): ?>
                            <div class="notification-icon notification">
                                <i class="fas fa-<?php echo $notification['notification_type'] === 'payment_received' ? 'credit-card' : ($notification['notification_type'] === 'review_received' ? 'star' : 'bell'); ?>"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                <div class="notification-description">
                                    <?php echo htmlspecialchars($notification['message']); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="notification-time">
                            <?php 
                            $time_diff = time() - strtotime($notification['created_at']);
                            if ($time_diff < 3600) {
                                echo floor($time_diff / 60) . ' min ago';
                            } elseif ($time_diff < 86400) {
                                echo floor($time_diff / 3600) . ' hr ago';
                            } else {
                                echo floor($time_diff / 86400) . ' day' . (floor($time_diff / 86400) > 1 ? 's' : '') . ' ago';
                            }
                            ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
