<?php
require_once 'database.php';

// Set execution time limit for large data insertion
set_time_limit(300);

echo "<h2>🏗️ Adding 20 New Contractor Profiles</h2>";

// Contractor data array
$contractors = [
    [
        'email' => '<EMAIL>',
        'business_name' => 'Samantha Construction Ltd',
        'contact_person' => '<PERSON>',
        'phone' => '+94 77 234 5678',
        'business_address' => 'No. 123, Kandy Road, Colombo 07, Sri Lanka',
        'service_areas' => ['Colombo', 'Gampaha', 'Kalutara'],
        'service_types' => ['House Construction', 'Building Renovation', 'Interior Design & Finishing'],
        'cida_registration' => 'CIDA/REG/2023/001235',
        'cida_grade' => 'C4',
        'business_description' => 'Samantha Construction Ltd specializes in luxury residential construction and high-end renovations. With 12 years of experience, we focus on modern architectural designs and sustainable building practices.',
        'website' => 'https://www.samanthaconstruction.lk',
        'average_rating' => 4.6,
        'total_reviews' => 18,
        'total_projects' => 32
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Royal Construction Group',
        'contact_person' => 'Nimal Fernando',
        'phone' => '+94 71 345 6789',
        'business_address' => 'No. 456, Galle Road, Dehiwala, Sri Lanka',
        'service_areas' => ['Colombo', 'Kalutara', 'Galle'],
        'service_types' => ['Commercial Construction', 'House Construction', 'Swimming Pool Construction'],
        'cida_registration' => 'CIDA/REG/2023/001236',
        'cida_grade' => 'C3',
        'business_description' => 'Royal Construction Group is a premier construction company specializing in commercial buildings, luxury homes, and resort developments. We have completed over 50 major projects across the Western and Southern provinces.',
        'website' => 'https://www.royalconstruction.lk',
        'average_rating' => 4.8,
        'total_reviews' => 25,
        'total_projects' => 58
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Green Earth Builders',
        'contact_person' => 'Ruwan Silva',
        'phone' => '+94 76 456 7890',
        'business_address' => 'No. 789, Negombo Road, Wattala, Sri Lanka',
        'service_areas' => ['Gampaha', 'Colombo', 'Puttalam'],
        'service_types' => ['House Construction', 'Landscaping & Gardening', 'Roofing & Waterproofing'],
        'cida_registration' => 'CIDA/REG/2023/001237',
        'cida_grade' => 'C5',
        'business_description' => 'Green Earth Builders focuses on eco-friendly construction solutions and sustainable building practices. We specialize in green buildings, solar installations, and environmentally conscious construction methods.',
        'website' => 'https://www.greenearthbuilders.lk',
        'average_rating' => 4.4,
        'total_reviews' => 15,
        'total_projects' => 28
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Modern Homes Lanka',
        'contact_person' => 'Priya Jayawardena',
        'phone' => '+94 75 567 8901',
        'business_address' => 'No. 321, Malabe Road, Kaduwela, Sri Lanka',
        'service_areas' => ['Colombo', 'Gampaha', 'Kandy'],
        'service_types' => ['House Construction', 'Interior Design & Finishing', 'Electrical Work'],
        'cida_registration' => 'CIDA/REG/2023/001238',
        'cida_grade' => 'C4',
        'business_description' => 'Modern Homes Lanka creates contemporary living spaces with cutting-edge design and smart home technology. We specialize in modern villa construction and luxury apartment developments.',
        'website' => 'https://www.modernhomeslanka.com',
        'average_rating' => 4.7,
        'total_reviews' => 22,
        'total_projects' => 41
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Elite Engineering Solutions',
        'contact_person' => 'Chaminda Rathnayake',
        'phone' => '+94 77 678 9012',
        'business_address' => 'No. 654, Peradeniya Road, Kandy, Sri Lanka',
        'service_areas' => ['Kandy', 'Matale', 'Nuwara Eliya'],
        'service_types' => ['Commercial Construction', 'Road & Infrastructure', 'Electrical Work'],
        'cida_registration' => 'CIDA/REG/2023/001239',
        'cida_grade' => 'C2',
        'business_description' => 'Elite Engineering Solutions is a leading infrastructure development company with expertise in commercial buildings, road construction, and large-scale engineering projects in the Central Province.',
        'website' => 'https://www.eliteengineering.lk',
        'average_rating' => 4.9,
        'total_reviews' => 31,
        'total_projects' => 67
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Crystal Construction Co.',
        'contact_person' => 'Anura Wickramasinghe',
        'phone' => '+94 71 789 0123',
        'business_address' => 'No. 987, Matara Road, Galle, Sri Lanka',
        'service_areas' => ['Galle', 'Matara', 'Hambantota'],
        'service_types' => ['House Construction', 'Building Renovation', 'Swimming Pool Construction'],
        'cida_registration' => 'CIDA/REG/2023/001240',
        'cida_grade' => 'C4',
        'business_description' => 'Crystal Construction Co. specializes in coastal construction projects, luxury beach houses, and resort developments. We have extensive experience in construction near coastal areas with proper waterproofing solutions.',
        'website' => 'https://www.crystalconstruction.lk',
        'average_rating' => 4.5,
        'total_reviews' => 19,
        'total_projects' => 35
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Apex Builders & Developers',
        'contact_person' => 'Lasantha Gunasekara',
        'phone' => '+94 76 890 1234',
        'business_address' => 'No. 147, Kurunegala Road, Puttalam, Sri Lanka',
        'service_areas' => ['Puttalam', 'Kurunegala', 'Anuradhapura'],
        'service_types' => ['Commercial Construction', 'House Construction', 'Road & Infrastructure'],
        'cida_registration' => 'CIDA/REG/2023/001241',
        'cida_grade' => 'C3',
        'business_description' => 'Apex Builders & Developers is a versatile construction company handling both residential and commercial projects. We specialize in large-scale developments and infrastructure projects in the North Western Province.',
        'website' => 'https://www.apexbuilders.lk',
        'average_rating' => 4.6,
        'total_reviews' => 24,
        'total_projects' => 49
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Sunshine Home Builders',
        'contact_person' => 'Malini Rajapaksa',
        'phone' => '+94 75 901 2345',
        'business_address' => 'No. 258, Ratnapura Road, Kegalle, Sri Lanka',
        'service_areas' => ['Kegalle', 'Ratnapura', 'Colombo'],
        'service_types' => ['House Construction', 'Interior Design & Finishing', 'Landscaping & Gardening'],
        'cida_registration' => 'CIDA/REG/2023/001242',
        'cida_grade' => 'C5',
        'business_description' => 'Sunshine Home Builders creates beautiful family homes with a focus on comfort and functionality. We specialize in mid-range housing projects and complete home solutions including interior design and landscaping.',
        'website' => 'https://www.sunshinehomes.lk',
        'average_rating' => 4.3,
        'total_reviews' => 16,
        'total_projects' => 29
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Diamond Construction Ltd',
        'contact_person' => 'Sunil Mendis',
        'phone' => '+94 77 012 3456',
        'business_address' => 'No. 369, Badulla Road, Monaragala, Sri Lanka',
        'service_areas' => ['Monaragala', 'Badulla', 'Ampara'],
        'service_types' => ['House Construction', 'Building Renovation', 'Roofing & Waterproofing'],
        'cida_registration' => 'CIDA/REG/2023/001243',
        'cida_grade' => 'C4',
        'business_description' => 'Diamond Construction Ltd serves the Uva Province with quality construction services. We specialize in residential construction, renovations, and roofing solutions adapted to the regional climate and terrain.',
        'website' => 'https://www.diamondconstruction.lk',
        'average_rating' => 4.4,
        'total_reviews' => 14,
        'total_projects' => 26
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Platinum Builders Group',
        'contact_person' => 'Ravi Dissanayake',
        'phone' => '+94 71 123 4567',
        'business_address' => 'No. 741, Jaffna Road, Vavuniya, Sri Lanka',
        'service_areas' => ['Vavuniya', 'Mannar', 'Jaffna'],
        'service_types' => ['Commercial Construction', 'House Construction', 'Road & Infrastructure'],
        'cida_registration' => 'CIDA/REG/2023/001244',
        'cida_grade' => 'C3',
        'business_description' => 'Platinum Builders Group is a leading construction company in the Northern Province, specializing in post-conflict reconstruction and development projects. We focus on community buildings and infrastructure development.',
        'website' => 'https://www.platinumbuilders.lk',
        'average_rating' => 4.7,
        'total_reviews' => 21,
        'total_projects' => 44
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Golden Gate Constructions',
        'contact_person' => 'Kumara Bandara',
        'phone' => '+94 76 234 5678',
        'business_address' => 'No. 852, Trincomalee Road, Batticaloa, Sri Lanka',
        'service_areas' => ['Batticaloa', 'Ampara', 'Trincomalee'],
        'service_types' => ['House Construction', 'Commercial Construction', 'Electrical Work'],
        'cida_registration' => 'CIDA/REG/2023/001245',
        'cida_grade' => 'C4',
        'business_description' => 'Golden Gate Constructions serves the Eastern Province with comprehensive construction services. We specialize in both residential and commercial projects with a focus on modern design and quality craftsmanship.',
        'website' => 'https://www.goldengateconstructions.lk',
        'average_rating' => 4.5,
        'total_reviews' => 17,
        'total_projects' => 33
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Supreme Builders Lanka',
        'contact_person' => 'Indika Samaraweera',
        'phone' => '+94 75 345 6789',
        'business_address' => 'No. 963, Polonnaruwa Road, Dambulla, Sri Lanka',
        'service_areas' => ['Matale', 'Polonnaruwa', 'Anuradhapura'],
        'service_types' => ['House Construction', 'Building Renovation', 'Plumbing & Sanitation'],
        'cida_registration' => 'CIDA/REG/2023/001246',
        'cida_grade' => 'C5',
        'business_description' => 'Supreme Builders Lanka focuses on traditional and modern construction techniques. We serve the North Central Province with expertise in residential construction and complete plumbing solutions.',
        'website' => 'https://www.supremebuilders.lk',
        'average_rating' => 4.2,
        'total_reviews' => 13,
        'total_projects' => 24
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Metro Construction Works',
        'contact_person' => 'Tharaka Wijesinghe',
        'phone' => '+94 77 456 7890',
        'business_address' => 'No. 174, High Level Road, Maharagama, Sri Lanka',
        'service_areas' => ['Colombo', 'Kalutara', 'Gampaha'],
        'service_types' => ['Commercial Construction', 'Interior Design & Finishing', 'Electrical Work'],
        'cida_registration' => 'CIDA/REG/2023/001247',
        'cida_grade' => 'C3',
        'business_description' => 'Metro Construction Works specializes in urban construction projects including office buildings, shopping complexes, and high-rise apartments. We excel in modern construction techniques and project management.',
        'website' => 'https://www.metroconstructions.lk',
        'average_rating' => 4.8,
        'total_reviews' => 29,
        'total_projects' => 52
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Heritage Builders & Associates',
        'contact_person' => 'Chandana Karunaratne',
        'phone' => '+94 71 567 8901',
        'business_address' => 'No. 285, Sigiriya Road, Habarana, Sri Lanka',
        'service_areas' => ['Anuradhapura', 'Polonnaruwa', 'Matale'],
        'service_types' => ['House Construction', 'Building Renovation', 'Landscaping & Gardening'],
        'cida_registration' => 'CIDA/REG/2023/001248',
        'cida_grade' => 'C4',
        'business_description' => 'Heritage Builders & Associates combines traditional Sri Lankan architecture with modern construction methods. We specialize in heritage-style homes and cultural building projects.',
        'website' => 'https://www.heritagebuilders.lk',
        'average_rating' => 4.6,
        'total_reviews' => 20,
        'total_projects' => 38
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Ocean View Constructions',
        'contact_person' => 'Nuwan Jayasuriya',
        'phone' => '+94 76 678 9012',
        'business_address' => 'No. 396, Coastal Road, Hikkaduwa, Sri Lanka',
        'service_areas' => ['Galle', 'Matara', 'Kalutara'],
        'service_types' => ['House Construction', 'Swimming Pool Construction', 'Roofing & Waterproofing'],
        'cida_registration' => 'CIDA/REG/2023/001249',
        'cida_grade' => 'C4',
        'business_description' => 'Ocean View Constructions specializes in coastal properties and beach houses. We have extensive experience in construction near the ocean with proper corrosion protection and waterproofing systems.',
        'website' => 'https://www.oceanviewconstructions.lk',
        'average_rating' => 4.7,
        'total_reviews' => 23,
        'total_projects' => 42
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Peak Construction Company',
        'contact_person' => 'Asanka Perera',
        'phone' => '+94 75 789 0123',
        'business_address' => 'No. 507, Nuwara Eliya Road, Hatton, Sri Lanka',
        'service_areas' => ['Nuwara Eliya', 'Kandy', 'Badulla'],
        'service_types' => ['House Construction', 'Commercial Construction', 'Roofing & Waterproofing'],
        'cida_registration' => 'CIDA/REG/2023/001250',
        'cida_grade' => 'C5',
        'business_description' => 'Peak Construction Company specializes in hill country construction with expertise in cold climate building techniques. We focus on tea estate buildings, mountain homes, and tourism infrastructure.',
        'website' => 'https://www.peakconstruction.lk',
        'average_rating' => 4.4,
        'total_reviews' => 16,
        'total_projects' => 31
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Star Construction Group',
        'contact_person' => 'Dilshan Rodrigo',
        'phone' => '+94 77 890 1234',
        'business_address' => 'No. 618, Chilaw Road, Negombo, Sri Lanka',
        'service_areas' => ['Gampaha', 'Puttalam', 'Colombo'],
        'service_types' => ['Commercial Construction', 'House Construction', 'Swimming Pool Construction'],
        'cida_registration' => 'CIDA/REG/2023/001251',
        'cida_grade' => 'C3',
        'business_description' => 'Star Construction Group is known for innovative construction solutions and timely project delivery. We specialize in resort construction, commercial buildings, and luxury residential projects.',
        'website' => 'https://www.starconstruction.lk',
        'average_rating' => 4.8,
        'total_reviews' => 27,
        'total_projects' => 48
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Unity Builders & Contractors',
        'contact_person' => 'Mahesh Gunathilaka',
        'phone' => '+94 71 901 2345',
        'business_address' => 'No. 729, Embilipitiya Road, Ratnapura, Sri Lanka',
        'service_areas' => ['Ratnapura', 'Kegalle', 'Kalutara'],
        'service_types' => ['House Construction', 'Building Renovation', 'Landscaping & Gardening'],
        'cida_registration' => 'CIDA/REG/2023/001252',
        'cida_grade' => 'C4',
        'business_description' => 'Unity Builders & Contractors provides comprehensive construction services in the Sabaragamuwa Province. We focus on sustainable construction practices and community development projects.',
        'website' => 'https://www.unitybuilders.lk',
        'average_rating' => 4.3,
        'total_reviews' => 15,
        'total_projects' => 27
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Prime Construction Solutions',
        'contact_person' => 'Janaka Wickremasinghe',
        'phone' => '+94 76 012 3456',
        'business_address' => 'No. 840, Wellawaya Road, Monaragala, Sri Lanka',
        'service_areas' => ['Monaragala', 'Badulla', 'Hambantota'],
        'service_types' => ['House Construction', 'Road & Infrastructure', 'Electrical Work'],
        'cida_registration' => 'CIDA/REG/2023/001253',
        'cida_grade' => 'C3',
        'business_description' => 'Prime Construction Solutions specializes in infrastructure development and residential construction in rural areas. We focus on sustainable development and community infrastructure projects.',
        'website' => 'https://www.primeconstructions.lk',
        'average_rating' => 4.5,
        'total_reviews' => 18,
        'total_projects' => 34
    ],
    [
        'email' => '<EMAIL>',
        'business_name' => 'Vision Builders International',
        'contact_person' => 'Roshan Amarasinghe',
        'phone' => '+94 75 123 4567',
        'business_address' => 'No. 951, Kilinochchi Road, Mannar, Sri Lanka',
        'service_areas' => ['Mannar', 'Vavuniya', 'Jaffna'],
        'service_types' => ['Commercial Construction', 'House Construction', 'Road & Infrastructure'],
        'cida_registration' => 'CIDA/REG/2023/001254',
        'cida_grade' => 'C2',
        'business_description' => 'Vision Builders International is committed to rebuilding and developing the Northern Province. We specialize in large-scale infrastructure projects, community buildings, and modern residential developments.',
        'website' => 'https://www.visionbuilders.lk',
        'average_rating' => 4.9,
        'total_reviews' => 32,
        'total_projects' => 61
    ]
];

// Portfolio projects data for each contractor
$portfolio_templates = [
    [
        'project_name' => 'Luxury Villa - Colombo',
        'project_description' => 'A stunning 4-bedroom luxury villa featuring modern architecture, smart home automation, premium finishes, and a beautiful garden with swimming pool.',
        'project_location' => 'Colombo 07, Sri Lanka',
        'project_value' => 28000000.00,
        'completion_date' => '2023-09-15',
        'is_featured' => 1
    ],
    [
        'project_name' => 'Commercial Office Building',
        'project_description' => 'A 6-story modern office complex with glass facade, central air conditioning, high-speed elevators, and underground parking facility.',
        'project_location' => 'Colombo 03, Sri Lanka',
        'project_value' => 55000000.00,
        'completion_date' => '2023-07-20',
        'is_featured' => 1
    ],
    [
        'project_name' => 'Apartment Renovation',
        'project_description' => 'Complete renovation of a 3-bedroom apartment including modern kitchen, luxury bathrooms, premium flooring, and contemporary interior design.',
        'project_location' => 'Kandy, Sri Lanka',
        'project_value' => 4500000.00,
        'completion_date' => '2023-10-05',
        'is_featured' => 0
    ],
    [
        'project_name' => 'Resort Development',
        'project_description' => 'A boutique beach resort with 20 rooms, restaurant, spa facilities, swimming pool, and landscaped gardens designed for luxury tourism.',
        'project_location' => 'Galle, Sri Lanka',
        'project_value' => 85000000.00,
        'completion_date' => '2023-06-10',
        'is_featured' => 1
    ],
    [
        'project_name' => 'Shopping Complex',
        'project_description' => 'A modern 3-story shopping complex with retail spaces, food court, cinema, and ample parking facilities in the heart of the city.',
        'project_location' => 'Gampaha, Sri Lanka',
        'project_value' => 42000000.00,
        'completion_date' => '2023-08-25',
        'is_featured' => 0
    ],
    [
        'project_name' => 'Residential Housing Project',
        'project_description' => 'A gated community of 15 modern houses with 3-4 bedrooms each, featuring contemporary design, security systems, and community facilities.',
        'project_location' => 'Kalutara, Sri Lanka',
        'project_value' => 75000000.00,
        'completion_date' => '2023-05-30',
        'is_featured' => 1
    ]
];

// Sample project images (placeholder URLs)
$sample_images = [
    'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1600607688969-a5bfcd646154?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1600607688960-e095ff8d5c6e?w=800&h=600&fit=crop'
];

try {
    $pdo->beginTransaction();

    $created_contractors = [];
    $password = password_hash('contractor123', PASSWORD_DEFAULT);

    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🚀 Creating Contractor Profiles...</h3>";

    foreach ($contractors as $index => $contractor_data) {
        echo "<p>Creating: " . htmlspecialchars($contractor_data['business_name']) . "...</p>";

        // Insert user
        $stmt = $pdo->prepare("INSERT INTO users (email, password, user_type, status, created_at, updated_at) VALUES (?, ?, 'contractor', 'approved', NOW(), NOW())");
        $stmt->execute([$contractor_data['email'], $password]);
        $user_id = $pdo->lastInsertId();

        // Insert contractor profile
        $stmt = $pdo->prepare("
            INSERT INTO contractor_profiles (
                user_id, business_name, contact_person, phone, business_address,
                service_areas, service_types, cida_registration, cida_grade,
                business_description, website, average_rating, total_reviews,
                total_projects, language_preference, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'en', NOW(), NOW())
        ");

        $stmt->execute([
            $user_id,
            $contractor_data['business_name'],
            $contractor_data['contact_person'],
            $contractor_data['phone'],
            $contractor_data['business_address'],
            json_encode($contractor_data['service_areas']),
            json_encode($contractor_data['service_types']),
            $contractor_data['cida_registration'],
            $contractor_data['cida_grade'],
            $contractor_data['business_description'],
            $contractor_data['website'],
            $contractor_data['average_rating'],
            $contractor_data['total_reviews'],
            $contractor_data['total_projects']
        ]);

        // Add portfolio projects (3-4 projects per contractor)
        $num_projects = rand(3, 4);
        for ($i = 0; $i < $num_projects; $i++) {
            $portfolio = $portfolio_templates[$i % count($portfolio_templates)];

            // Randomize some values
            $portfolio['project_value'] = $portfolio['project_value'] + rand(-5000000, 10000000);
            $portfolio['completion_date'] = date('Y-m-d', strtotime('-' . rand(30, 365) . ' days'));

            // Select random images for this project
            $project_images = array_slice($sample_images, 0, rand(2, 4));

            $stmt = $pdo->prepare("
                INSERT INTO contractor_portfolios (
                    contractor_id, project_name, project_description, project_location,
                    completion_date, project_value, project_images, is_featured, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");

            $stmt->execute([
                $user_id,
                $portfolio['project_name'] . ' - ' . $contractor_data['business_name'],
                $portfolio['project_description'],
                $portfolio['project_location'],
                $portfolio['completion_date'],
                $portfolio['project_value'],
                json_encode($project_images),
                $portfolio['is_featured']
            ]);
        }

        $created_contractors[] = [
            'email' => $contractor_data['email'],
            'business_name' => $contractor_data['business_name'],
            'contact_person' => $contractor_data['contact_person'],
            'user_id' => $user_id
        ];
    }

    $pdo->commit();
    echo "<p style='color: green; font-weight: bold;'>✅ All contractors created successfully!</p>";
    echo "</div>";

} catch (PDOException $e) {
    $pdo->rollback();
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ Error Creating Contractors</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    exit();
}

// Display all contractor credentials
echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔑 All Contractor Login Credentials</h3>";
echo "<p><strong>Password for all contractors:</strong> contractor123</p>";
echo "<table border='1' style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
echo "<tr style='background: #f0f0f0;'>";
echo "<th style='padding: 10px;'>Business Name</th>";
echo "<th style='padding: 10px;'>Contact Person</th>";
echo "<th style='padding: 10px;'>Email</th>";
echo "<th style='padding: 10px;'>Password</th>";
echo "<th style='padding: 10px;'>User ID</th>";
echo "</tr>";

foreach ($created_contractors as $contractor) {
    echo "<tr>";
    echo "<td style='padding: 10px;'>" . htmlspecialchars($contractor['business_name']) . "</td>";
    echo "<td style='padding: 10px;'>" . htmlspecialchars($contractor['contact_person']) . "</td>";
    echo "<td style='padding: 10px;'>" . htmlspecialchars($contractor['email']) . "</td>";
    echo "<td style='padding: 10px; font-weight: bold; color: #007bff;'>contractor123</td>";
    echo "<td style='padding: 10px;'>" . $contractor['user_id'] . "</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Summary statistics
echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>📊 Summary</h3>";
echo "<ul>";
echo "<li><strong>Total Contractors Created:</strong> " . count($created_contractors) . "</li>";
echo "<li><strong>Total Portfolio Projects:</strong> " . (count($created_contractors) * 3.5) . " (average)</li>";
echo "<li><strong>All contractors status:</strong> Approved (ready to login)</li>";
echo "<li><strong>Password for all:</strong> contractor123</li>";
echo "</ul>";
echo "</div>";

// Quick access links
echo "<div style='background: #e2e3e5; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔗 Quick Access</h3>";
echo "<p><a href='../login.php' target='_blank'>Contractor Login Page</a></p>";
echo "<p><a href='../admin/contractors.php' target='_blank'>Admin Contractors Management</a></p>";
echo "<p><a href='../contractors.php' target='_blank'>Browse All Contractors</a></p>";
echo "<p><a href='complete_debug.php'>Debug System Status</a></p>";
echo "</div>";

// Copy-paste credentials
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>📋 Copy-Paste Credentials List</h3>";
echo "<textarea style='width: 100%; height: 200px; font-family: monospace;' readonly>";
echo "CONTRACTOR LOGIN CREDENTIALS\n";
echo "Password for all: contractor123\n\n";
foreach ($created_contractors as $contractor) {
    echo "Business: " . $contractor['business_name'] . "\n";
    echo "Email: " . $contractor['email'] . "\n";
    echo "Password: contractor123\n";
    echo "Contact: " . $contractor['contact_person'] . "\n\n";
}
echo "</textarea>";
echo "</div>";

?>

<!DOCTYPE html>
<html>
<head>
    <title>20 New Contractors Added</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        h2, h3 { color: #333; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
        textarea { border: 1px solid #ccc; border-radius: 4px; padding: 10px; }
    </style>
</head>
<body>
    <h1>🏗️ BrickClick - 20 New Contractors Added Successfully!</h1>
    <p>All contractor profiles have been created with complete portfolio data and are ready for use.</p>
</body>
</html>
