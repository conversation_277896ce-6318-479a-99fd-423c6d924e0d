<?php
session_start();
require_once 'config/database.php';

echo "<h2>🧪 Test Contractor Access</h2>";

try {
    // Step 1: Check if we have contractors
    $stmt = $pdo->query("
        SELECT u.id, u.email, u.status, cp.business_name 
        FROM users u 
        LEFT JOIN contractor_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'contractor' AND u.status = 'approved'
        LIMIT 1
    ");
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        echo "<p style='color: red;'>❌ No approved contractors found</p>";
        echo "<p>Please run debug_contractor_quotes.php first to create test data</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Found contractor: " . htmlspecialchars($contractor['business_name']) . " (ID: " . $contractor['id'] . ")</p>";
    
    // Step 2: Simulate contractor login
    $_SESSION['user_id'] = $contractor['id'];
    $_SESSION['user_type'] = 'contractor';
    $_SESSION['email'] = $contractor['email'];
    
    echo "<p style='color: green;'>✅ Simulated contractor login</p>";
    
    // Step 3: Test the quotes query directly
    echo "<h3>Testing Quotes Query</h3>";
    
    $contractor_id = $contractor['id'];
    
    // Use the exact query from contractor/quotes.php
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
               cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        JOIN service_categories sc ON qr.service_category_id = sc.id
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND (qr.status IN ('open', 'completed', 'cancelled') OR qr.id IN (SELECT quote_request_id FROM quote_responses WHERE contractor_id = ?))
        ORDER BY qr.created_at DESC
    ");
    
    $query_params = [$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id];
    $stmt->execute($query_params);
    $all_quotes = $stmt->fetchAll();
    
    echo "<p>SQL query returned " . count($all_quotes) . " quotes</p>";
    
    if (empty($all_quotes)) {
        echo "<p style='color: red;'>❌ No quotes found by SQL query</p>";
        
        // Debug: Check if the JOIN is working
        echo "<h4>Debug: Check contractor profile</h4>";
        $stmt = $pdo->prepare("SELECT * FROM contractor_profiles WHERE user_id = ?");
        $stmt->execute([$contractor_id]);
        $profile = $stmt->fetch();
        
        if ($profile) {
            echo "<p>✅ Contractor profile exists</p>";
            echo "<p>Service areas: " . htmlspecialchars($profile['service_areas']) . "</p>";
            echo "<p>Service types: " . htmlspecialchars($profile['service_types']) . "</p>";
        } else {
            echo "<p style='color: red;'>❌ Contractor profile missing!</p>";
        }
        
        // Debug: Check if there are any quote requests at all
        echo "<h4>Debug: Check quote requests</h4>";
        $stmt = $pdo->query("SELECT COUNT(*) FROM quote_requests");
        $count = $stmt->fetchColumn();
        echo "<p>Total quote requests in database: $count</p>";
        
        if ($count > 0) {
            $stmt = $pdo->query("SELECT * FROM quote_requests ORDER BY created_at DESC LIMIT 3");
            $quotes = $stmt->fetchAll();
            
            echo "<p>Recent quote requests:</p>";
            foreach ($quotes as $quote) {
                echo "<div style='border: 1px solid #ccc; padding: 5px; margin: 5px 0;'>";
                echo "ID: " . $quote['id'] . ", ";
                echo "Service: " . $quote['service_category_id'] . ", ";
                echo "District: " . $quote['district'] . ", ";
                echo "Status: " . $quote['status'] . ", ";
                echo "Specific: " . ($quote['specific_contractor_id'] ?? 'NULL');
                echo "</div>";
            }
        }
        
        // Debug: Check service categories
        echo "<h4>Debug: Check service categories</h4>";
        $stmt = $pdo->query("SELECT * FROM service_categories ORDER BY id");
        $categories = $stmt->fetchAll();
        echo "<p>Service categories:</p>";
        foreach ($categories as $cat) {
            echo "<div>" . $cat['id'] . ": " . htmlspecialchars($cat['name_en']) . "</div>";
        }
        
    } else {
        echo "<p style='color: green;'>✅ SQL query found quotes, now testing PHP filtering</p>";
        
        // Apply the same PHP filtering as in contractor/quotes.php
        $quotes = [];
        foreach ($all_quotes as $quote) {
            // Always show direct quotes for this contractor
            if ($quote['specific_contractor_id'] == $contractor_id) {
                $quotes[] = $quote;
                echo "<p>✅ Added direct quote: " . htmlspecialchars($quote['title']) . "</p>";
                continue;
            }

            // For general quotes (no specific contractor), check service and area match
            if ($quote['specific_contractor_id'] === null) {
                $contractor_services = json_decode($quote['service_types'], true) ?: [];
                $contractor_areas = json_decode($quote['service_areas'], true) ?: [];

                // Check if contractor provides this service
                $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                              in_array((string)$quote['service_category_id'], $contractor_services);
                
                // Check if contractor serves this area
                $has_area = in_array($quote['district'], $contractor_areas);

                echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
                echo "<strong>Quote:</strong> " . htmlspecialchars($quote['title']) . "<br>";
                echo "<strong>Service ID:</strong> " . $quote['service_category_id'] . "<br>";
                echo "<strong>District:</strong> " . $quote['district'] . "<br>";
                echo "<strong>Contractor Services:</strong> " . implode(', ', $contractor_services) . "<br>";
                echo "<strong>Contractor Areas:</strong> " . implode(', ', $contractor_areas) . "<br>";
                echo "<strong>Has Service:</strong> " . ($has_service ? '✅ Yes' : '❌ No') . "<br>";
                echo "<strong>Has Area:</strong> " . ($has_area ? '✅ Yes' : '❌ No') . "<br>";
                
                if ($has_service && $has_area) {
                    $quotes[] = $quote;
                    echo "<strong>Result:</strong> ✅ INCLUDED<br>";
                } else {
                    echo "<strong>Result:</strong> ❌ EXCLUDED<br>";
                }
                echo "</div>";
            }
        }
        
        echo "<p><strong>Final result: " . count($quotes) . " quotes will be shown to contractor</strong></p>";
        
        if (count($quotes) > 0) {
            echo "<p style='color: green;'>✅ Contractor should see quotes!</p>";
            echo "<h4>Quotes that should be visible:</h4>";
            foreach ($quotes as $quote) {
                echo "<div style='background: #e8f5e8; padding: 10px; margin: 5px 0;'>";
                echo "<strong>" . htmlspecialchars($quote['title']) . "</strong><br>";
                echo "Service: " . htmlspecialchars($quote['service_category']) . "<br>";
                echo "District: " . $quote['district'] . "<br>";
                echo "Type: " . $quote['quote_type'] . "<br>";
                echo "Status: " . $quote['status'] . "<br>";
                echo "</div>";
            }
        } else {
            echo "<p style='color: red;'>❌ No quotes match contractor's services/areas</p>";
        }
    }
    
    // Step 4: Test direct access to quotes page
    echo "<h3>Test Direct Access</h3>";
    echo "<p><a href='contractor/quotes.php' target='_blank'>Open Contractor Quotes Page</a></p>";
    echo "<p><a href='contractor/dashboard.php' target='_blank'>Open Contractor Dashboard</a></p>";
    
    echo "<h3>Session Information</h3>";
    echo "<p>User ID: " . $_SESSION['user_id'] . "</p>";
    echo "<p>User Type: " . $_SESSION['user_type'] . "</p>";
    echo "<p>Email: " . $_SESSION['email'] . "</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
