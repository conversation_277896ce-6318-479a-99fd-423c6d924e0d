<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../login.php');
    exit();
}

// Get contractor ID from URL
$contractor_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$contractor_id) {
    $_SESSION['error'] = 'Invalid contractor ID.';
    header('Location: contractors.php');
    exit();
}

// Get customer profile
try {
    $stmt = $pdo->prepare("SELECT cp.*, u.email FROM customer_profiles cp JOIN users u ON cp.user_id = u.id WHERE cp.user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $customer = $stmt->fetch();
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: ../login.php');
    exit();
}

// Get contractor profile
try {
    // Check which review field structure we're using
    $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'status'");
    $has_status = $stmt->fetch();

    if ($has_status) {
        $review_count_condition = "(r.status IS NULL OR r.status != 'deleted')";
    } else {
        $review_count_condition = "(r.is_approved IS NULL OR r.is_approved != 0)";
    }

    $stmt = $pdo->prepare("
        SELECT cp.*, u.email, u.status,
               (SELECT COUNT(*) FROM reviews r WHERE r.contractor_id = u.id AND $review_count_condition) as review_count,
               (SELECT COUNT(*) FROM customer_favorites cf WHERE cf.customer_id = ? AND cf.contractor_id = u.id) as is_favorite
        FROM contractor_profiles cp
        JOIN users u ON cp.user_id = u.id
        WHERE u.id = ? AND u.status = 'approved'
    ");
    $stmt->execute([$_SESSION['user_id'], $contractor_id]);
    $contractor = $stmt->fetch();
    
    if (!$contractor) {
        $_SESSION['error'] = 'Contractor not found.';
        header('Location: contractors.php');
        exit();
    }
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error.';
    header('Location: contractors.php');
    exit();
}

// Get contractor reviews
try {
    $stmt = $pdo->prepare("
        SELECT r.*, cp.first_name, cp.last_name
        FROM reviews r
        JOIN customer_profiles cp ON r.customer_id = cp.user_id
        WHERE r.contractor_id = ? AND $review_count_condition
        ORDER BY r.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$contractor_id]);
    $reviews = $stmt->fetchAll();
} catch (PDOException $e) {
    $reviews = [];
}

// Get service categories
try {
    $stmt = $pdo->prepare("SELECT * FROM service_categories WHERE is_active = 1 ORDER BY name_en");
    $stmt->execute();
    $service_categories = $stmt->fetchAll();
    $categories_map = [];
    foreach ($service_categories as $cat) {
        $categories_map[$cat['id']] = $cat['name_en'];
    }
} catch (PDOException $e) {
    $categories_map = [];
}

// Get contractor portfolio
try {
    $stmt = $pdo->prepare("
        SELECT * FROM contractor_portfolios
        WHERE contractor_id = ?
        ORDER BY is_featured DESC, created_at DESC
        LIMIT 12
    ");
    $stmt->execute([$contractor_id]);
    $portfolio_items = $stmt->fetchAll();
} catch (PDOException $e) {
    $portfolio_items = [];
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title><?php echo htmlspecialchars($contractor['business_name']); ?> - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet"> 

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">
    <link href="../lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
    <link href="../lib/lightbox/css/lightbox.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <style>
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
        }
        
        .contractor-avatar-large {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            font-weight: bold;
            box-shadow: 0 15px 35px rgba(253, 126, 20, 0.3);
            margin: 0 auto 2rem;
            overflow: hidden;
        }

        .profile-image-large {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }
        
        .rating-stars {
            color: #ffc107;
            font-size: 1.2rem;
        }
        
        .info-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }
        
        .info-card:hover {
            transform: translateY(-5px);
        }
        
        .service-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 500;
            margin: 0.25rem;
            display: inline-block;
        }
        
        .review-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }

        .portfolio-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .portfolio-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .portfolio-image {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .portfolio-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .portfolio-card:hover .portfolio-image img {
            transform: scale(1.1);
        }

        .featured-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #ffc107, #ff8c00);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .portfolio-content {
            padding: 1.5rem;
        }

        .portfolio-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            line-height: 1.3;
        }

        .portfolio-location {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-bottom: 0.8rem;
        }

        .portfolio-description {
            color: #6c757d;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .portfolio-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }

        .portfolio-value {
            color: #27ae60;
            font-weight: 600;
        }

        .portfolio-date {
            color: #95a5a6;
            font-size: 0.85rem;
        }
        
        .btn-contact {
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-contact:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(253, 126, 20, 0.3);
            color: white;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #fd7e14, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-label {
            color: #6c757d;
            font-weight: 500;
        }
    </style>
</head>

<body>
    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
        <a href="../index.php" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="../img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="contractors.php" class="nav-item nav-link">Find Contractors</a>
                <a href="quotes.php" class="nav-item nav-link">My Quotes</a>
                <a href="cost_estimator.php" class="nav-item nav-link">Cost Estimator</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
            </div>
            <div class="dropdown">
                <a href="#" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($customer['first_name']); ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>Edit Profile</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Profile Header Start -->
    <div class="profile-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-3 text-center">
                    <div class="contractor-avatar-large">
                        <?php if (!empty($contractor['profile_image'])): ?>
                            <img src="../uploads/profile_pictures/<?php echo htmlspecialchars($contractor['profile_image']); ?>"
                                 alt="<?php echo htmlspecialchars($contractor['business_name']); ?>"
                                 class="profile-image-large">
                        <?php else: ?>
                            <?php echo strtoupper(substr($contractor['business_name'], 0, 1)); ?>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-lg-6">
                    <h1 class="display-5 mb-3"><?php echo htmlspecialchars($contractor['business_name']); ?></h1>
                    <p class="lead mb-2"><?php echo htmlspecialchars($contractor['contact_person']); ?></p>
                    <div class="rating-stars mb-3">
                        <?php 
                        $rating = $contractor['average_rating'];
                        for ($i = 1; $i <= 5; $i++): 
                        ?>
                            <i class="fas fa-star<?php echo $i <= $rating ? '' : '-o'; ?>"></i>
                        <?php endfor; ?>
                        <span class="ms-2"><?php echo number_format($rating, 1); ?> (<?php echo $contractor['review_count']; ?> reviews)</span>
                    </div>
                    <p class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <?php 
                        $areas = json_decode($contractor['service_areas'], true);
                        echo htmlspecialchars(implode(', ', $areas ?? []));
                        ?>
                    </p>
                </div>
                <div class="col-lg-3 text-center">
                    <a href="request_quote.php?contractor=<?php echo $contractor['user_id']; ?>" class="btn btn-contact btn-lg">
                        <i class="fas fa-paper-plane me-2"></i>Request Quote
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Profile Header End -->

    <!-- Main Content Start -->
    <div class="container-fluid py-5">
        <div class="container">
            <div class="row">
                <!-- Left Column -->
                <div class="col-lg-8">
                    <!-- About Section -->
                    <div class="info-card">
                        <h3 class="mb-4"><i class="fas fa-info-circle me-2"></i>About</h3>
                        <?php if (!empty($contractor['business_description'])): ?>
                            <p><?php echo nl2br(htmlspecialchars($contractor['business_description'])); ?></p>
                        <?php else: ?>
                            <p class="text-muted">No description available.</p>
                        <?php endif; ?>
                    </div>

                    <!-- Services Section -->
                    <div class="info-card">
                        <h3 class="mb-4"><i class="fas fa-tools me-2"></i>Services Offered</h3>
                        <?php
                        $services = json_decode($contractor['service_types'], true);
                        if ($services && is_array($services)):
                            foreach ($services as $service_id):
                                if (isset($categories_map[$service_id])):
                        ?>
                            <span class="service-tag"><?php echo htmlspecialchars($categories_map[$service_id]); ?></span>
                        <?php
                                endif;
                            endforeach;
                        else:
                        ?>
                            <p class="text-muted">No services listed.</p>
                        <?php endif; ?>
                    </div>

                    <!-- Portfolio Section -->
                    <div class="info-card">
                        <h3 class="mb-4"><i class="fas fa-images me-2"></i>Portfolio</h3>
                        <?php if (!empty($portfolio_items)): ?>
                            <div class="row g-4">
                                <?php foreach ($portfolio_items as $item): ?>
                                    <div class="col-md-6 col-lg-4">
                                        <div class="portfolio-card">
                                            <?php
                                            $images = json_decode($item['project_images'], true);
                                            $main_image = !empty($images) ? $images[0] : 'assets/images/placeholder-project.jpg';
                                            ?>
                                            <div class="portfolio-image">
                                                <img src="../<?php echo htmlspecialchars($main_image); ?>"
                                                     alt="<?php echo htmlspecialchars($item['project_name']); ?>"
                                                     onerror="this.src='../assets/images/placeholder-project.jpg'">
                                                <?php if ($item['is_featured']): ?>
                                                    <div class="featured-badge">
                                                        <i class="fas fa-star"></i> Featured
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="portfolio-content">
                                                <h5 class="portfolio-title"><?php echo htmlspecialchars($item['project_name']); ?></h5>
                                                <p class="portfolio-location">
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    <?php echo htmlspecialchars($item['project_location']); ?>
                                                </p>
                                                <p class="portfolio-description">
                                                    <?php echo htmlspecialchars(substr($item['project_description'], 0, 100)) . '...'; ?>
                                                </p>
                                                <div class="portfolio-meta">
                                                    <div class="portfolio-value">
                                                        <strong>LKR <?php echo number_format($item['project_value']); ?></strong>
                                                    </div>
                                                    <div class="portfolio-date">
                                                        <?php echo date('M Y', strtotime($item['completion_date'])); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <?php if (count($portfolio_items) >= 12): ?>
                                <div class="text-center mt-4">
                                    <p class="text-muted">Showing recent projects</p>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-images fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No portfolio items available yet.</p>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Reviews Section -->
                    <div class="info-card">
                        <h3 class="mb-4"><i class="fas fa-star me-2"></i>Customer Reviews</h3>
                        <?php if (!empty($reviews)): ?>
                            <?php foreach ($reviews as $review): ?>
                                <div class="review-card">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($review['first_name'] . ' ' . $review['last_name']); ?></h6>
                                            <div class="rating-stars">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star<?php echo $i <= $review['rating'] ? '' : '-o'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        <small class="text-muted"><?php echo date('M j, Y', strtotime($review['created_at'])); ?></small>
                                    </div>
                                    <?php if (!empty($review['review_text'])): ?>
                                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($review['review_text'])); ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted">No reviews yet.</p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="col-lg-4">
                    <!-- Stats Card -->
                    <div class="info-card">
                        <h4 class="mb-4 text-center">Statistics</h4>
                        <div class="row">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $contractor['total_projects']; ?></div>
                                    <div class="stat-label">Projects</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $contractor['review_count']; ?></div>
                                    <div class="stat-label">Reviews</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo number_format($contractor['average_rating'], 1); ?></div>
                                    <div class="stat-label">Rating</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $contractor['cida_grade'] ?? 'N/A'; ?></div>
                                    <div class="stat-label">CIDA Grade</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div class="info-card">
                        <h4 class="mb-4"><i class="fas fa-address-card me-2"></i>Contact Information</h4>
                        <div class="mb-3">
                            <strong>Phone:</strong><br>
                            <a href="tel:<?php echo htmlspecialchars($contractor['phone']); ?>" class="text-decoration-none">
                                <i class="fas fa-phone me-2"></i><?php echo htmlspecialchars($contractor['phone']); ?>
                            </a>
                        </div>
                        <div class="mb-3">
                            <strong>Email:</strong><br>
                            <a href="mailto:<?php echo htmlspecialchars($contractor['email']); ?>" class="text-decoration-none">
                                <i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($contractor['email']); ?>
                            </a>
                        </div>
                        <?php if (!empty($contractor['business_address'])): ?>
                            <div class="mb-3">
                                <strong>Address:</strong><br>
                                <i class="fas fa-map-marker-alt me-2"></i><?php echo nl2br(htmlspecialchars($contractor['business_address'])); ?>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($contractor['website'])): ?>
                            <div class="mb-3">
                                <strong>Website:</strong><br>
                                <a href="<?php echo htmlspecialchars($contractor['website']); ?>" target="_blank" class="text-decoration-none">
                                    <i class="fas fa-globe me-2"></i><?php echo htmlspecialchars($contractor['website']); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Action Buttons -->
                    <div class="info-card text-center">
                        <a href="request_quote.php?contractor=<?php echo $contractor['user_id']; ?>" class="btn btn-contact btn-lg w-100 mb-3">
                            <i class="fas fa-paper-plane me-2"></i>Request Quote
                        </a>
                        <a href="contractors.php" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-arrow-left me-2"></i>Back to Search
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Main Content End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer mt-5 py-5">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-4">Brick & Click</h4>
                    <p class="mb-2"><i class="fa fa-map-marker-alt me-3"></i>123 Street, Colombo, Sri Lanka</p>
                    <p class="mb-2"><i class="fa fa-phone-alt me-3"></i>+94 11 234 5678</p>
                    <p class="mb-2"><i class="fa fa-envelope me-3"></i><EMAIL></p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-4">Quick Links</h4>
                    <a class="btn btn-link" href="../index.php">Home</a>
                    <a class="btn btn-link" href="../about.php">About Us</a>
                    <a class="btn btn-link" href="../services.php">Services</a>
                    <a class="btn btn-link" href="../contact.php">Contact</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-4">Customer Portal</h4>
                    <a class="btn btn-link" href="dashboard.php">Dashboard</a>
                    <a class="btn btn-link" href="contractors.php">Find Contractors</a>
                    <a class="btn btn-link" href="quotes.php">My Quotes</a>
                    <a class="btn btn-link" href="favorites.php">Favorites</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h4 class="text-white mb-4">Follow Us</h4>
                    <div class="d-flex">
                        <a class="btn btn-outline-light btn-social" href=""><i class="fab fa-twitter"></i></a>
                        <a class="btn btn-outline-light btn-social" href=""><i class="fab fa-facebook-f"></i></a>
                        <a class="btn btn-outline-light btn-social" href=""><i class="fab fa-youtube"></i></a>
                        <a class="btn btn-outline-light btn-social" href=""><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Copyright Start -->
    <div class="container-fluid copyright py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                    &copy; <a class="border-bottom" href="#">Brick & Click</a>, All Right Reserved.
                </div>
                <div class="col-md-6 text-center text-md-end">
                    Designed By <a class="border-bottom" href="#">Brick & Click Team</a>
                </div>
            </div>
        </div>
    </div>
    <!-- Copyright End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square rounded-circle back-to-top"><i class="bi bi-arrow-up"></i></a>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>
    <script src="../lib/owlcarousel/owl.carousel.min.js"></script>
    <script src="../lib/lightbox/js/lightbox.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
</body>

</html>
