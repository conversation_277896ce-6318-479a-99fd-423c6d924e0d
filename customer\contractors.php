<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a customer
$is_logged_in = isset($_SESSION['user_id']) && $_SESSION['user_type'] === 'customer';
if (!$is_logged_in) {
    // Allow public access for browsing contractors, but disable favorites functionality
    $_SESSION['user_id'] = null;
}

// Handle favorite actions (only for logged-in users)
if ($is_logged_in && isset($_POST['add_favorite'])) {
    $contractor_id = (int)$_POST['contractor_id'];
    try {
        $stmt = $pdo->prepare("INSERT IGNORE INTO customer_favorites (customer_id, contractor_id) VALUES (?, ?)");
        $stmt->execute([$_SESSION['user_id'], $contractor_id]);
        $_SESSION['success'] = 'Contractor added to favorites successfully!';
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Error adding contractor to favorites.';
    }
    header('Location: contractors.php?' . $_SERVER['QUERY_STRING']);
    exit();
}

if ($is_logged_in && isset($_POST['remove_favorite'])) {
    $contractor_id = (int)$_POST['contractor_id'];
    try {
        $stmt = $pdo->prepare("DELETE FROM customer_favorites WHERE customer_id = ? AND contractor_id = ?");
        $stmt->execute([$_SESSION['user_id'], $contractor_id]);
        $_SESSION['success'] = 'Contractor removed from favorites successfully!';
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Error removing contractor from favorites.';
    }
    header('Location: contractors.php?' . $_SERVER['QUERY_STRING']);
    exit();
}

// Get customer profile (only if logged in)
$customer = null;
if ($is_logged_in) {
    try {
        $stmt = $pdo->prepare("SELECT cp.*, u.email FROM customer_profiles cp JOIN users u ON cp.user_id = u.id WHERE cp.user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $customer = $stmt->fetch();
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error.';
        header('Location: ../login.php');
        exit();
    }
}

// Get unread notifications count (only if logged in)
$unread_count = 0;
if ($is_logged_in) {
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
        $stmt->execute([$_SESSION['user_id']]);
        $unread_count = $stmt->fetchColumn();
    } catch (PDOException $e) {
        // Ignore error, keep count as 0
    }
}

// Get search parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$category = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$location = isset($_GET['location']) ? trim($_GET['location']) : '';
$cida_grade = isset($_GET['cida_grade']) ? trim($_GET['cida_grade']) : '';
$min_rating = isset($_GET['min_rating']) ? (float)$_GET['min_rating'] : 0;
$sort_by = isset($_GET['sort_by']) ? trim($_GET['sort_by']) : 'rating';



// Build search query
$where_conditions = ["u.status = 'approved'"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(cp.business_name LIKE ? OR cp.business_description LIKE ? OR cp.contact_person LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if ($category > 0) {
    $where_conditions[] = "JSON_CONTAINS(cp.service_types, ?)";
    // Search by category ID (contractors now store service IDs as integers)
    $params[] = json_encode($category);
}

if (!empty($location)) {
    $where_conditions[] = "JSON_CONTAINS(cp.service_areas, ?)";
    $params[] = json_encode($location);
}

if (!empty($cida_grade)) {
    $where_conditions[] = "cp.cida_grade = ?";
    $params[] = $cida_grade;
}

if ($min_rating > 0) {
    $where_conditions[] = "cp.average_rating >= ?";
    $params[] = $min_rating;
}

// Build ORDER BY clause
$order_by = "cp.average_rating DESC, cp.total_projects DESC";
switch ($sort_by) {
    case 'name':
        $order_by = "cp.business_name ASC";
        break;
    case 'rating':
        $order_by = "cp.average_rating DESC, cp.total_reviews DESC";
        break;
    case 'projects':
        $order_by = "cp.total_projects DESC";
        break;
    case 'newest':
        $order_by = "cp.created_at DESC";
        break;
}

// Get contractors
try {
    // Check which review field structure we're using
    $stmt = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'status'");
    $has_status = $stmt->fetch();

    if ($has_status) {
        $review_count_condition = "(r.status IS NULL OR r.status != 'deleted')";
    } else {
        $review_count_condition = "(r.is_approved IS NULL OR r.is_approved != 0)";
    }

    $where_clause = implode(' AND ', $where_conditions);
    $sql = "
        SELECT cp.*, u.email, u.status,
               (SELECT COUNT(*) FROM reviews r WHERE r.contractor_id = u.id AND $review_count_condition) as review_count,
               (SELECT COUNT(*) FROM customer_favorites cf WHERE cf.customer_id = ? AND cf.contractor_id = u.id) as is_favorite
        FROM contractor_profiles cp
        JOIN users u ON cp.user_id = u.id
        WHERE $where_clause
        ORDER BY $order_by
    ";

    $stmt = $pdo->prepare($sql);
    // Add customer ID as first parameter for the favorite check (use 0 if not logged in)
    array_unshift($params, $is_logged_in ? $_SESSION['user_id'] : 0);
    $stmt->execute($params);
    $contractors = $stmt->fetchAll();
} catch (PDOException $e) {
    $contractors = [];
}

// Get service categories for filter
try {
    $stmt = $pdo->prepare("SELECT * FROM service_categories WHERE is_active = 1 ORDER BY name_en");
    $stmt->execute();
    $service_categories = $stmt->fetchAll();

    // Create mapping for service IDs to names
    $categories_map = [];
    foreach ($service_categories as $cat) {
        $categories_map[$cat['id']] = $cat['name_en'];
    }
} catch (PDOException $e) {
    $service_categories = [];
    $categories_map = [];
}

// Get districts for location filter
$districts = [
    'Colombo', 'Gampaha', 'Kalutara', 'Kandy', 'Matale', 'Nuwara Eliya',
    'Galle', 'Matara', 'Hambantota', 'Jaffna', 'Kilinochchi', 'Mannar',
    'Vavuniya', 'Mullaitivu', 'Batticaloa', 'Ampara', 'Trincomalee',
    'Kurunegala', 'Puttalam', 'Anuradhapura', 'Polonnaruwa', 'Badulla',
    'Moneragala', 'Ratnapura', 'Kegalle'
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Find Contractors - Brick & Click</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="construction, contractors, Sri Lanka, find contractors" name="keywords">
    <meta content="Find Contractors - Brick & Click" name="description">

    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&family=Teko:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="../lib/animate/animate.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">

    <style>
        .search-header {
            background: linear-gradient(135deg, #212529 0%, #34495e 100%);
            color: white;
            padding: 2rem 0;
        }
        
        .search-form {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .filter-sidebar {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            position: sticky;
            top: 100px;
        }
        
        .contractor-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.4s ease;
            border: 1px solid rgba(197, 23, 46, 0.1);
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            position: relative;
        }

        .contractor-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #C5172E, #FF6B35);
        }

        .contractor-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 50px rgba(197, 23, 46, 0.15);
            border-color: #C5172E;
        }
        
        .contractor-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #C5172E, #FF6B35);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            box-shadow: 0 8px 20px rgba(197, 23, 46, 0.3);
            transition: all 0.3s ease;
            position: relative;
        }

        .contractor-avatar::before {
            content: '';
            position: absolute;
            inset: -3px;
            background: linear-gradient(135deg, #C5172E, #FF6B35, #F7931E);
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .contractor-card:hover .contractor-avatar::before {
            opacity: 1;
        }

        .contractor-card:hover .contractor-avatar {
            transform: scale(1.1);
        }
        
        .rating-stars {
            color: #ffc107;
        }
        
        .cida-badge {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 700;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .service-tag {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #495057;
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            margin: 0.25rem;
            display: inline-block;
            border: 1px solid rgba(197, 23, 46, 0.1);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .service-tag:hover {
            background: linear-gradient(135deg, #C5172E, #FF6B35);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(197, 23, 46, 0.3);
        }
        
        .filter-section {
            border-bottom: 1px solid rgba(197, 23, 46, 0.1);
            padding-bottom: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .filter-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .filter-section h6 {
            color: #C5172E;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .filter-section select {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .filter-section select:focus {
            border-color: #C5172E;
            box-shadow: 0 0 0 0.2rem rgba(197, 23, 46, 0.25);
            outline: none;
        }
        
        .results-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .btn-favorite {
            background: none;
            border: 2px solid #e9ecef;
            color: #6c757d;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .btn-favorite:hover,
        .btn-favorite.active {
            border-color: #dc3545;
            color: #dc3545;
            background: #fff5f5;
        }
        
        .no-results {
            text-align: center;
            padding: 3rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>

<body>
    <!-- Navbar Start -->
    <nav class="navbar navbar-expand-lg bg-white navbar-light sticky-top py-lg-0 px-lg-5">
        <a href="../index.php" class="navbar-brand ms-4 ms-lg-0">
            <h1 class="text-primary m-0"><img class="me-3" src="../img/icons/icon-1.png" alt="Icon">Brick & Click</h1>
        </a>
        <button type="button" class="navbar-toggler me-4" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto p-4 p-lg-0">
                <?php if ($is_logged_in): ?>
                <a href="dashboard.php" class="nav-item nav-link">Dashboard</a>
                <a href="contractors.php" class="nav-item nav-link active">Find Contractors</a>
                <a href="quotes.php" class="nav-item nav-link">My Quotes</a>
                <a href="cost_estimator.php" class="nav-item nav-link">Cost Estimator</a>
                <a href="favorites.php" class="nav-item nav-link">Favorites</a>
                <a href="profile.php" class="nav-item nav-link">Profile</a>
                <?php else: ?>
                <a href="../index.php" class="nav-item nav-link">Home</a>
                <a href="contractors.php" class="nav-item nav-link active">Find Contractors</a>
                <a href="../about.php" class="nav-item nav-link">About</a>
                <a href="../services.php" class="nav-item nav-link">Services</a>
                <a href="../contact.php" class="nav-item nav-link">Contact</a>
                <?php endif; ?>
            </div>
            <?php if ($is_logged_in): ?>
            <div class="dropdown">
                <a href="#" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($customer['first_name']); ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="notifications.php"><i class="fas fa-bell me-2"></i>Notifications <?php if ($unread_count > 0): ?><span class="badge bg-danger"><?php echo $unread_count; ?></span><?php endif; ?></a></li>
                    <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
            <?php else: ?>
            <div class="d-flex">
                <a href="../login.php" class="btn btn-outline-primary me-2">Login</a>
                <a href="../signup.php" class="btn btn-primary">Sign Up</a>
            </div>
            <?php endif; ?>
        </div>
    </nav>
    <!-- Navbar End -->

    <!-- Search Header Start -->
    <div class="search-header">
        <div class="container">


            <div class="search-form">
                <h2 class="text-dark mb-4">Find Verified Contractors</h2>
                <form method="GET" action="">
                    <div class="row g-3">
                        <div class="col-lg-4">
                            <input type="text" class="form-control" name="search" placeholder="Search contractors, services..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-lg-3">
                            <select class="form-control" name="category">
                                <option value="">All Services</option>
                                <option value="1" <?php echo $category == 1 ? 'selected' : ''; ?>>House Construction</option>
                                <option value="2" <?php echo $category == 2 ? 'selected' : ''; ?>>Building Renovation</option>
                                <option value="3" <?php echo $category == 3 ? 'selected' : ''; ?>>Commercial Construction</option>
                                <option value="4" <?php echo $category == 4 ? 'selected' : ''; ?>>Interior Design & Finishing</option>
                                <option value="5" <?php echo $category == 5 ? 'selected' : ''; ?>>Roofing & Waterproofing</option>
                                <option value="6" <?php echo $category == 6 ? 'selected' : ''; ?>>Electrical Work</option>
                                <option value="7" <?php echo $category == 7 ? 'selected' : ''; ?>>Plumbing & Sanitation</option>
                                <option value="8" <?php echo $category == 8 ? 'selected' : ''; ?>>Landscaping & Gardening</option>
                                <option value="9" <?php echo $category == 9 ? 'selected' : ''; ?>>Swimming Pool Construction</option>
                                <option value="10" <?php echo $category == 10 ? 'selected' : ''; ?>>Road & Infrastructure</option>
                            </select>
                        </div>
                        <div class="col-lg-3">
                            <select class="form-control" name="location">
                                <option value="">All Locations</option>
                                <?php foreach ($districts as $district): ?>
                                <option value="<?php echo $district; ?>" <?php echo $location == $district ? 'selected' : ''; ?>>
                                    <?php echo $district; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-lg-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- Search Header End -->

    <!-- Search Results Start -->
    <div class="container-xxl py-5">
        <div class="container">
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Filters Sidebar -->
                <div class="col-lg-3">
                    <div class="filter-sidebar">
                        <h5 class="mb-3">Filters</h5>
                        
                        <form method="GET" action="" id="filterForm">
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                            <input type="hidden" name="category" value="<?php echo $category; ?>">
                            <input type="hidden" name="location" value="<?php echo htmlspecialchars($location); ?>">
                            
                            <div class="filter-section">
                                <h6>CIDA Grade</h6>
                                <select class="form-control" name="cida_grade" onchange="document.getElementById('filterForm').submit();">
                                    <option value="">All Grades</option>
                                    <?php for ($i = 1; $i <= 10; $i++): ?>
                                    <option value="C<?php echo $i; ?>" <?php echo $cida_grade == "C$i" ? 'selected' : ''; ?>>
                                        Grade C<?php echo $i; ?>
                                    </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            
                            <div class="filter-section">
                                <h6>Minimum Rating</h6>
                                <select class="form-control" name="min_rating" onchange="document.getElementById('filterForm').submit();">
                                    <option value="">Any Rating</option>
                                    <option value="4" <?php echo $min_rating == 4 ? 'selected' : ''; ?>>4+ Stars</option>
                                    <option value="3" <?php echo $min_rating == 3 ? 'selected' : ''; ?>>3+ Stars</option>
                                    <option value="2" <?php echo $min_rating == 2 ? 'selected' : ''; ?>>2+ Stars</option>
                                </select>
                            </div>
                            
                            <div class="filter-section">
                                <h6>Sort By</h6>
                                <select class="form-control" name="sort_by" onchange="document.getElementById('filterForm').submit();">
                                    <option value="rating" <?php echo $sort_by == 'rating' ? 'selected' : ''; ?>>Highest Rated</option>
                                    <option value="projects" <?php echo $sort_by == 'projects' ? 'selected' : ''; ?>>Most Projects</option>
                                    <option value="name" <?php echo $sort_by == 'name' ? 'selected' : ''; ?>>Name A-Z</option>
                                    <option value="newest" <?php echo $sort_by == 'newest' ? 'selected' : ''; ?>>Newest</option>
                                </select>
                            </div>
                        </form>
                        
                        <div class="mt-4">
                            <a href="contractors.php" class="btn btn-outline-danger w-100" style="border-radius: 12px; padding: 0.75rem; font-weight: 600; border-width: 2px;">
                                <i class="fas fa-times-circle me-2"></i>Clear All Filters
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Results -->
                <div class="col-lg-9">
                    <div class="results-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1">Search Results</h5>
                                <p class="text-muted mb-0">Found <?php echo count($contractors); ?> contractors</p>
                            </div>
                            <div>
                                <a href="request_quote.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>General Request Quote
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (empty($contractors)): ?>
                    <div class="no-results">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4>No contractors found</h4>
                        <p class="text-muted mb-4">Try adjusting your search criteria or browse all contractors</p>
                        <a href="contractors.php" class="btn btn-primary">View All Contractors</a>
                    </div>
                    <?php else: ?>
                    
                    <?php foreach ($contractors as $contractor): ?>
                    <div class="contractor-card">
                        <div class="row g-0">
                            <div class="col-md-3 p-4 text-center">
                                <div class="contractor-avatar mx-auto mb-3">
                                    <i class="fas fa-hard-hat"></i>
                                </div>
                                <span class="cida-badge">CIDA <?php echo htmlspecialchars($contractor['cida_grade']); ?></span>
                            </div>
                            <div class="col-md-6 p-4">
                                <h5 class="mb-2"><?php echo htmlspecialchars($contractor['business_name']); ?></h5>
                                <p class="text-muted mb-2"><?php echo htmlspecialchars($contractor['contact_person']); ?></p>
                                
                                <div class="rating-stars mb-2">
                                    <?php
                                    $rating = $contractor['average_rating'];
                                    for ($i = 1; $i <= 5; $i++) {
                                        if ($i <= $rating) {
                                            echo '<i class="fas fa-star"></i>';
                                        } elseif ($i - 0.5 <= $rating) {
                                            echo '<i class="fas fa-star-half-alt"></i>';
                                        } else {
                                            echo '<i class="far fa-star"></i>';
                                        }
                                    }
                                    ?>
                                    <span class="ms-2 text-muted"><?php echo number_format($rating, 1); ?> (<?php echo $contractor['review_count']; ?> reviews)</span>
                                </div>
                                
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php 
                                        $areas = json_decode($contractor['service_areas'], true);
                                        echo htmlspecialchars(implode(', ', array_slice($areas, 0, 3)));
                                        if (count($areas) > 3) echo ' +' . (count($areas) - 3) . ' more';
                                        ?>
                                    </small>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted d-block">
                                        <i class="fas fa-tools me-1"></i><?php echo $contractor['total_projects']; ?> Projects Completed
                                    </small>
                                </div>
                                
                                <div class="mb-3">
                                    <?php
                                    $services = json_decode($contractor['service_types'], true);
                                    if ($services && is_array($services)) {
                                        $displayed_count = 0;
                                        foreach (array_slice($services, 0, 4) as $service_id) {
                                            if (isset($categories_map[$service_id])) {
                                                echo '<span class="service-tag">' . htmlspecialchars($categories_map[$service_id]) . '</span>';
                                                $displayed_count++;
                                            }
                                        }
                                        if (count($services) > 4) {
                                            echo '<span class="service-tag">+' . (count($services) - 4) . ' more</span>';
                                        }
                                    }
                                    ?>
                                </div>
                                
                                <?php if (!empty($contractor['business_description'])): ?>
                                <p class="text-muted mb-0"><?php echo htmlspecialchars(substr($contractor['business_description'], 0, 150)); ?>...</p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-3 p-4 text-center">
                                <div class="d-flex flex-column gap-2">
                                    <?php if ($is_logged_in): ?>
                                        <?php if ($contractor['is_favorite'] > 0): ?>
                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="contractor_id" value="<?php echo $contractor['user_id']; ?>">
                                                <button type="submit" name="remove_favorite" class="btn-favorite active" title="Remove from favorites">
                                                    <i class="fas fa-heart"></i>
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="contractor_id" value="<?php echo $contractor['user_id']; ?>">
                                                <button type="submit" name="add_favorite" class="btn-favorite" title="Add to favorites">
                                                    <i class="far fa-heart"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <a href="contractor_profile.php?id=<?php echo $contractor['user_id']; ?>" class="btn btn-outline-primary btn-sm">
                                        View Profile
                                    </a>
                                    <?php if ($is_logged_in): ?>
                                    <a href="request_quote.php?contractor=<?php echo $contractor['user_id']; ?>" class="btn btn-primary btn-sm">
                                        Request Quote
                                    </a>
                                    <?php else: ?>
                                    <a href="../login.php" class="btn btn-primary btn-sm">
                                        Login to Request Quote
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <!-- Search Results End -->

    <!-- Footer Start -->
    <div class="container-fluid bg-dark text-light footer mt-5 pt-5 px-0">
        <div class="container py-5">
            <div class="row g-5">
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Brick & Click</h3>
                    <p class="mb-2 text-light">Sri Lanka's leading construction contractor marketplace</p>
                    <p class="mb-2 text-light"><i class="fa fa-phone-alt text-primary me-3"></i>+94 77 123 4567</p>
                    <p class="mb-2 text-light"><i class="fa fa-envelope text-primary me-3"></i><EMAIL></p>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Quick Links</h3>
                    <a class="btn btn-link text-light" href="contractors.php">Find Contractors</a>
                    <a class="btn btn-link text-light" href="quotes.php">My Quotes</a>
                    <a class="btn btn-link text-light" href="favorites.php">Favorites</a>
                    <a class="btn btn-link text-light" href="profile.php">Profile</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Services</h3>
                    <a class="btn btn-link text-light" href="#">House Construction</a>
                    <a class="btn btn-link text-light" href="#">Renovation</a>
                    <a class="btn btn-link text-light" href="#">Roofing</a>
                    <a class="btn btn-link text-light" href="#">Electrical</a>
                </div>
                <div class="col-lg-3 col-md-6">
                    <h3 class="text-light mb-4">Support</h3>
                    <a class="btn btn-link text-light" href="#">Help Center</a>
                    <a class="btn btn-link text-light" href="#">Contact Support</a>
                    <a class="btn btn-link text-light" href="#">Terms of Service</a>
                    <a class="btn btn-link text-light" href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
        <div class="container-fluid copyright">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                        <span class="text-light">&copy; <a href="#" class="text-primary">Brick & Click</a>, All Right Reserved.</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer End -->

    <!-- Back to Top -->
    <a href="#" class="btn btn-lg btn-primary btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>



    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../lib/wow/wow.min.js"></script>
    <script src="../lib/easing/easing.min.js"></script>
    <script src="../lib/waypoints/waypoints.min.js"></script>
    <script src="../lib/counterup/counterup.min.js"></script>

    <!-- Template Javascript -->
    <script src="../js/main.js"></script>
</body>
</html>
