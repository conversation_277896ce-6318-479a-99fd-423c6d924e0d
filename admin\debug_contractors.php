<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

echo "<h1>Contractor Database Debug</h1>";

// Check if there are any users with contractor type
try {
    echo "<h2>1. Check Users Table for Contractors</h2>";
    $stmt = $pdo->query("SELECT id, email, user_type, status, created_at FROM users WHERE user_type = 'contractor'");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<p style='color: red;'>❌ No contractors found in users table</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($users) . " contractors in users table</p>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Email</th><th>User Type</th><th>Status</th><th>Created At</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['user_type']) . "</td>";
            echo "<td>" . htmlspecialchars($user['status']) . "</td>";
            echo "<td>" . htmlspecialchars($user['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error checking users table: " . $e->getMessage() . "</p>";
}

// Check contractor_profiles table
try {
    echo "<h2>2. Check Contractor Profiles Table</h2>";
    $stmt = $pdo->query("SELECT user_id, business_name, contact_person, phone FROM contractor_profiles");
    $profiles = $stmt->fetchAll();
    
    if (empty($profiles)) {
        echo "<p style='color: red;'>❌ No contractor profiles found</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($profiles) . " contractor profiles</p>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>User ID</th><th>Business Name</th><th>Contact Person</th><th>Phone</th></tr>";
        foreach ($profiles as $profile) {
            echo "<tr>";
            echo "<td>" . $profile['user_id'] . "</td>";
            echo "<td>" . htmlspecialchars($profile['business_name'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($profile['contact_person'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($profile['phone'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error checking contractor_profiles table: " . $e->getMessage() . "</p>";
}

// Test the exact query from contractors.php
try {
    echo "<h2>3. Test Contractors.php Query</h2>";
    
    // Get filter parameters (same as contractors.php)
    $status_filter = isset($_GET['status']) ? $_GET['status'] : 'all';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    // Build query (same as contractors.php)
    $where_conditions = ["u.user_type = 'contractor'"];
    $params = [];
    
    if ($status_filter !== 'all') {
        $where_conditions[] = "u.status = ?";
        $params[] = $status_filter;
    }
    
    if (!empty($search)) {
        $where_conditions[] = "(cp.business_name LIKE ? OR cp.contact_person LIKE ? OR u.email LIKE ?)";
        $search_term = "%$search%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    echo "<p><strong>Where Clause:</strong> $where_clause</p>";
    echo "<p><strong>Parameters:</strong> " . print_r($params, true) . "</p>";
    
    $stmt = $pdo->prepare("
        SELECT u.id as user_id, u.email, u.status, u.created_at, u.user_type,
               cp.id as profile_id, cp.business_name, cp.contact_person, cp.phone, cp.business_address,
               cp.website, cp.cida_registration, cp.cida_grade, cp.business_description,
               cp.service_areas, cp.service_types,
               (SELECT COUNT(*) FROM reviews r WHERE r.contractor_id = u.id AND r.is_approved = 1) as review_count,
               (SELECT AVG(rating) FROM reviews r WHERE r.contractor_id = u.id AND r.is_approved = 1) as average_rating,
               (SELECT COUNT(*) FROM quote_responses qr WHERE qr.contractor_id = u.id) as quote_count
        FROM users u
        LEFT JOIN contractor_profiles cp ON u.id = cp.user_id
        WHERE $where_clause
        ORDER BY u.created_at DESC
    ");
    $stmt->execute($params);
    $contractors = $stmt->fetchAll();
    
    echo "<p><strong>Query Result:</strong> " . count($contractors) . " contractors found</p>";
    
    if (!empty($contractors)) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>User ID</th><th>Email</th><th>Business Name</th><th>Status</th><th>Review Count</th><th>Quote Count</th></tr>";
        foreach ($contractors as $contractor) {
            echo "<tr>";
            echo "<td>" . $contractor['user_id'] . "</td>";
            echo "<td>" . htmlspecialchars($contractor['email']) . "</td>";
            echo "<td>" . htmlspecialchars($contractor['business_name'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($contractor['status']) . "</td>";
            echo "<td>" . $contractor['review_count'] . "</td>";
            echo "<td>" . $contractor['quote_count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error with contractors.php query: " . $e->getMessage() . "</p>";
}

// Test status counts query
try {
    echo "<h2>4. Test Status Counts Query</h2>";
    $stmt = $pdo->query("
        SELECT 
            status,
            COUNT(*) as count
        FROM users 
        WHERE user_type = 'contractor'
        GROUP BY status
    ");
    $status_counts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    echo "<p><strong>Status Counts:</strong></p>";
    echo "<pre>" . print_r($status_counts, true) . "</pre>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error with status counts query: " . $e->getMessage() . "</p>";
}

echo "<br><a href='contractors.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Back to Contractors Page</a>";
?>
