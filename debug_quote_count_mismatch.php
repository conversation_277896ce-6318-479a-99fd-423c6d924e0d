<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔍 Debug Quote Count Mismatch</h2>";

try {
    // Get the contractor from the session or use a test contractor
    $contractor_id = $_SESSION['user_id'] ?? null;
    
    if (!$contractor_id) {
        // Get a test contractor
        $stmt = $pdo->query("
            SELECT u.id, cp.business_name 
            FROM users u 
            JOIN contractor_profiles cp ON u.id = cp.user_id 
            WHERE u.user_type = 'contractor' AND u.status = 'approved' 
            LIMIT 1
        ");
        $contractor = $stmt->fetch();
        $contractor_id = $contractor['id'];
        echo "<p><strong>Using test contractor:</strong> " . htmlspecialchars($contractor['business_name']) . " (ID: $contractor_id)</p>";
    } else {
        $stmt = $pdo->prepare("SELECT business_name FROM contractor_profiles WHERE user_id = ?");
        $stmt->execute([$contractor_id]);
        $business_name = $stmt->fetchColumn();
        echo "<p><strong>Current contractor:</strong> " . htmlspecialchars($business_name) . " (ID: $contractor_id)</p>";
    }
    
    echo "<h3>Step 1: Dashboard Query (Exact Copy)</h3>";
    
    // Dashboard query - EXACT COPY
    $stmt = $pdo->prepare("
        SELECT qr.*, cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE qr.status = 'open'
        AND (qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)
        AND qr.id NOT IN (
            SELECT COALESCE(quote_request_id, 0)
            FROM quote_responses
            WHERE contractor_id = ?
        )
    ");
    $stmt->execute([$contractor_id, $contractor_id, $contractor_id]);
    $dashboard_quotes = $stmt->fetchAll();
    
    echo "<p><strong>Dashboard SQL returned:</strong> " . count($dashboard_quotes) . " quotes</p>";
    
    // Apply dashboard filtering
    $dashboard_filtered = 0;
    foreach ($dashboard_quotes as $quote) {
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $dashboard_filtered++;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $dashboard_filtered++;
            }
        }
    }
    
    echo "<p><strong>Dashboard filtered count:</strong> $dashboard_filtered</p>";
    
    echo "<h3>Step 2: Quotes Page Query (Pending Filter)</h3>";
    
    // Quotes page query for pending - EXACT COPY
    $where_conditions = ["(qr.specific_contractor_id = ? OR qr.specific_contractor_id IS NULL)"];
    $params = [$contractor_id];
    $where_conditions[] = "qr.status = 'open' AND qr.id NOT IN (SELECT COALESCE(quote_request_id, 0) FROM quote_responses WHERE contractor_id = ?)";
    $params[] = $contractor_id;
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $pdo->prepare("
        SELECT qr.*, cp.first_name, cp.last_name, cp.district, cp.phone, sc.name_en as service_category,
               (SELECT COUNT(*) FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ?) as has_responded,
               (SELECT quoted_amount FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_amount,
               (SELECT status FROM quote_responses WHERE quote_request_id = qr.id AND contractor_id = ? ORDER BY created_at DESC LIMIT 1) as my_quote_status,
               CASE WHEN qr.specific_contractor_id = ? THEN 'specific' ELSE 'general' END as quote_type,
               cp_contractor.service_types, cp_contractor.service_areas
        FROM quote_requests qr
        JOIN customer_profiles cp ON qr.customer_id = cp.user_id
        LEFT JOIN service_categories sc ON qr.service_category_id = sc.id
        JOIN contractor_profiles cp_contractor ON cp_contractor.user_id = ?
        WHERE $where_clause
        ORDER BY qr.created_at DESC
    ");
    
    $query_params = array_merge([$contractor_id, $contractor_id, $contractor_id, $contractor_id, $contractor_id], $params);
    $stmt->execute($query_params);
    $quotes_page_quotes = $stmt->fetchAll();
    
    echo "<p><strong>Quotes page SQL returned:</strong> " . count($quotes_page_quotes) . " quotes</p>";
    
    // Apply quotes page filtering
    $quotes_page_filtered = 0;
    foreach ($quotes_page_quotes as $quote) {
        if ($quote['specific_contractor_id'] == $contractor_id) {
            $quotes_page_filtered++;
        } elseif ($quote['specific_contractor_id'] === null) {
            $contractor_services = json_decode($quote['service_types'], true) ?: [];
            $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
            
            $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                          in_array((string)$quote['service_category_id'], $contractor_services);
            $has_area = in_array($quote['district'], $contractor_areas);
            
            if ($has_service && $has_area) {
                $quotes_page_filtered++;
            }
        }
    }
    
    echo "<p><strong>Quotes page filtered count:</strong> $quotes_page_filtered</p>";
    
    echo "<h3>Step 3: Comparison</h3>";
    
    if ($dashboard_filtered == $quotes_page_filtered) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 1rem; border-radius: 5px;'>";
        echo "<h4 style='color: #155724; margin: 0;'>✅ Counts Match!</h4>";
        echo "<p style='color: #155724; margin: 0.5rem 0 0 0;'>Dashboard and quotes page should show the same count.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 1rem; border-radius: 5px;'>";
        echo "<h4 style='color: #721c24; margin: 0;'>❌ Counts Don't Match!</h4>";
        echo "<p style='color: #721c24; margin: 0.5rem 0 0 0;'>Dashboard: $dashboard_filtered, Quotes Page: $quotes_page_filtered</p>";
        echo "</div>";
        
        echo "<h3>Step 4: Detailed Analysis</h3>";
        
        // Check if the SQL queries return different results
        if (count($dashboard_quotes) != count($quotes_page_quotes)) {
            echo "<p style='color: red;'><strong>Issue:</strong> SQL queries return different counts!</p>";
            echo "<p>Dashboard SQL: " . count($dashboard_quotes) . " quotes</p>";
            echo "<p>Quotes page SQL: " . count($quotes_page_quotes) . " quotes</p>";
            
            // Find the difference
            $dashboard_ids = array_column($dashboard_quotes, 'id');
            $quotes_page_ids = array_column($quotes_page_quotes, 'id');
            
            $missing_in_quotes_page = array_diff($dashboard_ids, $quotes_page_ids);
            $extra_in_quotes_page = array_diff($quotes_page_ids, $dashboard_ids);
            
            if (!empty($missing_in_quotes_page)) {
                echo "<p style='color: red;'><strong>Missing in quotes page:</strong> Quote IDs " . implode(', ', $missing_in_quotes_page) . "</p>";
            }
            
            if (!empty($extra_in_quotes_page)) {
                echo "<p style='color: orange;'><strong>Extra in quotes page:</strong> Quote IDs " . implode(', ', $extra_in_quotes_page) . "</p>";
            }
        } else {
            echo "<p style='color: green;'>SQL queries return same count - issue is in PHP filtering</p>";
            
            // Compare filtering results
            echo "<h4>Filtering Comparison</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Quote ID</th><th>Title</th><th>District</th><th>Service ID</th><th>Dashboard Match</th><th>Quotes Page Match</th></tr>";
            
            foreach ($dashboard_quotes as $quote) {
                $dashboard_match = false;
                $quotes_page_match = false;
                
                // Dashboard filtering
                if ($quote['specific_contractor_id'] == $contractor_id) {
                    $dashboard_match = true;
                } elseif ($quote['specific_contractor_id'] === null) {
                    $contractor_services = json_decode($quote['service_types'], true) ?: [];
                    $contractor_areas = json_decode($quote['service_areas'], true) ?: [];
                    
                    $has_service = in_array((int)$quote['service_category_id'], $contractor_services) ||
                                  in_array((string)$quote['service_category_id'], $contractor_services);
                    $has_area = in_array($quote['district'], $contractor_areas);
                    
                    if ($has_service && $has_area) {
                        $dashboard_match = true;
                    }
                }
                
                // Find same quote in quotes page results
                foreach ($quotes_page_quotes as $qp_quote) {
                    if ($qp_quote['id'] == $quote['id']) {
                        if ($qp_quote['specific_contractor_id'] == $contractor_id) {
                            $quotes_page_match = true;
                        } elseif ($qp_quote['specific_contractor_id'] === null) {
                            $contractor_services = json_decode($qp_quote['service_types'], true) ?: [];
                            $contractor_areas = json_decode($qp_quote['service_areas'], true) ?: [];
                            
                            $has_service = in_array((int)$qp_quote['service_category_id'], $contractor_services) ||
                                          in_array((string)$qp_quote['service_category_id'], $contractor_services);
                            $has_area = in_array($qp_quote['district'], $contractor_areas);
                            
                            if ($has_service && $has_area) {
                                $quotes_page_match = true;
                            }
                        }
                        break;
                    }
                }
                
                $row_color = ($dashboard_match != $quotes_page_match) ? 'background: #ffebee;' : '';
                echo "<tr style='$row_color'>";
                echo "<td>" . $quote['id'] . "</td>";
                echo "<td>" . htmlspecialchars($quote['title'] ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($quote['district']) . "</td>";
                echo "<td>" . $quote['service_category_id'] . "</td>";
                echo "<td>" . ($dashboard_match ? '✅' : '❌') . "</td>";
                echo "<td>" . ($quotes_page_match ? '✅' : '❌') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    echo "<h3>Step 5: Test URLs</h3>";
    echo "<p><strong>Dashboard:</strong> <a href='contractor/dashboard.php' target='_blank'>contractor/dashboard.php</a></p>";
    echo "<p><strong>Quotes page (pending):</strong> <a href='contractor/quotes.php?status=pending&debug=1' target='_blank'>contractor/quotes.php?status=pending&debug=1</a></p>";
    echo "<p><strong>Quotes page (all):</strong> <a href='contractor/quotes.php?status=all&debug=1' target='_blank'>contractor/quotes.php?status=all&debug=1</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Error: " . $e->getMessage() . "</p>";
}
?>
