<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

// Initialize admin info with defaults
$admin_info = [
    'username' => 'admin',
    'email' => '<EMAIL>'
];

// Try to get current admin info from database
try {
    if (isset($pdo)) {
        $stmt = $pdo->prepare("SELECT username, email FROM users WHERE id = ? AND user_type = 'admin'");
        $stmt->execute([$_SESSION['user_id']]);
        $result = $stmt->fetch();
        if ($result) {
            $admin_info = $result;
        }
    }
} catch (PDOException $e) {
    // Use default values if database query fails
    // Don't show error for this as it's not critical for page functionality
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['change_password'])) {
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];

        // Validate inputs
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error = 'All password fields are required.';
        } elseif ($new_password !== $confirm_password) {
            $error = 'New passwords do not match.';
        } elseif (strlen($new_password) < 6) {
            $error = 'New password must be at least 6 characters long.';
        } else {
            try {
                if (isset($pdo)) {
                    // Verify current password
                    $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
                    $stmt->execute([$_SESSION['user_id']]);
                    $user = $stmt->fetch();

                    if ($user && password_verify($current_password, $user['password'])) {
                        // Update password
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                        $stmt->execute([$hashed_password, $_SESSION['user_id']]);
                        $message = 'Password changed successfully!';
                    } else {
                        $error = 'Current password is incorrect.';
                    }
                } else {
                    $error = 'Database connection not available.';
                }
            } catch (PDOException $e) {
                $error = 'Error updating password: ' . $e->getMessage();
            }
        }
    } elseif (isset($_POST['update_profile'])) {
        $new_email = trim($_POST['email']);

        if (empty($new_email)) {
            $error = 'Email is required.';
        } elseif (!filter_var($new_email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address.';
        } else {
            try {
                if (isset($pdo)) {
                    $stmt = $pdo->prepare("UPDATE users SET email = ? WHERE id = ?");
                    $stmt->execute([$new_email, $_SESSION['user_id']]);
                    $admin_info['email'] = $new_email;
                    $message = 'Profile updated successfully!';
                } else {
                    $error = 'Database connection not available.';
                }
            } catch (PDOException $e) {
                $error = 'Error updating profile: ' . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Settings - Admin Panel</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF8C00;
            --accent-yellow: #FFD700;
            --light-gray: #F8F9FA;
            --medium-gray: #6C757D;
            --success-green: #28A745;
            --warning-orange: #FFC107;
            --info-blue: #17A2B8;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f5f6fa;
            color: var(--primary-dark);
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-header h3 {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }
        
        .sidebar-header p {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 15px rgba(197, 23, 46, 0.3);
        }

        .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-dark), #0a2a3a);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(6, 32, 43, 0.3);
        }
        
        .page-title {
            font-weight: 700;
            margin: 0;
            font-size: 2.5rem;
        }
        
        .page-subtitle {
            margin: 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .settings-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .settings-section {
            margin-bottom: 3rem;
        }
        
        .settings-section:last-child {
            margin-bottom: 0;
        }
        
        .section-title {
            color: var(--primary-dark);
            font-weight: 600;
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--light-gray);
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-red);
            box-shadow: 0 0 0 0.2rem rgba(197, 23, 46, 0.25);
        }
        
        .btn-save {
            background: linear-gradient(135deg, var(--primary-red), #e41e3f);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(197, 23, 46, 0.4);
            color: white;
        }
        
        .info-box {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 1px solid #90caf9;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            color: #0d47a1;
        }
        
        .info-box i {
            color: var(--info-blue);
            margin-right: 0.5rem;
        }

        .btn-outline-primary,
        .btn-outline-warning,
        .btn-outline-info,
        .btn-outline-danger {
            border-width: 2px;
            font-weight: 600;
            padding: 0.75rem 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover,
        .btn-outline-warning:hover,
        .btn-outline-info:hover,
        .btn-outline-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .form-control[readonly] {
            background-color: #f8f9fa;
            border-color: #e9ecef;
        }

        .text-muted {
            font-size: 0.875rem;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-shield-alt me-2"></i>Admin Panel</h3>
            <p>Brick & Click Management</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="contractors.php" class="nav-link">
                    <i class="fas fa-hard-hat"></i>Contractors
                </a>
            </div>
            <div class="nav-item">
                <a href="customers.php" class="nav-link">
                    <i class="fas fa-users"></i>Customers
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews
                </a>
            </div>
            <div class="nav-item">
                <a href="payments.php" class="nav-link">
                    <i class="fas fa-credit-card"></i>Payments
                </a>
            </div>
            <div class="nav-item">
                <a href="reports.php" class="nav-link">
                    <i class="fas fa-chart-bar"></i>Reports
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link active">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Settings</h1>
            <p class="page-subtitle">Configure platform settings and preferences</p>
        </div>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                <hr>
                <small>If you're experiencing database issues, you can <a href="check_db.php" class="alert-link">run the database check tool</a> to diagnose and fix common problems.</small>
            </div>
        <?php endif; ?>
        
        <!-- Profile Settings -->
        <div class="settings-card">
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-user-cog me-2"></i>Admin Profile
                </h3>

                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" value="<?php echo htmlspecialchars($admin_info['username'] ?? ''); ?>" readonly>
                            <small class="text-muted">Username cannot be changed</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($admin_info['email'] ?? ''); ?>" required>
                        </div>
                    </div>

                    <div class="text-end">
                        <button type="submit" name="update_profile" class="btn btn-save">
                            <i class="fas fa-save me-2"></i>Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Password Change -->
        <div class="settings-card">
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-lock me-2"></i>Change Password
                </h3>

                <form method="POST">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="new_password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                            <small class="text-muted">Minimum 6 characters</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>

                    <div class="text-end">
                        <button type="submit" name="change_password" class="btn btn-save">
                            <i class="fas fa-key me-2"></i>Change Password
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- System Information -->
        <div class="settings-card">
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-info-circle me-2"></i>System Information
                </h3>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Platform Version</label>
                        <input type="text" class="form-control" value="Brick & Click v1.0" readonly>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Last Login</label>
                        <input type="text" class="form-control" value="<?php echo date('M j, Y \a\t g:i A'); ?>" readonly>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Admin ID</label>
                        <input type="text" class="form-control" value="<?php echo $_SESSION['user_id']; ?>" readonly>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Session Status</label>
                        <input type="text" class="form-control" value="Active" readonly style="color: #28a745; font-weight: 600;">
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="settings-card">
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h3>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="contractors.php?status=pending" class="btn btn-outline-primary w-100">
                            <i class="fas fa-clock me-2"></i>Pending Approvals
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="reviews.php?status=pending" class="btn btn-outline-warning w-100">
                            <i class="fas fa-star me-2"></i>Review Moderation
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="reports.php" class="btn btn-outline-info w-100">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="../logout.php" class="btn btn-outline-danger w-100" onclick="return confirm('Are you sure you want to logout?')">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
