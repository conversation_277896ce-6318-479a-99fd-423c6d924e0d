<?php
// Include authentication and authorization check
require_once 'includes/auth_check.php';

// Contractor data is now available in $contractor variable

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $project_name = trim($_POST['project_name']);
    $project_description = trim($_POST['project_description']);
    $project_location = trim($_POST['project_location']);
    $completion_date = $_POST['completion_date'];
    $project_value = (float)$_POST['project_value'];
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
    
    $errors = [];
    
    // Validation
    if (empty($project_name)) $errors[] = 'Project name is required.';
    if (empty($project_description)) $errors[] = 'Project description is required.';
    if (empty($project_location)) $errors[] = 'Project location is required.';
    if (empty($completion_date)) $errors[] = 'Completion date is required.';
    if ($project_value <= 0) $errors[] = 'Project value must be greater than 0.';
    
    // Date validation
    if (!empty($completion_date) && strtotime($completion_date) > time()) {
        $errors[] = 'Completion date cannot be in the future.';
    }
    
    // Handle file uploads
    $uploaded_images = [];
    if (!empty($_FILES['project_images']['name'][0])) {
        $upload_dir = '../uploads/portfolio/';
        
        // Create upload directory if it doesn't exist
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        $max_file_size = 5 * 1024 * 1024; // 5MB
        
        foreach ($_FILES['project_images']['tmp_name'] as $key => $tmp_name) {
            if (!empty($tmp_name)) {
                $file_name = $_FILES['project_images']['name'][$key];
                $file_size = $_FILES['project_images']['size'][$key];
                $file_type = $_FILES['project_images']['type'][$key];
                $file_error = $_FILES['project_images']['error'][$key];
                
                // Validate file
                if ($file_error !== UPLOAD_ERR_OK) {
                    $errors[] = "Error uploading file: $file_name";
                    continue;
                }
                
                if (!in_array($file_type, $allowed_types)) {
                    $errors[] = "Invalid file type for: $file_name. Only JPEG, PNG, and GIF are allowed.";
                    continue;
                }
                
                if ($file_size > $max_file_size) {
                    $errors[] = "File too large: $file_name. Maximum size is 5MB.";
                    continue;
                }
                
                // Generate unique filename
                $file_extension = pathinfo($file_name, PATHINFO_EXTENSION);
                $unique_filename = uniqid() . '_' . time() . '.' . $file_extension;
                $upload_path = $upload_dir . $unique_filename;
                
                if (move_uploaded_file($tmp_name, $upload_path)) {
                    $uploaded_images[] = $unique_filename;
                } else {
                    $errors[] = "Failed to upload file: $file_name";
                }
            }
        }
    }
    
    if (empty($errors)) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO contractor_portfolios (
                    contractor_id, project_name, project_description, project_location,
                    completion_date, project_value, project_images, is_featured,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            
            $stmt->execute([
                $_SESSION['user_id'],
                $project_name,
                $project_description,
                $project_location,
                $completion_date,
                $project_value,
                json_encode($uploaded_images),
                $is_featured
            ]);
            
            $_SESSION['success'] = 'Portfolio project added successfully!';
            header('Location: portfolio.php');
            exit();
            
        } catch (PDOException $e) {
            $errors[] = 'Database error. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Add Portfolio Project - BrickClick Contractor</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
    <!-- Favicon -->
    <link href="../img/favicon.ico" rel="icon">
    
    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet"> 
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    
    <!-- Customized Bootstrap Stylesheet -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Template Stylesheet -->
    <link href="../css/style.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #C5172E;
            --primary-dark: #06202B;
            --accent-orange: #FF6B35;
            --accent-yellow: #F7931E;
            --success-green: #28a745;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --dark-gray: #343a40;
        }
        
        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e9ecef 100%);
            font-family: 'Open Sans', sans-serif;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, var(--primary-dark) 0%, #0a2a3a 100%);
            z-index: 1000;
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            flex-shrink: 0;
        }

        .sidebar-header h3 {
            color: var(--accent-orange);
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
        }

        .sidebar-header p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }

        .sidebar-nav {
            flex: 1;
            padding: 0.5rem 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .nav-item {
            margin: 0.15rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,107,53,0.1);
            color: var(--accent-orange);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--accent-orange), var(--primary-red));
            color: white;
            box-shadow: 0 3px 10px rgba(255,107,53,0.3);
        }

        .nav-link i {
            margin-right: 0.8rem;
            width: 18px;
            text-align: center;
            font-size: 1rem;
        }
        
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--accent-orange), #e55a2b);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .page-title {
            color: white;
            font-weight: 700;
            margin: 0;
            font-size: 2rem;
        }
        
        .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }
        
        .form-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .form-section {
            margin-bottom: 2rem;
        }
        
        .section-title {
            color: var(--primary-dark);
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--accent-orange);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            color: var(--primary-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--accent-orange);
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group-text {
            background: var(--accent-orange);
            color: white;
            border: 2px solid var(--accent-orange);
            border-radius: 10px 0 0 10px;
            font-weight: 600;
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        
        .file-upload-area {
            border: 2px dashed #e9ecef;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-upload-area:hover {
            border-color: var(--accent-orange);
            background: rgba(255, 107, 53, 0.05);
        }
        
        .file-upload-area.dragover {
            border-color: var(--accent-orange);
            background: rgba(255, 107, 53, 0.1);
        }
        
        .upload-icon {
            font-size: 3rem;
            color: var(--medium-gray);
            margin-bottom: 1rem;
        }
        
        .upload-text {
            color: var(--dark-gray);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .upload-hint {
            color: var(--medium-gray);
            font-size: 0.9rem;
        }
        
        .form-check {
            background: var(--light-gray);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-check-input:checked {
            background-color: var(--accent-orange);
            border-color: var(--accent-orange);
        }
        
        .form-check-label {
            color: var(--primary-dark);
            font-weight: 600;
        }
        
        .btn-submit {
            background: linear-gradient(135deg, var(--success-green), #1e7e34);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .btn-cancel {
            background: linear-gradient(135deg, var(--medium-gray), #5a6268);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
        
        .required {
            color: var(--primary-red);
        }
        
        .form-text {
            color: var(--medium-gray);
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }
        
        .preview-container {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }
        
        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #e9ecef;
        }
        
        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .preview-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background: var(--primary-red);
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            font-size: 0.8rem;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>BrickClick</h3>
            <p>Contractor Portal</p>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="quotes.php" class="nav-link">
                    <i class="fas fa-file-invoice-dollar"></i>Quote Requests
                </a>
            </div>
            <div class="nav-item">
                <a href="portfolio.php" class="nav-link active">
                    <i class="fas fa-images"></i>Portfolio
                </a>
            </div>
            <div class="nav-item">
                <a href="reviews.php" class="nav-link">
                    <i class="fas fa-star"></i>Reviews & Ratings
                </a>
            </div>
            <div class="nav-item">
                <a href="profile.php" class="nav-link">
                    <i class="fas fa-user-edit"></i>Profile & Services
                </a>
            </div>
            <div class="nav-item">
                <a href="notifications.php" class="nav-link">
                    <i class="fas fa-bell"></i>Notifications
                </a>
            </div>
            <div class="nav-item">
                <a href="settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Add Portfolio Project</h1>
            <p class="page-subtitle">Showcase your completed work to attract more customers</p>
        </div>

        <!-- Form Card -->
        <div class="form-card">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="" enctype="multipart/form-data">
                <!-- Project Information -->
                <div class="form-section">
                    <h3 class="section-title">Project Information</h3>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label class="form-label">Project Name <span class="required">*</span></label>
                                <input type="text" class="form-control" name="project_name" 
                                       value="<?php echo htmlspecialchars($_POST['project_name'] ?? ''); ?>" 
                                       placeholder="e.g., Modern Villa Construction" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Completion Date <span class="required">*</span></label>
                                <input type="date" class="form-control" name="completion_date" 
                                       value="<?php echo htmlspecialchars($_POST['completion_date'] ?? ''); ?>" 
                                       max="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label class="form-label">Project Location <span class="required">*</span></label>
                                <input type="text" class="form-control" name="project_location" 
                                       value="<?php echo htmlspecialchars($_POST['project_location'] ?? ''); ?>" 
                                       placeholder="e.g., Colombo, Sri Lanka" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Project Value <span class="required">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">Rs.</span>
                                    <input type="number" class="form-control" name="project_value" 
                                           value="<?php echo htmlspecialchars($_POST['project_value'] ?? ''); ?>" 
                                           min="1" step="0.01" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Project Description <span class="required">*</span></label>
                        <textarea class="form-control" name="project_description" rows="5" required 
                                  placeholder="Describe the project scope, challenges overcome, materials used, and key achievements..."><?php echo htmlspecialchars($_POST['project_description'] ?? ''); ?></textarea>
                        <div class="form-text">Provide a detailed description to help customers understand your capabilities</div>
                    </div>
                </div>
                
                <!-- Project Images -->
                <div class="form-section">
                    <h3 class="section-title">Project Images</h3>
                    
                    <div class="form-group">
                        <label class="form-label">Upload Images</label>
                        <div class="file-upload-area" onclick="document.getElementById('project_images').click()">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-text">Click to upload images or drag and drop</div>
                            <div class="upload-hint">Maximum 5MB per image. JPEG, PNG, GIF supported.</div>
                        </div>
                        <input type="file" id="project_images" name="project_images[]" multiple 
                               accept="image/*" style="display: none;" onchange="previewImages(this)">
                        <div id="image-preview" class="preview-container"></div>
                        <div class="form-text">Upload multiple images to showcase different aspects of your project</div>
                    </div>
                </div>
                
                <!-- Project Settings -->
                <div class="form-section">
                    <h3 class="section-title">Project Settings</h3>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_featured" id="is_featured" 
                               <?php echo isset($_POST['is_featured']) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_featured">
                            <i class="fas fa-star me-2"></i>Mark as Featured Project
                        </label>
                        <div class="form-text">Featured projects are highlighted in your portfolio and shown to customers first</div>
                    </div>
                </div>
                
                <div class="d-flex gap-3 mt-4">
                    <button type="submit" class="btn btn-submit">
                        <i class="fas fa-plus me-2"></i>Add Project to Portfolio
                    </button>
                    <a href="portfolio.php" class="btn btn-cancel">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function previewImages(input) {
            const previewContainer = document.getElementById('image-preview');
            previewContainer.innerHTML = '';
            
            if (input.files) {
                Array.from(input.files).forEach((file, index) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewItem = document.createElement('div');
                        previewItem.className = 'preview-item';
                        previewItem.innerHTML = `
                            <img src="${e.target.result}" alt="Preview ${index + 1}">
                            <button type="button" class="preview-remove" onclick="removeImage(${index})">
                                <i class="fas fa-times"></i>
                            </button>
                        `;
                        previewContainer.appendChild(previewItem);
                    };
                    reader.readAsDataURL(file);
                });
            }
        }
        
        function removeImage(index) {
            const input = document.getElementById('project_images');
            const dt = new DataTransfer();
            
            Array.from(input.files).forEach((file, i) => {
                if (i !== index) {
                    dt.items.add(file);
                }
            });
            
            input.files = dt.files;
            previewImages(input);
        }
        
        // Drag and drop functionality
        const uploadArea = document.querySelector('.file-upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            const input = document.getElementById('project_images');
            input.files = files;
            previewImages(input);
        });
    </script>
</body>
</html>
