<?php
session_start();
require_once 'config/database.php';

echo "<h2>🔄 End-to-End Payment Flow Test</h2>";

try {
    // Get test data
    $stmt = $pdo->query("SELECT id FROM users WHERE user_type = 'customer' AND status = 'approved' LIMIT 1");
    $customer_id = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT id FROM quote_requests WHERE customer_id = ? LIMIT 1");
    $stmt->execute([$customer_id]);
    $quote_id = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("SELECT * FROM quote_responses WHERE quote_request_id = ? LIMIT 1");
    $stmt->execute([$quote_id]);
    $quote_response = $stmt->fetch();
    
    if (!$quote_response) {
        echo "<p style='color: red;'>No test data found. Please run fix_database_issues.php first.</p>";
        echo "<p><a href='fix_database_issues.php' style='background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Create Test Data</a></p>";
        exit;
    }
    
    echo "<h3>1. Test Data Found</h3>";
    echo "<p>Customer ID: $customer_id</p>";
    echo "<p>Quote Request ID: $quote_id</p>";
    echo "<p>Quote Response ID: " . $quote_response['id'] . "</p>";
    echo "<p>Quoted Amount: Rs. " . number_format($quote_response['quoted_amount'], 2) . "</p>";
    
    // Set up session for testing
    $_SESSION['user_id'] = $customer_id;
    $_SESSION['user_type'] = 'customer';
    
    // Set up payment session data
    $down_payment_amount = $quote_response['quoted_amount'] * 0.3; // 30% down payment
    $_SESSION['payment_data'] = [
        'quote_response_id' => $quote_response['id'],
        'amount' => $down_payment_amount,
        'payment_type' => 'down_payment',
        'payment_method' => 'card'
    ];
    
    echo "<h3>2. Payment Session Setup</h3>";
    echo "<p style='color: green;'>✓ User session configured</p>";
    echo "<p style='color: green;'>✓ Payment data session configured</p>";
    echo "<p>Down payment amount: Rs. " . number_format($down_payment_amount, 2) . "</p>";
    
    echo "<h3>3. Test Payment Portal Access</h3>";
    echo "<p><a href='customer/payment_portal.php' target='_blank' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🔗 Open Payment Portal</a></p>";
    
    echo "<h3>4. Simulate Payment Processing</h3>";
    echo "<form method='POST' action='customer/process_payment_portal.php' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>Test Payment Form</h4>";
    echo "<p>This form will simulate the payment processing with test data:</p>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label><strong>Cardholder Name:</strong></label><br>";
    echo "<input type='text' name='cardholder_name' value='Test Customer' style='width: 300px; padding: 8px; margin: 5px 0;'>";
    echo "</div>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label><strong>Card Number:</strong></label><br>";
    echo "<input type='text' name='card_number' value='1234 5678 9012 3456' style='width: 300px; padding: 8px; margin: 5px 0;'>";
    echo "</div>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label><strong>Expiry Date:</strong></label><br>";
    echo "<input type='text' name='expiry_date' value='12/25' style='width: 100px; padding: 8px; margin: 5px 0;'>";
    echo "</div>";
    
    echo "<div style='margin: 10px 0;'>";
    echo "<label><strong>CVV:</strong></label><br>";
    echo "<input type='text' name='cvv' value='123' style='width: 100px; padding: 8px; margin: 5px 0;'>";
    echo "</div>";
    
    echo "<input type='hidden' name='quote_response_id' value='" . $quote_response['id'] . "'>";
    echo "<input type='hidden' name='amount' value='$down_payment_amount'>";
    echo "<input type='hidden' name='payment_type' value='down_payment'>";
    echo "<input type='hidden' name='payment_method' value='card'>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<button type='submit' style='background: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; font-size: 16px; font-weight: bold; cursor: pointer;'>💳 Process Test Payment - Rs. " . number_format($down_payment_amount, 2) . "</button>";
    echo "</div>";
    echo "</form>";
    
    echo "<h3>5. Check Payment History</h3>";
    $stmt = $pdo->prepare("
        SELECT pp.*, cp.business_name, qr.title 
        FROM project_payments pp
        LEFT JOIN quote_responses qres ON pp.quote_response_id = qres.id
        LEFT JOIN quote_requests qr ON qres.quote_request_id = qr.id
        LEFT JOIN contractor_profiles cp ON qres.contractor_id = cp.user_id
        WHERE pp.customer_id = ?
        ORDER BY pp.created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$customer_id]);
    $payments = $stmt->fetchAll();
    
    if (!empty($payments)) {
        echo "<p>Found " . count($payments) . " payment(s) for this customer:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>ID</th><th style='padding: 8px;'>Project</th><th style='padding: 8px;'>Contractor</th><th style='padding: 8px;'>Amount</th><th style='padding: 8px;'>Status</th><th style='padding: 8px;'>Date</th><th style='padding: 8px;'>Action</th></tr>";
        foreach ($payments as $payment) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $payment['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($payment['title'] ?? 'N/A') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($payment['business_name'] ?? 'N/A') . "</td>";
            echo "<td style='padding: 8px;'>Rs. " . number_format($payment['amount'], 2) . "</td>";
            echo "<td style='padding: 8px;'><span style='background: " . ($payment['payment_status'] == 'completed' ? '#d4edda' : '#fff3cd') . "; padding: 4px 8px; border-radius: 4px;'>" . $payment['payment_status'] . "</span></td>";
            echo "<td style='padding: 8px;'>" . date('M j, Y H:i', strtotime($payment['created_at'])) . "</td>";
            echo "<td style='padding: 8px;'><a href='customer/payment_success.php?payment_id=" . $payment['id'] . "' target='_blank' style='color: #007bff; text-decoration: none;'>View</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No payments found for this customer yet.</p>";
    }
    
    echo "<h3>6. Quick Links</h3>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='customer/quote_responses.php?id=$quote_id' target='_blank' style='background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>📋 Quote Responses</a>";
    echo "<a href='test_login.php' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>🔐 Test Login</a>";
    echo "<a href='final_verification.php' style='background: #6f42c1; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>✅ System Check</a>";
    echo "</div>";
    
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d7ff; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4 style='margin-top: 0; color: #0056b3;'>💡 Testing Instructions</h4>";
    echo "<ol>";
    echo "<li><strong>Open Payment Portal:</strong> Click the blue button above to access the payment form</li>";
    echo "<li><strong>Fill Form:</strong> Use the pre-filled test data or enter your own</li>";
    echo "<li><strong>Submit Payment:</strong> Click 'Process Payment' - it should redirect to success page</li>";
    echo "<li><strong>Verify Success:</strong> Check that payment appears in the history table above</li>";
    echo "<li><strong>Test Success Page:</strong> Click 'View' link in payment history to see success page</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}
?>
